'use client';

const EarlyAdopterSection = () => {
  return (
    <section className="py-20 bg-gradient-to-b from-midnight-blue to-deep-purple">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-6">
            <span className="bg-gradient-to-r from-hologram-pink to-quantum-gold bg-clip-text text-transparent">
              Early Adopter CTA
            </span>
          </h2>
          <p className="text-xl text-text-secondary">
            Fireflies background and early adopter loop coming soon...
          </p>
        </div>
      </div>
    </section>
  );
};

export default EarlyAdopterSection;
