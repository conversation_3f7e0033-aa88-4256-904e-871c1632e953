{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/effects/TypingText.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst TypingText = ({\n  texts = ['Your Name. Reinvented.'],\n  speed = 100,\n  deleteSpeed = 50,\n  pauseTime = 2000,\n  loop = true,\n  className = '',\n  cursorClassName = '',\n  showCursor = true,\n  onComplete = null\n}) => {\n  const [currentTextIndex, setCurrentTextIndex] = useState(0);\n  const [currentText, setCurrentText] = useState('');\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [isPaused, setIsPaused] = useState(false);\n\n  useEffect(() => {\n    if (texts.length === 0) return;\n\n    const currentFullText = texts[currentTextIndex];\n    \n    const timeout = setTimeout(() => {\n      if (isPaused) {\n        setIsPaused(false);\n        if (loop || currentTextIndex < texts.length - 1) {\n          setIsDeleting(true);\n        }\n        return;\n      }\n\n      if (isDeleting) {\n        // Deleting characters\n        if (currentText.length > 0) {\n          setCurrentText(currentText.slice(0, -1));\n        } else {\n          // Finished deleting, move to next text\n          setIsDeleting(false);\n          setCurrentTextIndex((prev) => (prev + 1) % texts.length);\n        }\n      } else {\n        // Typing characters\n        if (currentText.length < currentFullText.length) {\n          setCurrentText(currentFullText.slice(0, currentText.length + 1));\n        } else {\n          // Finished typing current text\n          if (texts.length === 1 && !loop) {\n            // Single text, no loop - we're done\n            if (onComplete) onComplete();\n            return;\n          }\n          // Pause before deleting (if looping) or moving to next\n          setIsPaused(true);\n        }\n      }\n    }, isPaused ? pauseTime : isDeleting ? deleteSpeed : speed);\n\n    return () => clearTimeout(timeout);\n  }, [\n    currentText, \n    currentTextIndex, \n    isDeleting, \n    isPaused, \n    texts, \n    speed, \n    deleteSpeed, \n    pauseTime, \n    loop, \n    onComplete\n  ]);\n\n  const Cursor = () => (\n    <span \n      className={cn(\n        'inline-block w-0.5 bg-neon-blue animate-pulse ml-1',\n        cursorClassName\n      )}\n      style={{ \n        animation: 'blink 1s infinite',\n        height: '1em'\n      }}\n    />\n  );\n\n  return (\n    <span className={cn('inline-block', className)}>\n      {currentText}\n      {showCursor && <Cursor />}\n      \n      <style jsx>{`\n        @keyframes blink {\n          0%, 50% { opacity: 1; }\n          51%, 100% { opacity: 0; }\n        }\n      `}</style>\n    </span>\n  );\n};\n\n// Preset configurations for common use cases\nexport const TypingTextPresets = {\n  hero: {\n    speed: 80,\n    deleteSpeed: 40,\n    pauseTime: 3000,\n    className: 'text-4xl md:text-6xl font-bold bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent',\n    cursorClassName: 'bg-neon-blue h-12 md:h-16'\n  },\n  \n  subtitle: {\n    speed: 60,\n    deleteSpeed: 30,\n    pauseTime: 2000,\n    className: 'text-xl md:text-2xl text-text-secondary',\n    cursorClassName: 'bg-electric-purple h-6 md:h-8'\n  },\n  \n  feature: {\n    speed: 50,\n    deleteSpeed: 25,\n    pauseTime: 1500,\n    className: 'text-lg text-text-primary',\n    cursorClassName: 'bg-cyber-green h-5'\n  },\n  \n  code: {\n    speed: 30,\n    deleteSpeed: 15,\n    pauseTime: 1000,\n    className: 'font-mono text-matrix-green',\n    cursorClassName: 'bg-matrix-green h-4'\n  }\n};\n\n// Enhanced typing text with preset support\nexport const EnhancedTypingText = ({ preset, ...props }) => {\n  const presetConfig = preset ? TypingTextPresets[preset] : {};\n  const mergedProps = { ...presetConfig, ...props };\n  \n  return <TypingText {...mergedProps} />;\n};\n\nexport default TypingText;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;AAHA;;;;AAKA,MAAM,aAAa,CAAC,EAClB,QAAQ;IAAC;CAAyB,EAClC,QAAQ,GAAG,EACX,cAAc,EAAE,EAChB,YAAY,IAAI,EAChB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,aAAa,IAAI,EACjB,aAAa,IAAI,EAClB;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,MAAM,MAAM,KAAK,GAAG;YAExB,MAAM,kBAAkB,KAAK,CAAC,iBAAiB;YAE/C,MAAM,UAAU;gDAAW;oBACzB,IAAI,UAAU;wBACZ,YAAY;wBACZ,IAAI,QAAQ,mBAAmB,MAAM,MAAM,GAAG,GAAG;4BAC/C,cAAc;wBAChB;wBACA;oBACF;oBAEA,IAAI,YAAY;wBACd,sBAAsB;wBACtB,IAAI,YAAY,MAAM,GAAG,GAAG;4BAC1B,eAAe,YAAY,KAAK,CAAC,GAAG,CAAC;wBACvC,OAAO;4BACL,uCAAuC;4BACvC,cAAc;4BACd;gEAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;wBACzD;oBACF,OAAO;wBACL,oBAAoB;wBACpB,IAAI,YAAY,MAAM,GAAG,gBAAgB,MAAM,EAAE;4BAC/C,eAAe,gBAAgB,KAAK,CAAC,GAAG,YAAY,MAAM,GAAG;wBAC/D,OAAO;4BACL,+BAA+B;4BAC/B,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM;gCAC/B,oCAAoC;gCACpC,IAAI,YAAY;gCAChB;4BACF;4BACA,uDAAuD;4BACvD,YAAY;wBACd;oBACF;gBACF;+CAAG,WAAW,YAAY,aAAa,cAAc;YAErD;wCAAO,IAAM,aAAa;;QAC5B;+BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,SAAS,kBACb,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;YAEF,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;;;;;;IAIJ,qBACE,6LAAC;mDAAgB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;;YACjC;YACA,4BAAc,6LAAC;;;;;;;;;;;;;;;;;AAUtB;GAhGM;KAAA;AAmGC,MAAM,oBAAoB;IAC/B,MAAM;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,UAAU;QACR,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,SAAS;QACP,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,MAAM;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;AACF;AAGO,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO;IACrD,MAAM,eAAe,SAAS,iBAAiB,CAAC,OAAO,GAAG,CAAC;IAC3D,MAAM,cAAc;QAAE,GAAG,YAAY;QAAE,GAAG,KAAK;IAAC;IAEhD,qBAAO,6LAAC;QAAY,GAAG,WAAW;;;;;;AACpC;MALa;uCAOE", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/effects/MatrixEffect.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\nconst MatrixEffect = ({ \n  className = '',\n  density = 0.8,\n  speed = 50,\n  color = '#00ff41',\n  fontSize = 14,\n  opacity = 0.8\n}) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    \n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = canvas.offsetWidth;\n      canvas.height = canvas.offsetHeight;\n    };\n    \n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Matrix characters (mix of katakana, numbers, and symbols)\n    const chars = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+-=[]{}|;:,.<>?';\n    const charArray = chars.split('');\n\n    // Calculate columns\n    const columns = Math.floor(canvas.width / fontSize);\n    const drops = new Array(columns).fill(1);\n\n    // Animation function\n    const animate = () => {\n      // Semi-transparent black background for trail effect\n      ctx.fillStyle = `rgba(10, 10, 15, ${1 - opacity})`;\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Set text properties\n      ctx.fillStyle = color;\n      ctx.font = `${fontSize}px 'JetBrains Mono', monospace`;\n      ctx.textAlign = 'center';\n\n      // Draw characters\n      for (let i = 0; i < drops.length; i++) {\n        // Random character\n        const char = charArray[Math.floor(Math.random() * charArray.length)];\n        \n        // Calculate position\n        const x = i * fontSize + fontSize / 2;\n        const y = drops[i] * fontSize;\n\n        // Add glow effect\n        ctx.shadowColor = color;\n        ctx.shadowBlur = 10;\n        \n        // Draw character\n        ctx.fillText(char, x, y);\n        \n        // Reset shadow\n        ctx.shadowBlur = 0;\n\n        // Reset drop to top randomly or when it reaches bottom\n        if (y > canvas.height && Math.random() > density) {\n          drops[i] = 0;\n        }\n\n        // Move drop down\n        drops[i]++;\n      }\n\n      // Continue animation\n      animationRef.current = setTimeout(animate, speed);\n    };\n\n    // Start animation\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        clearTimeout(animationRef.current);\n      }\n    };\n  }, [density, speed, color, fontSize, opacity]);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className={`absolute inset-0 w-full h-full pointer-events-none ${className}`}\n      style={{ zIndex: -1 }}\n    />\n  );\n};\n\nexport default MatrixEffect;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,eAAe,CAAC,EACpB,YAAY,EAAE,EACd,UAAU,GAAG,EACb,QAAQ,EAAE,EACV,QAAQ,SAAS,EACjB,WAAW,EAAE,EACb,UAAU,GAAG,EACd;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAE9B,kBAAkB;YAClB,MAAM;uDAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,WAAW;oBACjC,OAAO,MAAM,GAAG,OAAO,YAAY;gBACrC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC,4DAA4D;YAC5D,MAAM,QAAQ;YACd,MAAM,YAAY,MAAM,KAAK,CAAC;YAE9B,oBAAoB;YACpB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG;YAC1C,MAAM,QAAQ,IAAI,MAAM,SAAS,IAAI,CAAC;YAEtC,qBAAqB;YACrB,MAAM;kDAAU;oBACd,qDAAqD;oBACrD,IAAI,SAAS,GAAG,CAAC,iBAAiB,EAAE,IAAI,QAAQ,CAAC,CAAC;oBAClD,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE9C,sBAAsB;oBACtB,IAAI,SAAS,GAAG;oBAChB,IAAI,IAAI,GAAG,GAAG,SAAS,8BAA8B,CAAC;oBACtD,IAAI,SAAS,GAAG;oBAEhB,kBAAkB;oBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;wBACrC,mBAAmB;wBACnB,MAAM,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;wBAEpE,qBAAqB;wBACrB,MAAM,IAAI,IAAI,WAAW,WAAW;wBACpC,MAAM,IAAI,KAAK,CAAC,EAAE,GAAG;wBAErB,kBAAkB;wBAClB,IAAI,WAAW,GAAG;wBAClB,IAAI,UAAU,GAAG;wBAEjB,iBAAiB;wBACjB,IAAI,QAAQ,CAAC,MAAM,GAAG;wBAEtB,eAAe;wBACf,IAAI,UAAU,GAAG;wBAEjB,uDAAuD;wBACvD,IAAI,IAAI,OAAO,MAAM,IAAI,KAAK,MAAM,KAAK,SAAS;4BAChD,KAAK,CAAC,EAAE,GAAG;wBACb;wBAEA,iBAAiB;wBACjB,KAAK,CAAC,EAAE;oBACV;oBAEA,qBAAqB;oBACrB,aAAa,OAAO,GAAG,WAAW,SAAS;gBAC7C;;YAEA,kBAAkB;YAClB;YAEA,UAAU;YACV;0CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,aAAa,OAAO,EAAE;wBACxB,aAAa,aAAa,OAAO;oBACnC;gBACF;;QACF;iCAAG;QAAC;QAAS;QAAO;QAAO;QAAU;KAAQ;IAE7C,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,mDAAmD,EAAE,WAAW;QAC5E,OAAO;YAAE,QAAQ,CAAC;QAAE;;;;;;AAG1B;GAhGM;KAAA;uCAkGS", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport Button from '@/components/ui/Button';\nimport { EnhancedTypingText } from '@/components/effects/TypingText';\nimport MatrixEffect from '@/components/effects/MatrixEffect';\n\nconst HeroSection = () => {\n  const heroRef = useRef(null);\n  const floatingElementsRef = useRef([]);\n\n  // Floating elements animation\n  useEffect(() => {\n    const elements = floatingElementsRef.current;\n    \n    elements.forEach((element, index) => {\n      if (element) {\n        const delay = index * 0.5;\n        const duration = 3 + Math.random() * 2;\n        \n        element.style.animationDelay = `${delay}s`;\n        element.style.animationDuration = `${duration}s`;\n      }\n    });\n  }, []);\n\n  const heroTexts = [\n    \"Your Name. Reinvented.\",\n    \"Not Just a Card—An Experience.\",\n    \"Connect in 3D. Remember Forever.\",\n    \"The Future of Networking Is Here.\"\n  ];\n\n  const FloatingCard = ({ index, className = '' }) => (\n    <div\n      ref={el => floatingElementsRef.current[index] = el}\n      className={`absolute opacity-20 animate-float ${className}`}\n      style={{\n        animationName: 'float',\n        animationTimingFunction: 'ease-in-out',\n        animationIterationCount: 'infinite',\n        animationDirection: 'alternate'\n      }}\n    >\n      <div className=\"w-16 h-10 bg-gradient-to-r from-neon-blue to-electric-purple rounded-lg shadow-lg shadow-neon-blue/25 transform rotate-12\" />\n    </div>\n  );\n\n  const StatsCard = ({ number, label, delay = 0 }) => (\n    <div \n      className=\"text-center group\"\n      style={{ animationDelay: `${delay}s` }}\n    >\n      <div className=\"text-2xl md:text-3xl font-bold font-display text-neon-blue mb-1 group-hover:scale-110 transition-transform duration-300\">\n        {number}\n      </div>\n      <div className=\"text-sm text-text-secondary\">{label}</div>\n    </div>\n  );\n\n  return (\n    <section \n      ref={heroRef}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-space via-deep-purple to-midnight-blue\"\n    >\n      {/* Matrix Background Effect */}\n      <MatrixEffect \n        className=\"absolute inset-0\" \n        density={0.6}\n        speed={80}\n        opacity={0.3}\n      />\n      \n      {/* Floating 3D Elements */}\n      <FloatingCard index={0} className=\"top-20 left-10\" />\n      <FloatingCard index={1} className=\"top-40 right-20\" />\n      <FloatingCard index={2} className=\"bottom-40 left-20\" />\n      <FloatingCard index={3} className=\"bottom-20 right-10\" />\n      <FloatingCard index={4} className=\"top-60 left-1/3\" />\n      <FloatingCard index={5} className=\"bottom-60 right-1/3\" />\n\n      {/* Main Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"space-y-8\">\n          {/* Main Headline */}\n          <div className=\"space-y-4\">\n            <h1 className=\"text-5xl md:text-7xl lg:text-8xl font-bold font-display leading-tight\">\n              <EnhancedTypingText\n                preset=\"hero\"\n                texts={heroTexts}\n                speed={100}\n                deleteSpeed={50}\n                pauseTime={3000}\n                loop={true}\n              />\n            </h1>\n            \n            <p className=\"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed\">\n              AR-enhanced digital business cards that let you share stunning, interactive profiles via \n              <span className=\"text-neon-blue font-semibold\"> QR, NFC, facial recognition, or camera scan</span>\n              —no app required.\n            </p>\n          </div>\n\n          {/* Mini Demo Preview */}\n          <div className=\"relative max-w-md mx-auto\">\n            <div className=\"bg-surface-dark/80 backdrop-blur-md rounded-2xl p-6 border border-electric-purple/30 hover:border-neon-blue/50 transition-all duration-300 group\">\n              <div className=\"relative\">\n                {/* Simulated 3D Card */}\n                <div className=\"w-full h-32 bg-gradient-to-br from-neon-blue via-electric-purple to-hologram-pink rounded-xl shadow-2xl shadow-neon-blue/30 transform group-hover:scale-105 group-hover:rotate-3 transition-all duration-500\">\n                  <div className=\"absolute inset-0 bg-white/10 rounded-xl backdrop-blur-sm\">\n                    <div className=\"p-4 h-full flex flex-col justify-between\">\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-8 h-8 bg-white/20 rounded-full\"></div>\n                        <div>\n                          <div className=\"w-16 h-2 bg-white/30 rounded mb-1\"></div>\n                          <div className=\"w-12 h-1.5 bg-white/20 rounded\"></div>\n                        </div>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <div className=\"w-20 h-1.5 bg-white/30 rounded\"></div>\n                        <div className=\"w-16 h-1.5 bg-white/20 rounded\"></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Floating particles */}\n                <div className=\"absolute -top-2 -right-2 w-2 h-2 bg-neon-blue rounded-full animate-ping\"></div>\n                <div className=\"absolute -bottom-2 -left-2 w-1.5 h-1.5 bg-electric-purple rounded-full animate-pulse\"></div>\n              </div>\n              \n              <p className=\"text-sm text-text-secondary mt-4 group-hover:text-neon-blue transition-colors duration-300\">\n                ✨ Live 3D Card Preview\n              </p>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Link href=\"/demo\">\n              <Button \n                variant=\"primary\" \n                size=\"lg\"\n                className=\"text-lg font-semibold px-8 py-4 shadow-2xl shadow-neon-blue/30 hover:shadow-electric-purple/40\"\n              >\n                Try Live Demo\n              </Button>\n            </Link>\n            \n            <Link href=\"/pitch\">\n              <Button \n                variant=\"outline\" \n                size=\"lg\"\n                className=\"text-lg font-semibold px-8 py-4\"\n              >\n                Watch Pitch\n              </Button>\n            </Link>\n          </div>\n\n          {/* Stats Row */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto pt-12 border-t border-electric-purple/20\">\n            <StatsCard number=\"88%\" label=\"Cards Thrown Away\" delay={0.2} />\n            <StatsCard number=\"7B+\" label=\"Cards Printed Yearly\" delay={0.4} />\n            <StatsCard number=\"70%\" label=\"Better Retention\" delay={0.6} />\n            <StatsCard number=\"0\" label=\"App Downloads\" delay={0.8} />\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-neon-blue rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-neon-blue rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n\n      {/* CSS for floating animation */}\n      <style jsx>{`\n        @keyframes float {\n          0% { transform: translateY(0px) rotate(12deg); }\n          100% { transform: translateY(-20px) rotate(15deg); }\n        }\n        \n        .animate-float {\n          animation: float 3s ease-in-out infinite alternate;\n        }\n      `}</style>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAQA,MAAM,cAAc;;IAClB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAErC,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW,oBAAoB,OAAO;YAE5C,SAAS,OAAO;yCAAC,CAAC,SAAS;oBACzB,IAAI,SAAS;wBACX,MAAM,QAAQ,QAAQ;wBACtB,MAAM,WAAW,IAAI,KAAK,MAAM,KAAK;wBAErC,QAAQ,KAAK,CAAC,cAAc,GAAG,GAAG,MAAM,CAAC,CAAC;wBAC1C,QAAQ,KAAK,CAAC,iBAAiB,GAAG,GAAG,SAAS,CAAC,CAAC;oBAClD;gBACF;;QACF;gCAAG,EAAE;IAEL,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE,iBAC7C,6LAAC;YACC,KAAK,CAAA,KAAM,oBAAoB,OAAO,CAAC,MAAM,GAAG;YAChD,WAAW,CAAC,kCAAkC,EAAE,WAAW;YAC3D,OAAO;gBACL,eAAe;gBACf,yBAAyB;gBACzB,yBAAyB;gBACzB,oBAAoB;YACtB;sBAEA,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAInB,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,iBAC7C,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE,gBAAgB,GAAG,MAAM,CAAC,CAAC;YAAC;;8BAErC,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,6LAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;IAIlD,qBACE,6LAAC;QACC,KAAK;kDACK;;0BAGV,6LAAC,+IAAA,CAAA,UAAY;gBACX,WAAU;gBACV,SAAS;gBACT,OAAO;gBACP,SAAS;;;;;;0BAIX,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAGlC,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAc;;sCAEb,6LAAC;sEAAc;;8CACb,6LAAC;8EAAa;8CACZ,cAAA,6LAAC,6IAAA,CAAA,qBAAkB;wCACjB,QAAO;wCACP,OAAO;wCACP,OAAO;wCACP,aAAa;wCACb,WAAW;wCACX,MAAM;;;;;;;;;;;8CAIV,6LAAC;8EAAY;;wCAA4E;sDAEvF,6LAAC;sFAAe;sDAA+B;;;;;;wCAAmD;;;;;;;;;;;;;sCAMtG,6LAAC;sEAAc;sCACb,cAAA,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;;0DAEb,6LAAC;0FAAc;0DACb,cAAA,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAc;;0EACb,6LAAC;0GAAc;;kFACb,6LAAC;kHAAc;;;;;;kFACf,6LAAC;;;0FACC,6LAAC;0HAAc;;;;;;0FACf,6LAAC;0HAAc;;;;;;;;;;;;;;;;;;0EAGnB,6LAAC;0GAAc;;kFACb,6LAAC;kHAAc;;;;;;kFACf,6LAAC;kHAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOvB,6LAAC;0FAAc;;;;;;0DACf,6LAAC;0FAAc;;;;;;;;;;;;kDAGjB,6LAAC;kFAAY;kDAA6F;;;;;;;;;;;;;;;;;sCAO9G,6LAAC;sEAAc;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,oIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAKH,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,oIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,6LAAC;sEAAc;;8CACb,6LAAC;oCAAU,QAAO;oCAAM,OAAM;oCAAoB,OAAO;;;;;;;8CACzD,6LAAC;oCAAU,QAAO;oCAAM,OAAM;oCAAuB,OAAO;;;;;;;8CAC5D,6LAAC;oCAAU,QAAO;oCAAM,OAAM;oCAAmB,OAAO;;;;;;;8CACxD,6LAAC;oCAAU,QAAO;oCAAI,OAAM;oCAAgB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAc;8BACb,cAAA,6LAAC;kEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBzB;GAxLM;KAAA;uCA0LS", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/ui/Card.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = forwardRef(({ \n  className, \n  variant = 'default',\n  hover = true,\n  glow = false,\n  children, \n  ...props \n}, ref) => {\n  const baseStyles = `\n    relative rounded-xl border backdrop-blur-sm\n    transition-all duration-300 ease-out\n    overflow-hidden group\n  `;\n\n  const variants = {\n    default: `\n      bg-surface-dark/80 border-electric-purple/20\n      hover:border-neon-blue/40 hover:bg-surface-light/80\n    `,\n    glass: `\n      bg-white/5 border-white/10\n      hover:bg-white/10 hover:border-white/20\n      backdrop-blur-md\n    `,\n    neon: `\n      bg-dark-space/90 border-neon-blue/50\n      hover:border-neon-blue hover:bg-dark-space\n      shadow-lg shadow-neon-blue/10\n    `,\n    purple: `\n      bg-deep-purple/80 border-electric-purple/30\n      hover:border-electric-purple hover:bg-deep-purple\n      shadow-lg shadow-electric-purple/10\n    `,\n    gradient: `\n      bg-gradient-to-br from-surface-dark/80 to-deep-purple/80\n      border-gradient-to-r from-neon-blue/30 to-electric-purple/30\n      hover:from-surface-light/80 hover:to-midnight-blue/80\n    `\n  };\n\n  const hoverEffects = hover ? `\n    hover:scale-[1.02] hover:-translate-y-1\n    hover:shadow-2xl hover:shadow-neon-blue/20\n  ` : '';\n\n  const glowEffect = glow ? `\n    before:absolute before:inset-0 before:rounded-xl\n    before:bg-gradient-to-r before:from-neon-blue/20 before:to-electric-purple/20\n    before:opacity-0 before:transition-opacity before:duration-300\n    hover:before:opacity-100\n  ` : '';\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        baseStyles,\n        variants[variant],\n        hoverEffects,\n        glowEffect,\n        className\n      )}\n      {...props}\n    >\n      {/* Animated border gradient */}\n      <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-neon-blue via-electric-purple to-hologram-pink opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n      \n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n      \n      {/* Corner accent */}\n      <div className=\"absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-neon-blue/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n    </div>\n  );\n});\n\nCard.displayName = 'Card';\n\n// Card Header Component\nconst CardHeader = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('p-6 pb-4', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardHeader.displayName = 'CardHeader';\n\n// Card Title Component\nconst CardTitle = forwardRef(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-xl font-semibold text-text-primary mb-2',\n      'bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </h3>\n));\n\nCardTitle.displayName = 'CardTitle';\n\n// Card Description Component\nconst CardDescription = forwardRef(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-text-secondary leading-relaxed', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\n\nCardDescription.displayName = 'CardDescription';\n\n// Card Content Component\nconst CardContent = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('px-6 pb-6', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardContent.displayName = 'CardContent';\n\n// Card Footer Component\nconst CardFooter = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'px-6 py-4 border-t border-electric-purple/20',\n      'bg-gradient-to-r from-transparent via-electric-purple/5 to-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardFooter.displayName = 'CardFooter';\n\n// Export all components\nexport {\n  Card,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n  CardContent,\n  CardFooter\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACvB,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAC;;;;EAIpB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;IAGV,CAAC;QACD,OAAO,CAAC;;;;IAIR,CAAC;QACD,MAAM,CAAC;;;;IAIP,CAAC;QACD,QAAQ,CAAC;;;;IAIT,CAAC;QACD,UAAU,CAAC;;;;IAIX,CAAC;IACH;IAEA,MAAM,eAAe,QAAQ,CAAC;;;EAG9B,CAAC,GAAG;IAEJ,MAAM,aAAa,OAAO,CAAC;;;;;EAK3B,CAAC,GAAG;IAEJ,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,cACA,YACA;QAED,GAAG,KAAK;;0BAGT,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;;AAEA,KAAK,WAAW,GAAG;AAEnB,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG;AAEzB,uBAAuB;AACvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIL,UAAU,WAAW,GAAG;AAExB,6BAA6B;AAC7B,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACrE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;QACpD,GAAG,KAAK;kBAER;;;;;;;AAIL,gBAAgB,WAAW,GAAG;AAE9B,yBAAyB;AACzB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACjE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;kBAER;;;;;;;AAIL,YAAY,WAAW,GAAG;AAE1B,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,0EACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/ProblemSection.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect } from 'react';\nimport { Card, CardContent } from '@/components/ui/Card';\n\nconst ProblemSection = () => {\n  const sectionRef = useRef(null);\n  const cardsRef = useRef([]);\n\n  // 3D Tilt Effect\n  useEffect(() => {\n    const handleMouseMove = (e, card) => {\n      if (!card) return;\n      \n      const rect = card.getBoundingClientRect();\n      const x = e.clientX - rect.left;\n      const y = e.clientY - rect.top;\n      \n      const centerX = rect.width / 2;\n      const centerY = rect.height / 2;\n      \n      const rotateX = (y - centerY) / 10;\n      const rotateY = (centerX - x) / 10;\n      \n      card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;\n    };\n\n    const handleMouseLeave = (card) => {\n      if (!card) return;\n      card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';\n    };\n\n    cardsRef.current.forEach((card) => {\n      if (card) {\n        const mouseMoveHandler = (e) => handleMouseMove(e, card);\n        const mouseLeaveHandler = () => handleMouseLeave(card);\n        \n        card.addEventListener('mousemove', mouseMoveHandler);\n        card.addEventListener('mouseleave', mouseLeaveHandler);\n        \n        return () => {\n          card.removeEventListener('mousemove', mouseMoveHandler);\n          card.removeEventListener('mouseleave', mouseLeaveHandler);\n        };\n      }\n    });\n  }, []);\n\n  const problems = [\n    {\n      icon: '🗑️',\n      title: 'Paper Cards Get Lost',\n      description: '88% of business cards are thrown away within a week. Your investment literally goes in the trash.',\n      stat: '7B+ cards wasted yearly'\n    },\n    {\n      icon: '📱',\n      title: 'Tech Dependency',\n      description: 'QR codes need cameras, NFC needs compatible devices. What happens when tech fails?',\n      stat: '30% compatibility issues'\n    },\n    {\n      icon: '😴',\n      title: 'Boring & Forgettable',\n      description: 'Static cards blend into the noise. No engagement, no story, no lasting impression.',\n      stat: '95% forgotten instantly'\n    },\n    {\n      icon: '🔄',\n      title: 'Manual Effort Required',\n      description: 'Typing contact info, updating details, managing connections. Too much friction.',\n      stat: '5 minutes per contact'\n    }\n  ];\n\n  return (\n    <section \n      ref={sectionRef}\n      className=\"py-20 bg-gradient-to-b from-midnight-blue to-deep-purple relative overflow-hidden\"\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(0,245,255,0.1),transparent_50%)]\"></div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-hologram-pink to-quantum-gold bg-clip-text text-transparent\">\n              The Problem with Traditional Networking\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n            In a digital world, we're still stuck with analog solutions that waste time, money, and opportunities.\n          </p>\n        </div>\n\n        {/* Problem Cards Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {problems.map((problem, index) => (\n            <Card\n              key={index}\n              ref={el => cardsRef.current[index] = el}\n              variant=\"glass\"\n              className=\"h-full transition-all duration-300 cursor-pointer transform-gpu\"\n              style={{ transformStyle: 'preserve-3d' }}\n            >\n              <CardContent className=\"p-6 h-full flex flex-col\">\n                {/* Icon */}\n                <div className=\"text-4xl mb-4 text-center\">{problem.icon}</div>\n                \n                {/* Title */}\n                <h3 className=\"text-xl font-semibold text-text-primary mb-3 text-center\">\n                  {problem.title}\n                </h3>\n                \n                {/* Description */}\n                <p className=\"text-text-secondary text-center mb-4 flex-grow\">\n                  {problem.description}\n                </p>\n                \n                {/* Stat */}\n                <div className=\"text-center\">\n                  <span className=\"inline-block px-3 py-1 bg-hologram-pink/20 text-hologram-pink rounded-full text-sm font-medium\">\n                    {problem.stat}\n                  </span>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-lg text-text-secondary mb-6\">\n            It's time for a solution that works in the real world.\n          </p>\n          <div className=\"w-24 h-1 bg-gradient-to-r from-hologram-pink to-quantum-gold mx-auto rounded-full\"></div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ProblemSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,iBAAiB;;IACrB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE1B,iBAAiB;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;4DAAkB,CAAC,GAAG;oBAC1B,IAAI,CAAC,MAAM;oBAEX,MAAM,OAAO,KAAK,qBAAqB;oBACvC,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;oBAC/B,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG;oBAE9B,MAAM,UAAU,KAAK,KAAK,GAAG;oBAC7B,MAAM,UAAU,KAAK,MAAM,GAAG;oBAE9B,MAAM,UAAU,CAAC,IAAI,OAAO,IAAI;oBAChC,MAAM,UAAU,CAAC,UAAU,CAAC,IAAI;oBAEhC,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,4BAA4B,EAAE,QAAQ,aAAa,EAAE,QAAQ,8BAA8B,CAAC;gBACtH;;YAEA,MAAM;6DAAmB,CAAC;oBACxB,IAAI,CAAC,MAAM;oBACX,KAAK,KAAK,CAAC,SAAS,GAAG;gBACzB;;YAEA,SAAS,OAAO,CAAC,OAAO;4CAAC,CAAC;oBACxB,IAAI,MAAM;wBACR,MAAM;yEAAmB,CAAC,IAAM,gBAAgB,GAAG;;wBACnD,MAAM;0EAAoB,IAAM,iBAAiB;;wBAEjD,KAAK,gBAAgB,CAAC,aAAa;wBACnC,KAAK,gBAAgB,CAAC,cAAc;wBAEpC;wDAAO;gCACL,KAAK,mBAAmB,CAAC,aAAa;gCACtC,KAAK,mBAAmB,CAAC,cAAc;4BACzC;;oBACF;gBACF;;QACF;mCAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAK,WAAU;8CAAoF;;;;;;;;;;;0CAItG,6LAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAM/D,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,kIAAA,CAAA,OAAI;gCAEH,KAAK,CAAA,KAAM,SAAS,OAAO,CAAC,MAAM,GAAG;gCACrC,SAAQ;gCACR,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAc;0CAEvC,cAAA,6LAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,6LAAC;4CAAI,WAAU;sDAA6B,QAAQ,IAAI;;;;;;sDAGxD,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAIhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAItB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,QAAQ,IAAI;;;;;;;;;;;;;;;;;+BAvBd;;;;;;;;;;kCAgCX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAGhD,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzB;GA1IM;KAAA;uCA4IS", "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/node_modules/styled-jsx/dist/index/index.js"], "sourcesContent": ["require('client-only');\nvar React = require('react');\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && process.env.NODE_ENV === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\n\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\n\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\n\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        })// Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        })// filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\nfunction useStyleRegistry() {\n    return React.useContext(StyleSheetContext);\n}\n\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\n\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\n"], "names": [], "mappings": "AAwBoB;;AAvBpB,IAAI;AAEJ,SAAS,sBAAuB,CAAC;IAAI,OAAO,KAAK,OAAO,MAAM,YAAY,aAAa,IAAI,IAAI;QAAE,WAAW;IAAE;AAAG;AAEjH,IAAI,iBAAiB,WAAW,GAAE,sBAAsB;AAExD;;;AAGA,GAAG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IACvC,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI;QACjC,IAAI,aAAa,KAAK,CAAC,EAAE;QACzB,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QACjD,WAAW,YAAY,GAAG;QAC1B,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QACjD,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAClD;AACJ;AACA,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IACtD,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IACzD,IAAI,aAAa,kBAAkB,aAAa;IAChD,OAAO;AACX;AACA,IAAI,SAAS,OAAO,gKAAA,CAAA,UAAO,KAAK,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,IAAI,oDAAyB;AACvF,IAAI,WAAW,SAAS,CAAC;IACrB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;AACjD;AACA,IAAI,aAAa,WAAW,GAAG;IAC3B,SAAS,WAAW,KAAK;QACrB,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,IAAI,eAAe,OAAO,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,SAAS;QAChN,YAAY,SAAS,OAAO;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,uBAAuB,GAAG,MAAM,OAAO;QAC5C,YAAY,OAAO,qBAAqB,WAAW;QACnD,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,OAAO,WAAW,eAAe,SAAS,aAAa,CAAC;QACnE,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,YAAY,CAAC,aAAa;IACxD;IACA,IAAI,SAAS,WAAW,SAAS;IACjC,OAAO,mBAAmB,GAAG,SAAS,oBAAoB,IAAI;QAC1D,YAAY,OAAO,SAAS,WAAW;QACvC,YAAY,IAAI,CAAC,WAAW,KAAK,GAAG;QACpC,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,MAAM;IACf;IACA,OAAO,kBAAkB,GAAG,SAAS;QACjC,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,OAAO,MAAM,GAAG,SAAS;QACrB,IAAI,QAAQ,IAAI;QAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,OAAO,WAAW,eAAe,IAAI,CAAC,iBAAiB,EAAE;YACzD,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK;YAC5C,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,IAAI,CAAC,QAAQ;YACtD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACzB,wCAAa;oBACT,QAAQ,IAAI,CAAC;gBACjB;gBACA,IAAI,CAAC,KAAK;gBACV,IAAI,CAAC,SAAS,GAAG;YACrB;YACA;QACJ;QACA,IAAI,CAAC,YAAY,GAAG;YAChB,UAAU,EAAE;YACZ,YAAY,SAAS,IAAI,EAAE,KAAK;gBAC5B,IAAI,OAAO,UAAU,UAAU;oBAC3B,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;wBACjC,SAAS;oBACb;gBACJ,OAAO;oBACH,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC7B,SAAS;oBACb;gBACJ;gBACA,OAAO;YACX;YACA,YAAY,SAAS,KAAK;gBACtB,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;YACzC;QACJ;IACJ;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,GAAG;QAC/C,IAAI,IAAI,KAAK,EAAE;YACX,OAAO,IAAI,KAAK;QACpB;QACA,2CAA2C;QAC3C,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,IAAI;YAChD,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC,SAAS,KAAK,KAAK;gBAC3C,OAAO,SAAS,WAAW,CAAC,EAAE;YAClC;QACJ;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAChE;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI,EAAE,KAAK;QAC/C,YAAY,SAAS,OAAO;QAC5B,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM;YAC7C;YACA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,QAAQ,IAAI,CAAC,QAAQ;YACzB,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,MAAM,QAAQ,CAAC,MAAM;YACjC;YACA,kDAAkD;YAClD,4FAA4F;YAC5F,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,OAAO,CAAC;YACZ;QACJ,OAAO;YACH,IAAI,iBAAiB,IAAI,CAAC,KAAK,CAAC,MAAM;YACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM;QACxD;QACA,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,KAAK,EAAE,IAAI;QACjD,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,WAAW,aAAa;YACzD,IAAI,QAAQ,OAAO,WAAW,cAAc,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;YAC/E,IAAI,CAAC,KAAK,IAAI,IAAI;gBACd,OAAO,IAAI,CAAC,uBAAuB;YACvC;YACA,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,EAAE;gBACxB,iCAAiC;gBACjC,OAAO;YACX;YACA,MAAM,UAAU,CAAC;YACjB,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,qEAAqE;gBACrE,MAAM,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACnD;QACJ,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,wBAAwB,QAAQ;YACjD,IAAI,WAAW,GAAG;QACtB;QACA,OAAO;IACX;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,KAAK;QACzC,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YAC7B;QACJ;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,WAAW,CAAC,OAAO;QAC5B,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,oBAAoB,QAAQ;YAC7C,IAAI,UAAU,CAAC,WAAW,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACxB;IACJ;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,GAAG;gBAC3B,OAAO,OAAO,IAAI,UAAU,CAAC,WAAW,CAAC;YAC7C;YACA,IAAI,CAAC,KAAK,GAAG,EAAE;QACnB,OAAO;YACH,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE;QACnC;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,OAAO,WAAW,aAAa;YAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;QACrC;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,KAAK,EAAE,GAAG;YACxC,IAAI,KAAK;gBACL,QAAQ,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc,CAAC,KAAK,QAAQ,EAAE,SAAS,IAAI;oBAC3F,OAAO,KAAK,OAAO,KAAK,MAAM,uBAAuB,GAAG,OAAO;gBACnE;YACJ,OAAO;gBACH,MAAM,IAAI,CAAC;YACf;YACA,OAAO;QACX,GAAG,EAAE;IACT;IACA,OAAO,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,SAAS,EAAE,aAAa;QACtE,IAAI,WAAW;YACX,YAAY,SAAS,YAAY;QACrC;QACA,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,MAAM;QACtD,IAAI,IAAI,GAAG;QACX,IAAI,YAAY,CAAC,UAAU,MAAM;QACjC,IAAI,WAAW;YACX,IAAI,WAAW,CAAC,SAAS,cAAc,CAAC;QAC5C;QACA,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACpE,IAAI,eAAe;YACf,KAAK,YAAY,CAAC,KAAK;QAC3B,OAAO;YACH,KAAK,WAAW,CAAC;QACrB;QACA,OAAO;IACX;IACA,aAAa,YAAY;QACrB;YACI,KAAK;YACL,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,WAAW;YAC3B;QACJ;KACH;IACD,OAAO;AACX;AACA,SAAS,YAAY,SAAS,EAAE,OAAO;IACnC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,iBAAiB,UAAU;IAC/C;AACJ;AAEA,SAAS,KAAK,GAAG;IACb,IAAI,SAAS,MAAM,IAAI,IAAI,MAAM;IACjC,MAAM,EAAE;QACJ,SAAS,SAAS,KAAK,IAAI,UAAU,CAAC,EAAE;IAC5C;IACA;;8DAE0D,GAAG,OAAO,WAAW;AACnF;AACA,IAAI,aAAa;AAEjB,IAAI,WAAW,SAAS,IAAI;IACxB,OAAO,KAAK,OAAO,CAAC,aAAa;AACrC;AACA,IAAI,QAAQ,CAAC;AACb;;;;CAIC,GAAG,SAAS,UAAU,MAAM,EAAE,KAAK;IAChC,IAAI,CAAC,OAAO;QACR,OAAO,SAAS;IACpB;IACA,IAAI,gBAAgB,OAAO;IAC3B,IAAI,MAAM,SAAS;IACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACb,KAAK,CAAC,IAAI,GAAG,SAAS,WAAW,SAAS,MAAM;IACpD;IACA,OAAO,KAAK,CAAC,IAAI;AACrB;AACA;;;;CAIC,GAAG,SAAS,gBAAgB,EAAE,EAAE,GAAG;IAChC,IAAI,2BAA2B;IAC/B,uBAAuB;IACvB,6DAA6D;IAC7D,2EAA2E;IAC3E,IAAI,OAAO,WAAW,aAAa;QAC/B,MAAM,SAAS;IACnB;IACA,IAAI,QAAQ,KAAK;IACjB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACf,KAAK,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC,0BAA0B;IACzD;IACA,OAAO,KAAK,CAAC,MAAM;AACvB;AAEA,SAAS,gBAAgB,QAAQ,EAAE,OAAO;IACtC,IAAI,YAAY,KAAK,GAAG,UAAU,CAAC;IACnC,OAAO,SAAS,GAAG,CAAC,SAAS,IAAI;QAC7B,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS;YAClE,IAAI,OAAO;YACX,wCAAwC;YACxC,KAAK,OAAO;YACZ,OAAO,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG;YACvC,yBAAyB;gBACrB,QAAQ;YACZ;QACJ;IACJ;AACJ;AACA,IAAI,qBAAqB,WAAW,GAAG;IACnC,SAAS,mBAAmB,KAAK;QAC7B,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,cAAc,IAAI,UAAU,EAAE,aAAa,gBAAgB,KAAK,IAAI,OAAO,aAAa,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,QAAQ;QACrO,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,WAAW;YACvC,MAAM;YACN,kBAAkB;QACtB;QACA,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,cAAc,OAAO,qBAAqB,WAAW;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,IAAI,SAAS,mBAAmB,SAAS;IACzC,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK;QAC3B,IAAI,QAAQ,IAAI;QAChB,IAAI,cAAc,IAAI,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,iBAAiB,GAAG,MAAM,OAAO,CAAC,MAAM,QAAQ;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB;YACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,OAAO,WAAW,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE;YACpD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB;YACxC,IAAI,CAAC,gBAAgB,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;gBAC9E,GAAG,CAAC,QAAQ,GAAG;gBACf,OAAO;YACX,GAAG,CAAC;QACR;QACA,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,OAAO,EAAE,QAAQ,IAAI,KAAK;QAC7E,+CAA+C;QAC/C,IAAI,WAAW,IAAI,CAAC,gBAAgB,EAAE;YAClC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;YAClC;QACJ;QACA,IAAI,UAAU,MAAM,GAAG,CAAC,SAAS,IAAI;YACjC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;QACnC,GAAE,2BAA2B;SAC5B,MAAM,CAAC,SAAS,KAAK;YAClB,OAAO,UAAU,CAAC;QACtB;QACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;QACzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;IACrC;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK;QACjC,IAAI,QAAQ,IAAI;QAChB,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO;QAC/C,UAAU,WAAW,IAAI,CAAC,gBAAgB,EAAE,eAAe,UAAU;QACrE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,GAAG;YACpC,IAAI,gBAAgB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;YACjE,IAAI,eAAe;gBACf,cAAc,UAAU,CAAC,WAAW,CAAC;gBACrC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;YACpC,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;oBACzC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;gBACnC;gBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACjC;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QACzC;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK,EAAE,SAAS;QAC5C,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,aAAa,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,SAAS,OAAO;YAClF,OAAO;gBACH;gBACA,MAAM,WAAW,CAAC,QAAQ;aAC7B;QACL,KAAK,EAAE;QACP,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;QACnC,OAAO,WAAW,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,OAAO;YACpE,OAAO;gBACH;gBACA,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK;oBACtC,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAClC,GAAG,IAAI,CAAC,MAAM,iBAAiB,GAAG,KAAK;aAC1C;QACL,GAAE,yBAAyB;SAC1B,MAAM,CAAC,SAAS,IAAI;YACjB,OAAO,QAAQ,IAAI,CAAC,EAAE;QAC1B;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,OAAO;QACnC,OAAO,gBAAgB,IAAI,CAAC,QAAQ,IAAI;IAC5C;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QAC/C,IAAI,MAAM,MAAM,QAAQ,EAAE,UAAU,MAAM,OAAO,EAAE,KAAK,MAAM,EAAE;QAChE,IAAI,SAAS;YACT,IAAI,UAAU,UAAU,IAAI;YAC5B,OAAO;gBACH,SAAS;gBACT,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,IAAI;oBAC7C,OAAO,gBAAgB,SAAS;gBACpC,KAAK;oBACD,gBAAgB,SAAS;iBAC5B;YACL;QACJ;QACA,OAAO;YACH,SAAS,UAAU;YACnB,OAAO,MAAM,OAAO,CAAC,OAAO,MAAM;gBAC9B;aACH;QACL;IACJ;IACA;;;;GAID,GAAG,OAAO,gBAAgB,GAAG,SAAS;QACjC,IAAI,WAAW,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACpE,OAAO,SAAS,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;YACxC,IAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,CAAC;YAC1B,GAAG,CAAC,GAAG,GAAG;YACV,OAAO;QACX,GAAG,CAAC;IACR;IACA,OAAO;AACX;AACA,SAAS,UAAU,SAAS,EAAE,OAAO;IACjC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,yBAAyB,UAAU;IACvD;AACJ;AACA,IAAI,oBAAoB,WAAW,GAAG,MAAM,aAAa,CAAC;AAC1D,kBAAkB,WAAW,GAAG;AAChC,SAAS;IACL,OAAO,IAAI;AACf;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,qBAAqB,MAAM,QAAQ,EAAE,WAAW,MAAM,QAAQ;IAClE,IAAI,eAAe,MAAM,UAAU,CAAC;IACpC,IAAI,MAAM,MAAM,QAAQ;uCAAC;YACrB,OAAO,gBAAgB,sBAAsB;QACjD;uCAAI,WAAW,GAAG,CAAC,EAAE;IACrB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,QAAQ,EAAE;QACrF,OAAO;IACX,GAAG;AACP;AACA,SAAS;IACL,OAAO,MAAM,UAAU,CAAC;AAC5B;AAEA,wFAAwF;AACxF,sDAAsD;AACtD,IAAI,qBAAqB,cAAc,CAAC,UAAU,CAAC,kBAAkB,IAAI,cAAc,CAAC,UAAU,CAAC,eAAe;AAClH,IAAI,kBAAkB,OAAO,WAAW,cAAc,wBAAwB;AAC9E,SAAS,SAAS,KAAK;IACnB,IAAI,WAAW,kBAAkB,kBAAkB;IACnD,oDAAoD;IACpD,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,IAAI,OAAO,WAAW,aAAa;QAC/B,SAAS,GAAG,CAAC;QACb,OAAO;IACX;IACA;uCAAmB;YACf,SAAS,GAAG,CAAC;YACb;+CAAO;oBACH,SAAS,MAAM,CAAC;gBACpB;;QACJ,wEAAwE;QACxE;sCAAG;QACC,MAAM,EAAE;QACR,OAAO,MAAM,OAAO;KACvB;IACD,OAAO;AACX;AACA,SAAS,OAAO,GAAG,SAAS,IAAI;IAC5B,OAAO,KAAK,GAAG,CAAC,SAAS,OAAO;QAC5B,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAI,QAAQ,OAAO,CAAC,EAAE;QACtB,OAAO,UAAU,QAAQ;IAC7B,GAAG,IAAI,CAAC;AACZ;AAEA,QAAQ,aAAa,GAAG;AACxB,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,KAAK,GAAG;AAChB,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/node_modules/styled-jsx/style.js"], "sourcesContent": ["module.exports = require('./dist/index').style\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,2GAAwB,KAAK", "ignoreList": [0], "debugId": null}}]}