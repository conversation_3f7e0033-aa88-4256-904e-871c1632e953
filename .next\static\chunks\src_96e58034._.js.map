{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/ui/Card.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = forwardRef(({ \n  className, \n  variant = 'default',\n  hover = true,\n  glow = false,\n  children, \n  ...props \n}, ref) => {\n  const baseStyles = `\n    relative rounded-xl border backdrop-blur-sm\n    transition-all duration-300 ease-out\n    overflow-hidden group\n  `;\n\n  const variants = {\n    default: `\n      bg-surface-dark/80 border-electric-purple/20\n      hover:border-neon-blue/40 hover:bg-surface-light/80\n    `,\n    glass: `\n      bg-white/5 border-white/10\n      hover:bg-white/10 hover:border-white/20\n      backdrop-blur-md\n    `,\n    neon: `\n      bg-dark-space/90 border-neon-blue/50\n      hover:border-neon-blue hover:bg-dark-space\n      shadow-lg shadow-neon-blue/10\n    `,\n    purple: `\n      bg-deep-purple/80 border-electric-purple/30\n      hover:border-electric-purple hover:bg-deep-purple\n      shadow-lg shadow-electric-purple/10\n    `,\n    gradient: `\n      bg-gradient-to-br from-surface-dark/80 to-deep-purple/80\n      border-gradient-to-r from-neon-blue/30 to-electric-purple/30\n      hover:from-surface-light/80 hover:to-midnight-blue/80\n    `\n  };\n\n  const hoverEffects = hover ? `\n    hover:scale-[1.02] hover:-translate-y-1\n    hover:shadow-2xl hover:shadow-neon-blue/20\n  ` : '';\n\n  const glowEffect = glow ? `\n    before:absolute before:inset-0 before:rounded-xl\n    before:bg-gradient-to-r before:from-neon-blue/20 before:to-electric-purple/20\n    before:opacity-0 before:transition-opacity before:duration-300\n    hover:before:opacity-100\n  ` : '';\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        baseStyles,\n        variants[variant],\n        hoverEffects,\n        glowEffect,\n        className\n      )}\n      {...props}\n    >\n      {/* Animated border gradient */}\n      <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-neon-blue via-electric-purple to-hologram-pink opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n      \n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n      \n      {/* Corner accent */}\n      <div className=\"absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-neon-blue/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n    </div>\n  );\n});\n\nCard.displayName = 'Card';\n\n// Card Header Component\nconst CardHeader = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('p-6 pb-4', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardHeader.displayName = 'CardHeader';\n\n// Card Title Component\nconst CardTitle = forwardRef(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-xl font-semibold text-text-primary mb-2',\n      'bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </h3>\n));\n\nCardTitle.displayName = 'CardTitle';\n\n// Card Description Component\nconst CardDescription = forwardRef(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-text-secondary leading-relaxed', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\n\nCardDescription.displayName = 'CardDescription';\n\n// Card Content Component\nconst CardContent = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('px-6 pb-6', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardContent.displayName = 'CardContent';\n\n// Card Footer Component\nconst CardFooter = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'px-6 py-4 border-t border-electric-purple/20',\n      'bg-gradient-to-r from-transparent via-electric-purple/5 to-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardFooter.displayName = 'CardFooter';\n\n// Export all components\nexport {\n  Card,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n  CardContent,\n  CardFooter\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACvB,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAC;;;;EAIpB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;IAGV,CAAC;QACD,OAAO,CAAC;;;;IAIR,CAAC;QACD,MAAM,CAAC;;;;IAIP,CAAC;QACD,QAAQ,CAAC;;;;IAIT,CAAC;QACD,UAAU,CAAC;;;;IAIX,CAAC;IACH;IAEA,MAAM,eAAe,QAAQ,CAAC;;;EAG9B,CAAC,GAAG;IAEJ,MAAM,aAAa,OAAO,CAAC;;;;;EAK3B,CAAC,GAAG;IAEJ,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,cACA,YACA;QAED,GAAG,KAAK;;0BAGT,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;;AAEA,KAAK,WAAW,GAAG;AAEnB,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG;AAEzB,uBAAuB;AACvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIL,UAAU,WAAW,GAAG;AAExB,6BAA6B;AAC7B,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACrE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;QACpD,GAAG,KAAK;kBAER;;;;;;;AAIL,gBAAgB,WAAW,GAAG;AAE9B,yBAAyB;AACzB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACjE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;kBAER;;;;;;;AAIL,YAAY,WAAW,GAAG;AAE1B,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,0EACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/3d/SimpleCard3D.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState } from 'react';\nimport { cn } from '@/lib/utils';\n\n// Simplified 3D Card with CSS transforms (more stable than Three.js)\nconst SimpleCard3D = ({ \n  cardData, \n  theme = 'cyber', \n  className = '',\n  onClick,\n  ...props \n}) => {\n  const cardRef = useRef(null);\n  const [isHovered, setIsHovered] = useState(false);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n\n  // Theme configurations\n  const themes = {\n    cyber: {\n      primary: '#00f5ff',\n      secondary: '#8b5cf6',\n      accent: '#ff006e',\n      gradient: 'from-neon-blue via-electric-purple to-hologram-pink'\n    },\n    matrix: {\n      primary: '#00ff41',\n      secondary: '#008f11',\n      accent: '#00ff88',\n      gradient: 'from-cyber-green via-matrix-green to-green-400'\n    },\n    neon: {\n      primary: '#ff0080',\n      secondary: '#8000ff',\n      accent: '#00ffff',\n      gradient: 'from-hologram-pink via-electric-purple to-neon-blue'\n    },\n    sunset: {\n      primary: '#ff0080',\n      secondary: '#ff6b35',\n      accent: '#ffd700',\n      gradient: 'from-hologram-pink via-orange-500 to-quantum-gold'\n    },\n    eco: {\n      primary: '#00ff88',\n      secondary: '#00cc6a',\n      accent: '#66ff99',\n      gradient: 'from-cyber-green via-green-500 to-emerald-400'\n    }\n  };\n\n  const currentTheme = themes[theme] || themes.cyber;\n\n  useEffect(() => {\n    const card = cardRef.current;\n    if (!card) return;\n\n    const handleMouseMove = (e) => {\n      if (!isHovered) return;\n\n      const rect = card.getBoundingClientRect();\n      const centerX = rect.left + rect.width / 2;\n      const centerY = rect.top + rect.height / 2;\n      \n      const mouseX = e.clientX - centerX;\n      const mouseY = e.clientY - centerY;\n      \n      const rotateX = (mouseY / (rect.height / 2)) * -10;\n      const rotateY = (mouseX / (rect.width / 2)) * 10;\n      \n      setMousePosition({ x: rotateX, y: rotateY });\n      \n      card.style.transform = `\n        perspective(1000px) \n        rotateX(${rotateX}deg) \n        rotateY(${rotateY}deg) \n        scale3d(1.05, 1.05, 1.05)\n      `;\n    };\n\n    const handleMouseEnter = () => {\n      setIsHovered(true);\n      card.style.transition = 'transform 0.1s ease-out';\n    };\n\n    const handleMouseLeave = () => {\n      setIsHovered(false);\n      card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';\n      card.style.transition = 'transform 0.5s ease-out';\n      setMousePosition({ x: 0, y: 0 });\n    };\n\n    card.addEventListener('mousemove', handleMouseMove);\n    card.addEventListener('mouseenter', handleMouseEnter);\n    card.addEventListener('mouseleave', handleMouseLeave);\n\n    return () => {\n      card.removeEventListener('mousemove', handleMouseMove);\n      card.removeEventListener('mouseenter', handleMouseEnter);\n      card.removeEventListener('mouseleave', handleMouseLeave);\n    };\n  }, [isHovered]);\n\n  return (\n    <div className={cn('relative w-full h-full', className)} {...props}>\n      <div\n        ref={cardRef}\n        onClick={onClick}\n        className=\"relative w-full h-full cursor-pointer transform-gpu\"\n        style={{ transformStyle: 'preserve-3d' }}\n      >\n        {/* Main Card */}\n        <div className={`\n          relative w-full h-full rounded-2xl overflow-hidden\n          bg-gradient-to-br ${currentTheme.gradient}\n          shadow-2xl border border-white/20\n          ${isHovered ? 'shadow-3xl' : ''}\n        `}>\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 opacity-10\">\n            <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_70%,rgba(255,255,255,0.1),transparent_50%)]\"></div>\n            <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(255,255,255,0.1),transparent_50%)]\"></div>\n          </div>\n\n          {/* Card Content */}\n          <div className=\"relative z-10 p-6 h-full flex flex-col justify-between text-white\">\n            {/* Header */}\n            <div className=\"flex items-center gap-4\">\n              <div \n                className=\"text-4xl filter drop-shadow-lg\"\n                style={{ \n                  filter: `drop-shadow(0 0 10px ${currentTheme.primary})`,\n                  transform: `translateZ(20px) rotateY(${mousePosition.y * 0.5}deg)`\n                }}\n              >\n                {cardData.avatar}\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold mb-1 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent\">\n                  {cardData.name}\n                </h3>\n                <p className=\"text-white/80 text-sm\">\n                  {cardData.title}\n                </p>\n              </div>\n            </div>\n\n            {/* Company & Industry */}\n            <div className=\"text-center\">\n              <p className=\"text-lg font-semibold mb-2\" style={{ color: currentTheme.primary }}>\n                {cardData.company}\n              </p>\n              <p className=\"text-white/70 text-sm\">\n                {cardData.industry}\n              </p>\n              <p className=\"text-white/60 text-xs mt-2 italic\">\n                {cardData.aiPersonality?.voiceIntro?.substring(0, 60)}...\n              </p>\n            </div>\n\n            {/* Stats */}\n            <div className=\"flex justify-between items-center\">\n              <div className=\"text-center\">\n                <div className=\"text-sm font-bold\" style={{ color: currentTheme.accent }}>\n                  {cardData.cardAnalytics?.totalViews?.toLocaleString()}\n                </div>\n                <div className=\"text-xs text-white/60\">Views</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-sm font-bold\" style={{ color: currentTheme.secondary }}>\n                  {cardData.cardAnalytics?.engagementRate}%\n                </div>\n                <div className=\"text-xs text-white/60\">Engagement</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-sm font-bold text-white\">\n                  {cardData.verification?.trustScore}%\n                </div>\n                <div className=\"text-xs text-white/60\">Trust</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Floating Particles */}\n          <div className=\"absolute top-4 right-4\">\n            <div \n              className=\"w-3 h-3 rounded-full animate-ping\"\n              style={{ backgroundColor: currentTheme.primary }}\n            />\n          </div>\n          \n          <div className=\"absolute bottom-4 left-4\">\n            <div \n              className=\"w-2 h-2 rounded-full animate-pulse\"\n              style={{ backgroundColor: currentTheme.accent }}\n            />\n          </div>\n\n          {/* Hover Glow Effect */}\n          {isHovered && (\n            <div \n              className=\"absolute inset-0 rounded-2xl opacity-20 transition-opacity duration-300\"\n              style={{ \n                background: `radial-gradient(circle at 50% 50%, ${currentTheme.primary}, transparent 70%)`,\n                filter: 'blur(20px)'\n              }}\n            />\n          )}\n\n          {/* AI Status Indicator */}\n          <div className=\"absolute top-2 right-2 flex items-center gap-1\">\n            <div \n              className=\"w-2 h-2 rounded-full animate-pulse\"\n              style={{ backgroundColor: currentTheme.primary }}\n            />\n            <span className=\"text-xs text-white/80 font-mono\">AI</span>\n          </div>\n        </div>\n\n        {/* Reflection Effect */}\n        <div \n          className=\"absolute inset-x-0 bottom-0 h-1/3 rounded-b-2xl opacity-20\"\n          style={{\n            background: `linear-gradient(to top, ${currentTheme.primary}20, transparent)`,\n            transform: 'translateZ(-10px) rotateX(180deg)',\n            filter: 'blur(2px)'\n          }}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleCard3D;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,qEAAqE;AACrE,MAAM,eAAe,CAAC,EACpB,QAAQ,EACR,QAAQ,OAAO,EACf,YAAY,EAAE,EACd,OAAO,EACP,GAAG,OACJ;;IACC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,uBAAuB;IACvB,MAAM,SAAS;QACb,OAAO;YACL,SAAS;YACT,WAAW;YACX,QAAQ;YACR,UAAU;QACZ;QACA,QAAQ;YACN,SAAS;YACT,WAAW;YACX,QAAQ;YACR,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,WAAW;YACX,QAAQ;YACR,UAAU;QACZ;QACA,QAAQ;YACN,SAAS;YACT,WAAW;YACX,QAAQ;YACR,UAAU;QACZ;QACA,KAAK;YACH,SAAS;YACT,WAAW;YACX,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,MAAM,eAAe,MAAM,CAAC,MAAM,IAAI,OAAO,KAAK;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,OAAO,QAAQ,OAAO;YAC5B,IAAI,CAAC,MAAM;YAEX,MAAM;0DAAkB,CAAC;oBACvB,IAAI,CAAC,WAAW;oBAEhB,MAAM,OAAO,KAAK,qBAAqB;oBACvC,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;oBACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;oBAEzC,MAAM,SAAS,EAAE,OAAO,GAAG;oBAC3B,MAAM,SAAS,EAAE,OAAO,GAAG;oBAE3B,MAAM,UAAU,AAAC,SAAS,CAAC,KAAK,MAAM,GAAG,CAAC,IAAK,CAAC;oBAChD,MAAM,UAAU,AAAC,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC,IAAK;oBAE9C,iBAAiB;wBAAE,GAAG;wBAAS,GAAG;oBAAQ;oBAE1C,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC;;gBAEd,EAAE,QAAQ;gBACV,EAAE,QAAQ;;MAEpB,CAAC;gBACH;;YAEA,MAAM;2DAAmB;oBACvB,aAAa;oBACb,KAAK,KAAK,CAAC,UAAU,GAAG;gBAC1B;;YAEA,MAAM;2DAAmB;oBACvB,aAAa;oBACb,KAAK,KAAK,CAAC,SAAS,GAAG;oBACvB,KAAK,KAAK,CAAC,UAAU,GAAG;oBACxB,iBAAiB;wBAAE,GAAG;wBAAG,GAAG;oBAAE;gBAChC;;YAEA,KAAK,gBAAgB,CAAC,aAAa;YACnC,KAAK,gBAAgB,CAAC,cAAc;YACpC,KAAK,gBAAgB,CAAC,cAAc;YAEpC;0CAAO;oBACL,KAAK,mBAAmB,CAAC,aAAa;oBACtC,KAAK,mBAAmB,CAAC,cAAc;oBACvC,KAAK,mBAAmB,CAAC,cAAc;gBACzC;;QACF;iCAAG;QAAC;KAAU;IAEd,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QAAa,GAAG,KAAK;kBAChE,cAAA,6LAAC;YACC,KAAK;YACL,SAAS;YACT,WAAU;YACV,OAAO;gBAAE,gBAAgB;YAAc;;8BAGvC,6LAAC;oBAAI,WAAW,CAAC;;4BAEG,EAAE,aAAa,QAAQ,CAAC;;UAE1C,EAAE,YAAY,eAAe,GAAG;QAClC,CAAC;;sCAEC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,QAAQ,CAAC,qBAAqB,EAAE,aAAa,OAAO,CAAC,CAAC,CAAC;gDACvD,WAAW,CAAC,yBAAyB,EAAE,cAAc,CAAC,GAAG,IAAI,IAAI,CAAC;4CACpE;sDAEC,SAAS,MAAM;;;;;;sDAElB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DACX,SAAS,IAAI;;;;;;8DAEhB,6LAAC;oDAAE,WAAU;8DACV,SAAS,KAAK;;;;;;;;;;;;;;;;;;8CAMrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;4CAA6B,OAAO;gDAAE,OAAO,aAAa,OAAO;4CAAC;sDAC5E,SAAS,OAAO;;;;;;sDAEnB,6LAAC;4CAAE,WAAU;sDACV,SAAS,QAAQ;;;;;;sDAEpB,6LAAC;4CAAE,WAAU;;gDACV,SAAS,aAAa,EAAE,YAAY,UAAU,GAAG;gDAAI;;;;;;;;;;;;;8CAK1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAAoB,OAAO;wDAAE,OAAO,aAAa,MAAM;oDAAC;8DACpE,SAAS,aAAa,EAAE,YAAY;;;;;;8DAEvC,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAAoB,OAAO;wDAAE,OAAO,aAAa,SAAS;oDAAC;;wDACvE,SAAS,aAAa,EAAE;wDAAe;;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,SAAS,YAAY,EAAE;wDAAW;;;;;;;8DAErC,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,aAAa,OAAO;gCAAC;;;;;;;;;;;sCAInD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,aAAa,MAAM;gCAAC;;;;;;;;;;;wBAKjD,2BACC,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY,CAAC,mCAAmC,EAAE,aAAa,OAAO,CAAC,kBAAkB,CAAC;gCAC1F,QAAQ;4BACV;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,aAAa,OAAO;oCAAC;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;;8BAKtD,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY,CAAC,wBAAwB,EAAE,aAAa,OAAO,CAAC,gBAAgB,CAAC;wBAC7E,WAAW;wBACX,QAAQ;oBACV;;;;;;;;;;;;;;;;;AAKV;GAnOM;KAAA;uCAqOS", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/3d/SimpleCarousel.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport SimpleCard3D from './SimpleCard3D';\nimport Button from '@/components/ui/Button';\n\nconst SimpleCarousel = ({ cards, onCardSelect, activeCardIndex = 0 }) => {\n  const [currentIndex, setCurrentIndex] = useState(activeCardIndex);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying) return;\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % cards.length);\n    }, 4000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying, cards.length]);\n\n  // Update parent when card changes\n  useEffect(() => {\n    if (onCardSelect && cards[currentIndex]) {\n      onCardSelect(cards[currentIndex], currentIndex);\n    }\n  }, [currentIndex, cards, onCardSelect]);\n\n  const handleCardSelect = (index) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  };\n\n  const nextCard = (e) => {\n    e?.stopPropagation();\n    const newIndex = (currentIndex + 1) % cards.length;\n    setCurrentIndex(newIndex);\n    setIsAutoPlaying(false);\n    if (onCardSelect && cards[newIndex]) {\n      onCardSelect(cards[newIndex], newIndex);\n    }\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  };\n\n  const prevCard = (e) => {\n    e?.stopPropagation();\n    const newIndex = (currentIndex - 1 + cards.length) % cards.length;\n    setCurrentIndex(newIndex);\n    setIsAutoPlaying(false);\n    if (onCardSelect && cards[newIndex]) {\n      onCardSelect(cards[newIndex], newIndex);\n    }\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  };\n\n  const getCardPosition = (index) => {\n    const diff = index - currentIndex;\n    const totalCards = cards.length;\n    \n    // Normalize the difference to be between -totalCards/2 and totalCards/2\n    let normalizedDiff = diff;\n    if (normalizedDiff > totalCards / 2) {\n      normalizedDiff -= totalCards;\n    } else if (normalizedDiff < -totalCards / 2) {\n      normalizedDiff += totalCards;\n    }\n    \n    return normalizedDiff;\n  };\n\n  const getCardStyle = (index) => {\n    const position = getCardPosition(index);\n    const isActive = index === currentIndex;\n    \n    // Calculate transform based on position\n    const translateX = position * 120; // 120px spacing\n    const translateZ = isActive ? 0 : -100 - Math.abs(position) * 50;\n    const rotateY = position * 15; // Slight rotation\n    const scale = isActive ? 1 : 0.8 - Math.abs(position) * 0.1;\n    const opacity = Math.max(0.3, 1 - Math.abs(position) * 0.3);\n    \n    return {\n      transform: `\n        translateX(${translateX}px) \n        translateZ(${translateZ}px) \n        rotateY(${rotateY}deg) \n        scale(${scale})\n      `,\n      opacity,\n      zIndex: isActive ? 10 : 10 - Math.abs(position),\n      transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)'\n    };\n  };\n\n  return (\n    <div className=\"relative w-full h-96 overflow-hidden\">\n      {/* 3D Perspective Container */}\n      <div \n        className=\"relative w-full h-full flex items-center justify-center\"\n        style={{ \n          perspective: '1000px',\n          perspectiveOrigin: 'center center'\n        }}\n      >\n        {/* Cards */}\n        <div className=\"relative w-80 h-64\">\n          {cards.map((card, index) => (\n            <div\n              key={card.id}\n              className=\"absolute inset-0 cursor-pointer\"\n              style={getCardStyle(index)}\n              onClick={() => handleCardSelect(index)}\n            >\n              <SimpleCard3D\n                cardData={card}\n                theme={card.cardTheme?.background || 'cyber'}\n                className=\"w-full h-full\"\n              />\n            </div>\n          ))}\n        </div>\n\n        {/* Navigation Arrows */}\n        <button\n          onClick={prevCard}\n          className=\"absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-surface-dark/80 backdrop-blur-sm border border-electric-purple/30 rounded-full flex items-center justify-center text-text-primary hover:bg-surface-light/80 hover:border-neon-blue/50 transition-all duration-300 z-20\"\n        >\n          ←\n        </button>\n        \n        <button\n          onClick={nextCard}\n          className=\"absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-surface-dark/80 backdrop-blur-sm border border-electric-purple/30 rounded-full flex items-center justify-center text-text-primary hover:bg-surface-light/80 hover:border-neon-blue/50 transition-all duration-300 z-20\"\n        >\n          →\n        </button>\n      </div>\n\n      {/* Dots Navigation */}\n      <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2\">\n        {cards.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => handleCardSelect(index)}\n            className={`w-3 h-3 rounded-full transition-all duration-300 ${\n              index === currentIndex\n                ? 'bg-neon-blue scale-125 shadow-lg shadow-neon-blue/50'\n                : 'bg-text-secondary/30 hover:bg-text-secondary/60'\n            }`}\n          />\n        ))}\n      </div>\n\n      {/* Auto-play Control */}\n      <div className=\"absolute top-4 right-4\">\n        <button\n          onClick={() => setIsAutoPlaying(!isAutoPlaying)}\n          className={`px-3 py-1 rounded-full text-xs border transition-all duration-300 ${\n            isAutoPlaying\n              ? 'border-cyber-green/50 bg-cyber-green/10 text-cyber-green'\n              : 'border-text-secondary/30 bg-surface-dark/50 text-text-secondary'\n          }`}\n        >\n          {isAutoPlaying ? '⏸️ Auto' : '▶️ Manual'}\n        </button>\n      </div>\n\n      {/* Card Info */}\n      <div className=\"absolute top-4 left-4 bg-dark-space/80 backdrop-blur-sm rounded-lg p-3 border border-electric-purple/30\">\n        <div className=\"text-sm text-text-primary font-semibold\">\n          {cards[currentIndex]?.name}\n        </div>\n        <div className=\"text-xs text-text-secondary\">\n          {cards[currentIndex]?.title}\n        </div>\n        <div className=\"text-xs text-electric-purple\">\n          {currentIndex + 1} of {cards.length}\n        </div>\n      </div>\n\n      {/* Background Glow */}\n      <div className=\"absolute inset-0 pointer-events-none\">\n        <div \n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full opacity-20 blur-3xl transition-all duration-1000\"\n          style={{\n            background: `radial-gradient(circle, ${cards[currentIndex]?.cardTheme?.primary || '#00f5ff'}, transparent 70%)`\n          }}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleCarousel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,kBAAkB,CAAC,EAAE;;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,eAAe;YAEpB,MAAM,WAAW;qDAAY;oBAC3B;6DAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;gBACrD;oDAAG;YAEH;4CAAO,IAAM,cAAc;;QAC7B;mCAAG;QAAC;QAAe,MAAM,MAAM;KAAC;IAEhC,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,gBAAgB,KAAK,CAAC,aAAa,EAAE;gBACvC,aAAa,KAAK,CAAC,aAAa,EAAE;YACpC;QACF;mCAAG;QAAC;QAAc;QAAO;KAAa;IAEtC,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,iBAAiB;QAEjB,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,WAAW,CAAC;QAChB,GAAG;QACH,MAAM,WAAW,CAAC,eAAe,CAAC,IAAI,MAAM,MAAM;QAClD,gBAAgB;QAChB,iBAAiB;QACjB,IAAI,gBAAgB,KAAK,CAAC,SAAS,EAAE;YACnC,aAAa,KAAK,CAAC,SAAS,EAAE;QAChC;QACA,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,WAAW,CAAC;QAChB,GAAG;QACH,MAAM,WAAW,CAAC,eAAe,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM;QACjE,gBAAgB;QAChB,iBAAiB;QACjB,IAAI,gBAAgB,KAAK,CAAC,SAAS,EAAE;YACnC,aAAa,KAAK,CAAC,SAAS,EAAE;QAChC;QACA,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,QAAQ;QACrB,MAAM,aAAa,MAAM,MAAM;QAE/B,wEAAwE;QACxE,IAAI,iBAAiB;QACrB,IAAI,iBAAiB,aAAa,GAAG;YACnC,kBAAkB;QACpB,OAAO,IAAI,iBAAiB,CAAC,aAAa,GAAG;YAC3C,kBAAkB;QACpB;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,gBAAgB;QACjC,MAAM,WAAW,UAAU;QAE3B,wCAAwC;QACxC,MAAM,aAAa,WAAW,KAAK,gBAAgB;QACnD,MAAM,aAAa,WAAW,IAAI,CAAC,MAAM,KAAK,GAAG,CAAC,YAAY;QAC9D,MAAM,UAAU,WAAW,IAAI,kBAAkB;QACjD,MAAM,QAAQ,WAAW,IAAI,MAAM,KAAK,GAAG,CAAC,YAAY;QACxD,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,IAAI,KAAK,GAAG,CAAC,YAAY;QAEvD,OAAO;YACL,WAAW,CAAC;mBACC,EAAE,WAAW;mBACb,EAAE,WAAW;gBAChB,EAAE,QAAQ;cACZ,EAAE,MAAM;MAChB,CAAC;YACD;YACA,QAAQ,WAAW,KAAK,KAAK,KAAK,GAAG,CAAC;YACtC,YAAY;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,aAAa;oBACb,mBAAmB;gBACrB;;kCAGA,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAEC,WAAU;gCACV,OAAO,aAAa;gCACpB,SAAS,IAAM,iBAAiB;0CAEhC,cAAA,6LAAC,0IAAA,CAAA,UAAY;oCACX,UAAU;oCACV,OAAO,KAAK,SAAS,EAAE,cAAc;oCACrC,WAAU;;;;;;+BARP,KAAK,EAAE;;;;;;;;;;kCAelB,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAID,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,6LAAC;wBAEC,SAAS,IAAM,iBAAiB;wBAChC,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,yDACA,mDACJ;uBANG;;;;;;;;;;0BAYX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS,IAAM,iBAAiB,CAAC;oBACjC,WAAW,CAAC,kEAAkE,EAC5E,gBACI,6DACA,mEACJ;8BAED,gBAAgB,YAAY;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,KAAK,CAAC,aAAa,EAAE;;;;;;kCAExB,6LAAC;wBAAI,WAAU;kCACZ,KAAK,CAAC,aAAa,EAAE;;;;;;kCAExB,6LAAC;wBAAI,WAAU;;4BACZ,eAAe;4BAAE;4BAAK,MAAM,MAAM;;;;;;;;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY,CAAC,wBAAwB,EAAE,KAAK,CAAC,aAAa,EAAE,WAAW,WAAW,UAAU,kBAAkB,CAAC;oBACjH;;;;;;;;;;;;;;;;;AAKV;GA9LM;KAAA;uCAgMS", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/3d/ARSimulation.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { Text, Html, useTexture, Plane } from '@react-three/drei';\nimport * as THREE from 'three';\n\n// AR Camera Feed Simulation\nconst ARCameraFeed = ({ onFaceDetected, isScanning }) => {\n  const videoRef = useRef();\n  const canvasRef = useRef();\n  const [faceDetected, setFaceDetected] = useState(false);\n  const [scanProgress, setScanProgress] = useState(0);\n\n  useEffect(() => {\n    if (isScanning) {\n      // Simulate face detection process\n      const detectFace = () => {\n        setScanProgress(prev => {\n          const newProgress = prev + 2;\n          if (newProgress >= 100) {\n            setFaceDetected(true);\n            onFaceDetected && onFaceDetected({\n              confidence: 0.95,\n              position: { x: 0.3, y: 0.2 },\n              size: { width: 0.4, height: 0.6 }\n            });\n            return 100;\n          }\n          return newProgress;\n        });\n      };\n\n      const interval = setInterval(detectFace, 50);\n      return () => clearInterval(interval);\n    } else {\n      setScanProgress(0);\n      setFaceDetected(false);\n    }\n  }, [isScanning, onFaceDetected]);\n\n  return (\n    <div className=\"relative w-full h-full bg-gradient-to-br from-midnight-blue to-deep-purple rounded-lg overflow-hidden\">\n      {/* Simulated Camera Feed */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-dark-space/80 to-midnight-blue/80\">\n        {/* Grid overlay for AR feel */}\n        <div className=\"absolute inset-0 opacity-20\">\n          <svg width=\"100%\" height=\"100%\" className=\"absolute inset-0\">\n            <defs>\n              <pattern id=\"grid\" width=\"40\" height=\"40\" patternUnits=\"userSpaceOnUse\">\n                <path d=\"M 40 0 L 0 0 0 40\" fill=\"none\" stroke=\"#00f5ff\" strokeWidth=\"0.5\"/>\n              </pattern>\n            </defs>\n            <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\n          </svg>\n        </div>\n\n        {/* Face Detection Overlay */}\n        {isScanning && (\n          <div className=\"absolute inset-0 flex items-center justify-center\">\n            {/* Scanning Frame */}\n            <div className=\"relative w-64 h-80 border-2 border-neon-blue/50 rounded-lg\">\n              {/* Corner brackets */}\n              <div className=\"absolute top-0 left-0 w-8 h-8 border-t-2 border-l-2 border-neon-blue\"></div>\n              <div className=\"absolute top-0 right-0 w-8 h-8 border-t-2 border-r-2 border-neon-blue\"></div>\n              <div className=\"absolute bottom-0 left-0 w-8 h-8 border-b-2 border-l-2 border-neon-blue\"></div>\n              <div className=\"absolute bottom-0 right-0 w-8 h-8 border-b-2 border-r-2 border-neon-blue\"></div>\n\n              {/* Scanning line */}\n              <div \n                className=\"absolute left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-neon-blue to-transparent transition-all duration-100\"\n                style={{ \n                  top: `${scanProgress}%`,\n                  boxShadow: '0 0 10px #00f5ff'\n                }}\n              />\n\n              {/* Face detected indicator */}\n              {faceDetected && (\n                <div className=\"absolute inset-4 border-2 border-cyber-green rounded-lg bg-cyber-green/10 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl mb-2\">👤</div>\n                    <div className=\"text-cyber-green text-sm font-semibold\">FACE DETECTED</div>\n                    <div className=\"text-cyber-green text-xs\">Confidence: 95%</div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* AR UI Elements */}\n        <div className=\"absolute top-4 left-4 text-neon-blue text-sm font-mono\">\n          <div>AR MODE: ACTIVE</div>\n          <div>SCAN PROGRESS: {scanProgress}%</div>\n          <div>STATUS: {faceDetected ? 'FACE DETECTED' : isScanning ? 'SCANNING...' : 'READY'}</div>\n        </div>\n\n        {/* Center crosshair */}\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\">\n          <div className=\"w-8 h-8 border border-neon-blue/50 rounded-full flex items-center justify-center\">\n            <div className=\"w-2 h-2 bg-neon-blue rounded-full animate-pulse\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// 3D AR Card Overlay\nconst ARCardOverlay = ({ cardData, position = [0, 0, 0], visible = false }) => {\n  const groupRef = useRef();\n  const [scale, setScale] = useState(0);\n\n  useFrame((state) => {\n    if (groupRef.current && visible) {\n      // Entrance animation\n      setScale(prev => Math.min(prev + 0.05, 1));\n      \n      // Floating animation\n      groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;\n      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1;\n    } else {\n      setScale(0);\n    }\n  });\n\n  if (!visible) return null;\n\n  return (\n    <group ref={groupRef} position={position} scale={[scale, scale, scale]}>\n      {/* Card Background */}\n      <Plane args={[2, 1.2]} position={[0, 0, -0.01]}>\n        <meshStandardMaterial\n          color=\"#0a0a0f\"\n          transparent\n          opacity={0.9}\n          side={THREE.DoubleSide}\n        />\n      </Plane>\n\n      {/* Card Border */}\n      <Plane args={[2.1, 1.3]} position={[0, 0, -0.02]}>\n        <meshStandardMaterial\n          color=\"#00f5ff\"\n          transparent\n          opacity={0.3}\n          side={THREE.DoubleSide}\n        />\n      </Plane>\n\n      {/* Card Content */}\n      <Html\n        transform\n        distanceFactor={10}\n        position={[0, 0, 0.01]}\n        style={{\n          width: '200px',\n          height: '120px',\n          background: 'linear-gradient(135deg, rgba(0,245,255,0.1), rgba(139,92,246,0.1))',\n          backdropFilter: 'blur(10px)',\n          border: '1px solid rgba(0,245,255,0.3)',\n          borderRadius: '8px',\n          padding: '12px',\n          color: 'white',\n          fontSize: '10px',\n          fontFamily: 'Inter, sans-serif'\n        }}\n      >\n        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>\n          <div style={{ fontSize: '16px' }}>{cardData.avatar}</div>\n          <div>\n            <div style={{ fontWeight: 'bold', fontSize: '12px' }}>{cardData.name}</div>\n            <div style={{ opacity: 0.8, fontSize: '9px' }}>{cardData.title}</div>\n          </div>\n        </div>\n        \n        <div style={{ textAlign: 'center', marginBottom: '8px' }}>\n          <div style={{ fontWeight: 'bold', color: '#00f5ff' }}>{cardData.company}</div>\n        </div>\n        \n        <div style={{ fontSize: '8px', opacity: 0.7 }}>\n          <div>{cardData.email}</div>\n          <div>{cardData.phone}</div>\n        </div>\n\n        {/* Action buttons */}\n        <div style={{ \n          display: 'flex', \n          gap: '4px', \n          marginTop: '8px',\n          justifyContent: 'center'\n        }}>\n          <button style={{\n            background: 'rgba(0,245,255,0.2)',\n            border: '1px solid #00f5ff',\n            borderRadius: '4px',\n            color: '#00f5ff',\n            fontSize: '8px',\n            padding: '2px 6px',\n            cursor: 'pointer'\n          }}>\n            Connect\n          </button>\n          <button style={{\n            background: 'rgba(139,92,246,0.2)',\n            border: '1px solid #8b5cf6',\n            borderRadius: '4px',\n            color: '#8b5cf6',\n            fontSize: '8px',\n            padding: '2px 6px',\n            cursor: 'pointer'\n          }}>\n            Save\n          </button>\n        </div>\n      </Html>\n\n      {/* Particle effects */}\n      <points>\n        <bufferGeometry>\n          <bufferAttribute\n            attach=\"attributes-position\"\n            count={20}\n            array={new Float32Array(Array.from({ length: 60 }, () => (Math.random() - 0.5) * 3))}\n            itemSize={3}\n          />\n        </bufferGeometry>\n        <pointsMaterial\n          size={0.02}\n          color=\"#00f5ff\"\n          transparent\n          opacity={0.6}\n        />\n      </points>\n    </group>\n  );\n};\n\n// Main AR Simulation Component\nconst ARSimulation = ({ cardData, isActive = false, onCardDetected }) => {\n  const [isScanning, setIsScanning] = useState(false);\n  const [cardVisible, setCardVisible] = useState(false);\n  const [detectedFace, setDetectedFace] = useState(null);\n\n  const handleStartScan = () => {\n    setIsScanning(true);\n    setCardVisible(false);\n  };\n\n  const handleFaceDetected = (faceData) => {\n    setDetectedFace(faceData);\n    setTimeout(() => {\n      setCardVisible(true);\n      setIsScanning(false);\n      onCardDetected && onCardDetected(cardData);\n    }, 1000);\n  };\n\n  const handleStopScan = () => {\n    setIsScanning(false);\n    setCardVisible(false);\n    setDetectedFace(null);\n  };\n\n  return (\n    <div className=\"relative w-full h-96 bg-dark-space rounded-2xl overflow-hidden\">\n      {/* AR Camera Feed */}\n      <ARCameraFeed \n        onFaceDetected={handleFaceDetected}\n        isScanning={isScanning}\n      />\n\n      {/* 3D AR Overlay */}\n      <div className=\"absolute inset-0\">\n        <Canvas\n          camera={{ position: [0, 0, 5], fov: 50 }}\n          style={{ background: 'transparent' }}\n        >\n          <ambientLight intensity={0.5} />\n          <pointLight position={[10, 10, 10]} intensity={1} color=\"#00f5ff\" />\n          \n          <ARCardOverlay\n            cardData={cardData}\n            position={[0, 0, 0]}\n            visible={cardVisible}\n          />\n        </Canvas>\n      </div>\n\n      {/* Control Panel */}\n      <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-4\">\n        {!isScanning && !cardVisible && (\n          <button\n            onClick={handleStartScan}\n            className=\"px-6 py-2 bg-neon-blue/20 border border-neon-blue rounded-lg text-neon-blue hover:bg-neon-blue/30 transition-colors\"\n          >\n            Start AR Scan\n          </button>\n        )}\n        \n        {(isScanning || cardVisible) && (\n          <button\n            onClick={handleStopScan}\n            className=\"px-6 py-2 bg-hologram-pink/20 border border-hologram-pink rounded-lg text-hologram-pink hover:bg-hologram-pink/30 transition-colors\"\n          >\n            Stop Scan\n          </button>\n        )}\n      </div>\n\n      {/* Status Indicator */}\n      <div className=\"absolute top-4 right-4 flex items-center gap-2\">\n        <div className={`w-3 h-3 rounded-full ${\n          cardVisible ? 'bg-cyber-green animate-pulse' :\n          isScanning ? 'bg-neon-blue animate-ping' :\n          'bg-text-secondary/50'\n        }`} />\n        <span className=\"text-sm text-text-secondary\">\n          {cardVisible ? 'Card Detected' :\n           isScanning ? 'Scanning...' :\n           'AR Ready'}\n        </span>\n      </div>\n    </div>\n  );\n};\n\nexport default ARSimulation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,4BAA4B;AAC5B,MAAM,eAAe,CAAC,EAAE,cAAc,EAAE,UAAU,EAAE;;IAClD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,YAAY;gBACd,kCAAkC;gBAClC,MAAM;yDAAa;wBACjB;iEAAgB,CAAA;gCACd,MAAM,cAAc,OAAO;gCAC3B,IAAI,eAAe,KAAK;oCACtB,gBAAgB;oCAChB,kBAAkB,eAAe;wCAC/B,YAAY;wCACZ,UAAU;4CAAE,GAAG;4CAAK,GAAG;wCAAI;wCAC3B,MAAM;4CAAE,OAAO;4CAAK,QAAQ;wCAAI;oCAClC;oCACA,OAAO;gCACT;gCACA,OAAO;4BACT;;oBACF;;gBAEA,MAAM,WAAW,YAAY,YAAY;gBACzC;8CAAO,IAAM,cAAc;;YAC7B,OAAO;gBACL,gBAAgB;gBAChB,gBAAgB;YAClB;QACF;iCAAG;QAAC;QAAY;KAAe;IAE/B,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,OAAM;wBAAO,QAAO;wBAAO,WAAU;;0CACxC,6LAAC;0CACC,cAAA,6LAAC;oCAAQ,IAAG;oCAAO,OAAM;oCAAK,QAAO;oCAAK,cAAa;8CACrD,cAAA,6LAAC;wCAAK,GAAE;wCAAoB,MAAK;wCAAO,QAAO;wCAAU,aAAY;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAK,OAAM;gCAAO,QAAO;gCAAO,MAAK;;;;;;;;;;;;;;;;;gBAKzC,4BACC,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,KAAK,GAAG,aAAa,CAAC,CAAC;oCACvB,WAAW;gCACb;;;;;;4BAID,8BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;sDAAyC;;;;;;sDACxD,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAI;;;;;;sCACL,6LAAC;;gCAAI;gCAAgB;gCAAa;;;;;;;sCAClC,6LAAC;;gCAAI;gCAAS,eAAe,kBAAkB,aAAa,gBAAgB;;;;;;;;;;;;;8BAI9E,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAnGM;KAAA;AAqGN,qBAAqB;AACrB,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,WAAW;IAAC;IAAG;IAAG;CAAE,EAAE,UAAU,KAAK,EAAE;;IACxE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE,CAAC;YACR,IAAI,SAAS,OAAO,IAAI,SAAS;gBAC/B,qBAAqB;gBACrB;8CAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,MAAM;;gBAEvC,qBAAqB;gBACrB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;gBACpF,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;YACpE,OAAO;gBACL,SAAS;YACX;QACF;;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6LAAC;QAAM,KAAK;QAAU,UAAU;QAAU,OAAO;YAAC;YAAO;YAAO;SAAM;;0BAEpE,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;0BAC5C,cAAA,6LAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;oBACT,MAAM,kJAAA,CAAA,aAAgB;;;;;;;;;;;0BAK1B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;0BAC9C,cAAA,6LAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;oBACT,MAAM,kJAAA,CAAA,aAAgB;;;;;;;;;;;0BAK1B,6LAAC,0JAAA,CAAA,OAAI;gBACH,SAAS;gBACT,gBAAgB;gBAChB,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBACtB,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,cAAc;oBACd,SAAS;oBACT,OAAO;oBACP,UAAU;oBACV,YAAY;gBACd;;kCAEA,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;4BAAO,cAAc;wBAAM;;0CACnF,6LAAC;gCAAI,OAAO;oCAAE,UAAU;gCAAO;0CAAI,SAAS,MAAM;;;;;;0CAClD,6LAAC;;kDACC,6LAAC;wCAAI,OAAO;4CAAE,YAAY;4CAAQ,UAAU;wCAAO;kDAAI,SAAS,IAAI;;;;;;kDACpE,6LAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAK,UAAU;wCAAM;kDAAI,SAAS,KAAK;;;;;;;;;;;;;;;;;;kCAIlE,6LAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,cAAc;wBAAM;kCACrD,cAAA,6LAAC;4BAAI,OAAO;gCAAE,YAAY;gCAAQ,OAAO;4BAAU;sCAAI,SAAS,OAAO;;;;;;;;;;;kCAGzE,6LAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAO,SAAS;wBAAI;;0CAC1C,6LAAC;0CAAK,SAAS,KAAK;;;;;;0CACpB,6LAAC;0CAAK,SAAS,KAAK;;;;;;;;;;;;kCAItB,6LAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,KAAK;4BACL,WAAW;4BACX,gBAAgB;wBAClB;;0CACE,6LAAC;gCAAO,OAAO;oCACb,YAAY;oCACZ,QAAQ;oCACR,cAAc;oCACd,OAAO;oCACP,UAAU;oCACV,SAAS;oCACT,QAAQ;gCACV;0CAAG;;;;;;0CAGH,6LAAC;gCAAO,OAAO;oCACb,YAAY;oCACZ,QAAQ;oCACR,cAAc;oCACd,OAAO;oCACP,UAAU;oCACV,SAAS;oCACT,QAAQ;gCACV;0CAAG;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;;kCACC,6LAAC;kCACC,cAAA,6LAAC;4BACC,QAAO;4BACP,OAAO;4BACP,OAAO,IAAI,aAAa,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAG,GAAG,IAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BACjF,UAAU;;;;;;;;;;;kCAGd,6LAAC;wBACC,MAAM;wBACN,OAAM;wBACN,WAAW;wBACX,SAAS;;;;;;;;;;;;;;;;;;AAKnB;IA/HM;;QAIJ,kNAAA,CAAA,WAAQ;;;MAJJ;AAiIN,+BAA+B;AAC/B,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,cAAc,EAAE;;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,kBAAkB;QACtB,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;QAChB,WAAW;YACT,eAAe;YACf,cAAc;YACd,kBAAkB,eAAe;QACnC,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,eAAe;QACf,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,gBAAgB;gBAChB,YAAY;;;;;;0BAId,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sMAAA,CAAA,SAAM;oBACL,QAAQ;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,KAAK;oBAAG;oBACvC,OAAO;wBAAE,YAAY;oBAAc;;sCAEnC,6LAAC;4BAAa,WAAW;;;;;;sCACzB,6LAAC;4BAAW,UAAU;gCAAC;gCAAI;gCAAI;6BAAG;4BAAE,WAAW;4BAAG,OAAM;;;;;;sCAExD,6LAAC;4BACC,UAAU;4BACV,UAAU;gCAAC;gCAAG;gCAAG;6BAAE;4BACnB,SAAS;;;;;;;;;;;;;;;;;0BAMf,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,cAAc,CAAC,6BACf,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;oBAKF,CAAC,cAAc,WAAW,mBACzB,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,CAAC,qBAAqB,EACpC,cAAc,iCACd,aAAa,8BACb,wBACA;;;;;;kCACF,6LAAC;wBAAK,WAAU;kCACb,cAAc,kBACd,aAAa,gBACb;;;;;;;;;;;;;;;;;;AAKX;IAtFM;MAAA;uCAwFS", "debugId": null}}, {"offset": {"line": 1685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/ai/VoiceInteraction.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst VoiceInteraction = ({ cardData, isActive = false, onInteraction }) => {\n  const [isListening, setIsListening] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isSpeaking, setIsSpeaking] = useState(false);\n  const [transcript, setTranscript] = useState('');\n  const [aiResponse, setAiResponse] = useState('');\n  const [conversation, setConversation] = useState([]);\n  const [voiceLevel, setVoiceLevel] = useState(0);\n  \n  const recognitionRef = useRef(null);\n  const synthRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n\n  // Initialize speech recognition and synthesis\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Speech Recognition\n      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {\n        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n        recognitionRef.current = new SpeechRecognition();\n        recognitionRef.current.continuous = true;\n        recognitionRef.current.interimResults = true;\n        recognitionRef.current.lang = 'en-US';\n\n        recognitionRef.current.onresult = (event) => {\n          let finalTranscript = '';\n          for (let i = event.resultIndex; i < event.results.length; i++) {\n            if (event.results[i].isFinal) {\n              finalTranscript += event.results[i][0].transcript;\n            }\n          }\n          if (finalTranscript) {\n            setTranscript(finalTranscript);\n            handleVoiceInput(finalTranscript);\n          }\n        };\n\n        recognitionRef.current.onerror = (event) => {\n          console.error('Speech recognition error:', event.error);\n          setIsListening(false);\n        };\n      }\n\n      // Speech Synthesis\n      if ('speechSynthesis' in window) {\n        synthRef.current = window.speechSynthesis;\n      }\n    }\n  }, []);\n\n  // AI Response Generator\n  const generateAIResponse = (userInput) => {\n    const input = userInput.toLowerCase();\n    const { aiPersonality, recentAchievements, currentProjects, availableActions } = cardData;\n\n    // Context-aware responses\n    if (input.includes('hello') || input.includes('hi') || input.includes('introduce')) {\n      return aiPersonality.voiceIntro;\n    }\n    \n    if (input.includes('project') || input.includes('working on')) {\n      const project = currentProjects[Math.floor(Math.random() * currentProjects.length)];\n      return `I'm currently working on ${project}. It's really exciting because it combines ${aiPersonality.interests.slice(0, 2).join(' and ')}.`;\n    }\n    \n    if (input.includes('achievement') || input.includes('accomplishment')) {\n      const achievement = recentAchievements[Math.floor(Math.random() * recentAchievements.length)];\n      return `One of my recent achievements is ${achievement}. I'm particularly proud of this because it aligns with my passion for ${aiPersonality.interests[0]}.`;\n    }\n    \n    if (input.includes('meet') || input.includes('schedule') || input.includes('coffee')) {\n      return `I'd love to meet! You can schedule a meeting through my calendar link, or we can connect on LinkedIn first. What works best for you?`;\n    }\n    \n    if (input.includes('contact') || input.includes('reach')) {\n      return `The best way to reach me is via email at ${cardData.email}, or you can connect with me on LinkedIn. I typically respond within 24 hours.`;\n    }\n    \n    if (input.includes('company') || input.includes('work')) {\n      return `I work at ${cardData.company}, where we focus on ${cardData.industry}. We're doing some groundbreaking work in ${aiPersonality.interests[0]}.`;\n    }\n\n    // Default responses based on personality\n    const responses = [\n      `That's interesting! As someone who's ${aiPersonality.personalityTraits.join(', ')}, I'd love to explore that topic further.`,\n      `Great question! My experience in ${cardData.industry} has taught me that ${aiPersonality.interests[0]} is key to innovation.`,\n      `I appreciate your curiosity! Feel free to reach out anytime to discuss ${aiPersonality.interests.slice(0, 2).join(' or ')}.`\n    ];\n    \n    return responses[Math.floor(Math.random() * responses.length)];\n  };\n\n  // Handle voice input\n  const handleVoiceInput = async (input) => {\n    setIsListening(false);\n    setIsProcessing(true);\n    \n    // Add user message to conversation\n    const userMessage = { type: 'user', content: input, timestamp: Date.now() };\n    setConversation(prev => [...prev, userMessage]);\n    \n    // Simulate AI processing delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    // Generate AI response\n    const response = generateAIResponse(input);\n    setAiResponse(response);\n    \n    // Add AI response to conversation\n    const aiMessage = { type: 'ai', content: response, timestamp: Date.now() };\n    setConversation(prev => [...prev, aiMessage]);\n    \n    setIsProcessing(false);\n    \n    // Speak the response\n    speakResponse(response);\n    \n    // Trigger interaction callback\n    onInteraction && onInteraction({ type: 'voice_interaction', input, response });\n  };\n\n  // Text-to-speech\n  const speakResponse = (text) => {\n    if (synthRef.current && text) {\n      setIsSpeaking(true);\n      const utterance = new SpeechSynthesisUtterance(text);\n      utterance.rate = 0.9;\n      utterance.pitch = 1.1;\n      utterance.volume = 0.8;\n      \n      // Try to use a more natural voice\n      const voices = synthRef.current.getVoices();\n      const preferredVoice = voices.find(voice => \n        voice.name.includes('Google') || \n        voice.name.includes('Microsoft') ||\n        voice.lang.includes('en-US')\n      );\n      if (preferredVoice) {\n        utterance.voice = preferredVoice;\n      }\n      \n      utterance.onend = () => setIsSpeaking(false);\n      utterance.onerror = () => setIsSpeaking(false);\n      \n      synthRef.current.speak(utterance);\n    }\n  };\n\n  // Start/stop listening\n  const toggleListening = () => {\n    if (isListening) {\n      recognitionRef.current?.stop();\n      setIsListening(false);\n    } else {\n      if (recognitionRef.current) {\n        recognitionRef.current.start();\n        setIsListening(true);\n        setTranscript('');\n      }\n    }\n  };\n\n  // Voice level visualization\n  const VoiceVisualizer = () => {\n    const bars = Array.from({ length: 5 }, (_, i) => (\n      <div\n        key={i}\n        className={`w-2 bg-neon-blue rounded-full transition-all duration-150 ${\n          isListening ? 'animate-pulse' : ''\n        }`}\n        style={{\n          height: `${Math.random() * 40 + 10}px`,\n          animationDelay: `${i * 0.1}s`\n        }}\n      />\n    ));\n\n    return (\n      <div className=\"flex items-end gap-1 h-12 justify-center\">\n        {bars}\n      </div>\n    );\n  };\n\n  // AI Avatar Animation\n  const AIAvatar = () => (\n    <div className=\"relative\">\n      <div className={`text-6xl transition-all duration-300 ${\n        isSpeaking ? 'animate-bounce' : \n        isProcessing ? 'animate-pulse' : \n        isListening ? 'animate-ping' : ''\n      }`}>\n        🤖\n      </div>\n      \n      {/* Status indicator */}\n      <div className=\"absolute -bottom-2 -right-2\">\n        <div className={`w-4 h-4 rounded-full ${\n          isSpeaking ? 'bg-cyber-green animate-pulse' :\n          isProcessing ? 'bg-quantum-gold animate-spin' :\n          isListening ? 'bg-neon-blue animate-ping' :\n          'bg-text-secondary/50'\n        }`} />\n      </div>\n    </div>\n  );\n\n  return (\n    <Card variant=\"glass\" className=\"p-6\">\n      <CardContent>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold text-text-primary mb-2\">\n              AI Voice Assistant\n            </h3>\n            <p className=\"text-text-secondary\">\n              Talk to {cardData.name}'s AI persona\n            </p>\n          </div>\n\n          {/* AI Avatar */}\n          <div className=\"flex justify-center\">\n            <AIAvatar />\n          </div>\n\n          {/* Voice Visualizer */}\n          {isListening && (\n            <div className=\"flex justify-center\">\n              <VoiceVisualizer />\n            </div>\n          )}\n\n          {/* Status Display */}\n          <div className=\"text-center\">\n            {isListening && (\n              <p className=\"text-neon-blue\">🎤 Listening...</p>\n            )}\n            {isProcessing && (\n              <p className=\"text-quantum-gold\">🧠 Processing...</p>\n            )}\n            {isSpeaking && (\n              <p className=\"text-cyber-green\">🔊 Speaking...</p>\n            )}\n            {!isListening && !isProcessing && !isSpeaking && (\n              <p className=\"text-text-secondary\">Ready to chat</p>\n            )}\n          </div>\n\n          {/* Conversation History */}\n          {conversation.length > 0 && (\n            <div className=\"max-h-40 overflow-y-auto space-y-2 bg-dark-space/50 rounded-lg p-4\">\n              {conversation.slice(-4).map((message, index) => (\n                <div\n                  key={index}\n                  className={`text-sm ${\n                    message.type === 'user' \n                      ? 'text-neon-blue text-right' \n                      : 'text-cyber-green text-left'\n                  }`}\n                >\n                  <strong>{message.type === 'user' ? 'You' : cardData.name}:</strong>\n                  <br />\n                  {message.content}\n                </div>\n              ))}\n            </div>\n          )}\n\n          {/* Controls */}\n          <div className=\"flex justify-center gap-4\">\n            <Button\n              onClick={toggleListening}\n              variant={isListening ? 'danger' : 'primary'}\n              size=\"lg\"\n              disabled={isProcessing || isSpeaking}\n            >\n              {isListening ? '🛑 Stop' : '🎤 Talk'}\n            </Button>\n            \n            {aiResponse && (\n              <Button\n                onClick={() => speakResponse(aiResponse)}\n                variant=\"outline\"\n                size=\"lg\"\n                disabled={isSpeaking}\n              >\n                🔊 Repeat\n              </Button>\n            )}\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"grid grid-cols-2 gap-2\">\n            {cardData.availableActions?.slice(0, 4).map((action, index) => (\n              <button\n                key={index}\n                onClick={() => handleVoiceInput(`Tell me about ${action.label.toLowerCase()}`)}\n                className=\"p-2 text-xs bg-surface-dark/50 border border-electric-purple/30 rounded-lg text-text-secondary hover:text-neon-blue hover:border-neon-blue/50 transition-colors\"\n              >\n                {action.icon} {action.label}\n              </button>\n            ))}\n          </div>\n\n          {/* Browser Support Warning */}\n          {typeof window !== 'undefined' && !('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window) && (\n            <div className=\"text-center text-hologram-pink text-sm\">\n              ⚠️ Voice recognition not supported in this browser\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default VoiceInteraction;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,aAAa,EAAE;;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,wCAAmC;gBACjC,qBAAqB;gBACrB,IAAI,6BAA6B,UAAU,uBAAuB,QAAQ;oBACxE,MAAM,oBAAoB,OAAO,iBAAiB,IAAI,OAAO,uBAAuB;oBACpF,eAAe,OAAO,GAAG,IAAI;oBAC7B,eAAe,OAAO,CAAC,UAAU,GAAG;oBACpC,eAAe,OAAO,CAAC,cAAc,GAAG;oBACxC,eAAe,OAAO,CAAC,IAAI,GAAG;oBAE9B,eAAe,OAAO,CAAC,QAAQ;sDAAG,CAAC;4BACjC,IAAI,kBAAkB;4BACtB,IAAK,IAAI,IAAI,MAAM,WAAW,EAAE,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE,IAAK;gCAC7D,IAAI,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;oCAC5B,mBAAmB,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU;gCACnD;4BACF;4BACA,IAAI,iBAAiB;gCACnB,cAAc;gCACd,iBAAiB;4BACnB;wBACF;;oBAEA,eAAe,OAAO,CAAC,OAAO;sDAAG,CAAC;4BAChC,QAAQ,KAAK,CAAC,6BAA6B,MAAM,KAAK;4BACtD,eAAe;wBACjB;;gBACF;gBAEA,mBAAmB;gBACnB,IAAI,qBAAqB,QAAQ;oBAC/B,SAAS,OAAO,GAAG,OAAO,eAAe;gBAC3C;YACF;QACF;qCAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,UAAU,WAAW;QACnC,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG;QAEjF,0BAA0B;QAC1B,IAAI,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,cAAc;YAClF,OAAO,cAAc,UAAU;QACjC;QAEA,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,eAAe;YAC7D,MAAM,UAAU,eAAe,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,gBAAgB,MAAM,EAAE;YACnF,OAAO,CAAC,yBAAyB,EAAE,QAAQ,2CAA2C,EAAE,cAAc,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9I;QAEA,IAAI,MAAM,QAAQ,CAAC,kBAAkB,MAAM,QAAQ,CAAC,mBAAmB;YACrE,MAAM,cAAc,kBAAkB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,mBAAmB,MAAM,EAAE;YAC7F,OAAO,CAAC,iCAAiC,EAAE,YAAY,uEAAuE,EAAE,cAAc,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/J;QAEA,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,eAAe,MAAM,QAAQ,CAAC,WAAW;YACpF,OAAO,CAAC,oIAAoI,CAAC;QAC/I;QAEA,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,UAAU;YACxD,OAAO,CAAC,yCAAyC,EAAE,SAAS,KAAK,CAAC,8EAA8E,CAAC;QACnJ;QAEA,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,SAAS;YACvD,OAAO,CAAC,UAAU,EAAE,SAAS,OAAO,CAAC,oBAAoB,EAAE,SAAS,QAAQ,CAAC,0CAA0C,EAAE,cAAc,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QACxJ;QAEA,yCAAyC;QACzC,MAAM,YAAY;YAChB,CAAC,qCAAqC,EAAE,cAAc,iBAAiB,CAAC,IAAI,CAAC,MAAM,yCAAyC,CAAC;YAC7H,CAAC,iCAAiC,EAAE,SAAS,QAAQ,CAAC,oBAAoB,EAAE,cAAc,SAAS,CAAC,EAAE,CAAC,sBAAsB,CAAC;YAC9H,CAAC,uEAAuE,EAAE,cAAc,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC9H;QAED,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;IAChE;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,OAAO;QAC9B,eAAe;QACf,gBAAgB;QAEhB,mCAAmC;QACnC,MAAM,cAAc;YAAE,MAAM;YAAQ,SAAS;YAAO,WAAW,KAAK,GAAG;QAAG;QAC1E,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAE9C,+BAA+B;QAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,uBAAuB;QACvB,MAAM,WAAW,mBAAmB;QACpC,cAAc;QAEd,kCAAkC;QAClC,MAAM,YAAY;YAAE,MAAM;YAAM,SAAS;YAAU,WAAW,KAAK,GAAG;QAAG;QACzE,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;aAAU;QAE5C,gBAAgB;QAEhB,qBAAqB;QACrB,cAAc;QAEd,+BAA+B;QAC/B,iBAAiB,cAAc;YAAE,MAAM;YAAqB;YAAO;QAAS;IAC9E;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,OAAO,IAAI,MAAM;YAC5B,cAAc;YACd,MAAM,YAAY,IAAI,yBAAyB;YAC/C,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,kCAAkC;YAClC,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,aACpB,MAAM,IAAI,CAAC,QAAQ,CAAC,gBACpB,MAAM,IAAI,CAAC,QAAQ,CAAC;YAEtB,IAAI,gBAAgB;gBAClB,UAAU,KAAK,GAAG;YACpB;YAEA,UAAU,KAAK,GAAG,IAAM,cAAc;YACtC,UAAU,OAAO,GAAG,IAAM,cAAc;YAExC,SAAS,OAAO,CAAC,KAAK,CAAC;QACzB;IACF;IAEA,uBAAuB;IACvB,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,eAAe,OAAO,EAAE;YACxB,eAAe;QACjB,OAAO;YACL,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,KAAK;gBAC5B,eAAe;gBACf,cAAc;YAChB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB;QACtB,MAAM,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACzC,6LAAC;gBAEC,WAAW,CAAC,0DAA0D,EACpE,cAAc,kBAAkB,IAChC;gBACF,OAAO;oBACL,QAAQ,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,EAAE,CAAC;oBACtC,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;gBAC/B;eAPK;;;;;QAWT,qBACE,6LAAC;YAAI,WAAU;sBACZ;;;;;;IAGP;IAEA,sBAAsB;IACtB,MAAM,WAAW,kBACf,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAW,CAAC,qCAAqC,EACpD,aAAa,mBACb,eAAe,kBACf,cAAc,iBAAiB,IAC/B;8BAAE;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAW,CAAC,qBAAqB,EACpC,aAAa,iCACb,eAAe,iCACf,cAAc,8BACd,wBACA;;;;;;;;;;;;;;;;;IAKR,qBACE,6LAAC,kIAAA,CAAA,OAAI;QAAC,SAAQ;QAAQ,WAAU;kBAC9B,cAAA,6LAAC,kIAAA,CAAA,cAAW;sBACV,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,6LAAC;gCAAE,WAAU;;oCAAsB;oCACxB,SAAS,IAAI;oCAAC;;;;;;;;;;;;;kCAK3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;;;;;;;;;oBAIF,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;;;;;;;;;kCAKL,6LAAC;wBAAI,WAAU;;4BACZ,6BACC,6LAAC;gCAAE,WAAU;0CAAiB;;;;;;4BAE/B,8BACC,6LAAC;gCAAE,WAAU;0CAAoB;;;;;;4BAElC,4BACC,6LAAC;gCAAE,WAAU;0CAAmB;;;;;;4BAEjC,CAAC,eAAe,CAAC,gBAAgB,CAAC,4BACjC,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;oBAKtC,aAAa,MAAM,GAAG,mBACrB,6LAAC;wBAAI,WAAU;kCACZ,aAAa,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,sBACpC,6LAAC;gCAEC,WAAW,CAAC,QAAQ,EAClB,QAAQ,IAAI,KAAK,SACb,8BACA,8BACJ;;kDAEF,6LAAC;;4CAAQ,QAAQ,IAAI,KAAK,SAAS,QAAQ,SAAS,IAAI;4CAAC;;;;;;;kDACzD,6LAAC;;;;;oCACA,QAAQ,OAAO;;+BATX;;;;;;;;;;kCAgBb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,UAAM;gCACL,SAAS;gCACT,SAAS,cAAc,WAAW;gCAClC,MAAK;gCACL,UAAU,gBAAgB;0CAEzB,cAAc,YAAY;;;;;;4BAG5B,4BACC,6LAAC,oIAAA,CAAA,UAAM;gCACL,SAAS,IAAM,cAAc;gCAC7B,SAAQ;gCACR,MAAK;gCACL,UAAU;0CACX;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;kCACZ,SAAS,gBAAgB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,sBACnD,6LAAC;gCAEC,SAAS,IAAM,iBAAiB,CAAC,cAAc,EAAE,OAAO,KAAK,CAAC,WAAW,IAAI;gCAC7E,WAAU;;oCAET,OAAO,IAAI;oCAAC;oCAAE,OAAO,KAAK;;+BAJtB;;;;;;;;;;oBAUV,aAAkB,eAAe,CAAC,CAAC,6BAA6B,MAAM,KAAK,CAAC,CAAC,uBAAuB,MAAM,mBACzG,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;;;;;;;;;;;;;;;;;AAQpE;GA5TM;KAAA;uCA8TS", "debugId": null}}, {"offset": {"line": 2133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/effects/TiltCard.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst TiltCard = ({ \n  children, \n  className = '',\n  tiltMaxAngleX = 15,\n  tiltMaxAngleY = 15,\n  perspective = 1000,\n  scale = 1.05,\n  transitionDuration = 400,\n  gyroscope = true,\n  glareEnable = true,\n  glareMaxOpacity = 0.7,\n  glareColor = '#ffffff',\n  glarePosition = 'bottom',\n  reset = true,\n  ...props \n}) => {\n  const cardRef = useRef(null);\n  const glareRef = useRef(null);\n  const [isHovered, setIsHovered] = useState(false);\n\n  useEffect(() => {\n    const card = cardRef.current;\n    const glare = glareRef.current;\n    \n    if (!card) return;\n\n    let requestId;\n    let timeout;\n\n    const handleMouseMove = (e) => {\n      if (!isHovered) return;\n\n      const rect = card.getBoundingClientRect();\n      const centerX = rect.left + rect.width / 2;\n      const centerY = rect.top + rect.height / 2;\n      \n      const mouseX = e.clientX - centerX;\n      const mouseY = e.clientY - centerY;\n      \n      const rotateX = (mouseY / (rect.height / 2)) * tiltMaxAngleX;\n      const rotateY = (mouseX / (rect.width / 2)) * tiltMaxAngleY;\n      \n      requestId = requestAnimationFrame(() => {\n        card.style.transform = `\n          perspective(${perspective}px) \n          rotateX(${-rotateX}deg) \n          rotateY(${rotateY}deg) \n          scale3d(${scale}, ${scale}, ${scale})\n        `;\n        \n        if (glare && glareEnable) {\n          const glareX = (mouseX / rect.width) * 100;\n          const glareY = (mouseY / rect.height) * 100;\n          const glareOpacity = Math.min(\n            Math.sqrt(mouseX * mouseX + mouseY * mouseY) / \n            Math.sqrt(rect.width * rect.width + rect.height * rect.height) * \n            glareMaxOpacity, \n            glareMaxOpacity\n          );\n          \n          glare.style.background = `\n            radial-gradient(\n              circle at ${50 + glareX}% ${50 + glareY}%, \n              ${glareColor} 0%, \n              transparent 50%\n            )\n          `;\n          glare.style.opacity = glareOpacity;\n        }\n      });\n    };\n\n    const handleMouseEnter = () => {\n      setIsHovered(true);\n      card.style.transition = `transform ${transitionDuration}ms cubic-bezier(0.03, 0.98, 0.52, 0.99)`;\n    };\n\n    const handleMouseLeave = () => {\n      setIsHovered(false);\n      \n      if (reset) {\n        card.style.transform = `\n          perspective(${perspective}px) \n          rotateX(0deg) \n          rotateY(0deg) \n          scale3d(1, 1, 1)\n        `;\n        \n        if (glare && glareEnable) {\n          glare.style.opacity = 0;\n        }\n      }\n      \n      timeout = setTimeout(() => {\n        card.style.transition = '';\n      }, transitionDuration);\n    };\n\n    // Gyroscope effect for mobile\n    const handleDeviceOrientation = (e) => {\n      if (!gyroscope || !isHovered) return;\n      \n      const rotateX = (e.beta - 90) * (tiltMaxAngleX / 90);\n      const rotateY = e.gamma * (tiltMaxAngleY / 90);\n      \n      requestId = requestAnimationFrame(() => {\n        card.style.transform = `\n          perspective(${perspective}px) \n          rotateX(${rotateX}deg) \n          rotateY(${rotateY}deg) \n          scale3d(${scale}, ${scale}, ${scale})\n        `;\n      });\n    };\n\n    card.addEventListener('mousemove', handleMouseMove);\n    card.addEventListener('mouseenter', handleMouseEnter);\n    card.addEventListener('mouseleave', handleMouseLeave);\n    \n    if (gyroscope && window.DeviceOrientationEvent) {\n      window.addEventListener('deviceorientation', handleDeviceOrientation);\n    }\n\n    return () => {\n      card.removeEventListener('mousemove', handleMouseMove);\n      card.removeEventListener('mouseenter', handleMouseEnter);\n      card.removeEventListener('mouseleave', handleMouseLeave);\n      \n      if (gyroscope && window.DeviceOrientationEvent) {\n        window.removeEventListener('deviceorientation', handleDeviceOrientation);\n      }\n      \n      if (requestId) {\n        cancelAnimationFrame(requestId);\n      }\n      \n      if (timeout) {\n        clearTimeout(timeout);\n      }\n    };\n  }, [\n    isHovered, \n    tiltMaxAngleX, \n    tiltMaxAngleY, \n    perspective, \n    scale, \n    transitionDuration, \n    gyroscope, \n    glareEnable, \n    glareMaxOpacity, \n    glareColor, \n    reset\n  ]);\n\n  return (\n    <div\n      ref={cardRef}\n      className={cn(\n        'relative transform-gpu',\n        className\n      )}\n      style={{\n        transformStyle: 'preserve-3d',\n      }}\n      {...props}\n    >\n      {/* Glare Effect */}\n      {glareEnable && (\n        <div\n          ref={glareRef}\n          className=\"absolute inset-0 pointer-events-none rounded-inherit\"\n          style={{\n            background: `radial-gradient(circle at 50% 50%, ${glareColor} 0%, transparent 50%)`,\n            opacity: 0,\n            transition: `opacity ${transitionDuration}ms ease-out`,\n            mixBlendMode: 'overlay',\n            zIndex: 1\n          }}\n        />\n      )}\n      \n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n      \n      {/* Shine Effect */}\n      <div \n        className=\"absolute inset-0 rounded-inherit opacity-0 hover:opacity-100 transition-opacity duration-500 pointer-events-none\"\n        style={{\n          background: 'linear-gradient(135deg, transparent 40%, rgba(255,255,255,0.1) 50%, transparent 60%)',\n          transform: 'translateX(-100%)',\n          animation: isHovered ? 'shine 1.5s ease-in-out infinite' : 'none'\n        }}\n      />\n      \n      {/* CSS Animation */}\n      <style jsx>{`\n        @keyframes shine {\n          0% { transform: translateX(-100%); }\n          50% { transform: translateX(100%); }\n          100% { transform: translateX(100%); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\n// Preset configurations\nexport const TiltPresets = {\n  subtle: {\n    tiltMaxAngleX: 5,\n    tiltMaxAngleY: 5,\n    scale: 1.02,\n    glareMaxOpacity: 0.3\n  },\n  \n  moderate: {\n    tiltMaxAngleX: 10,\n    tiltMaxAngleY: 10,\n    scale: 1.05,\n    glareMaxOpacity: 0.5\n  },\n  \n  dramatic: {\n    tiltMaxAngleX: 20,\n    tiltMaxAngleY: 20,\n    scale: 1.1,\n    glareMaxOpacity: 0.8\n  },\n  \n  card: {\n    tiltMaxAngleX: 15,\n    tiltMaxAngleY: 15,\n    scale: 1.05,\n    glareEnable: true,\n    glareMaxOpacity: 0.6,\n    perspective: 1000\n  },\n  \n  nft: {\n    tiltMaxAngleX: 25,\n    tiltMaxAngleY: 25,\n    scale: 1.08,\n    glareEnable: true,\n    glareMaxOpacity: 0.9,\n    perspective: 1200,\n    transitionDuration: 300\n  }\n};\n\n// Enhanced Tilt Card with preset support\nexport const EnhancedTiltCard = ({ preset, ...props }) => {\n  const presetConfig = preset ? TiltPresets[preset] : {};\n  const mergedProps = { ...presetConfig, ...props };\n  \n  return <TiltCard {...mergedProps} />;\n};\n\nexport default TiltCard;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;AAHA;;;;AAKA,MAAM,WAAW,CAAC,EAChB,QAAQ,EACR,YAAY,EAAE,EACd,gBAAgB,EAAE,EAClB,gBAAgB,EAAE,EAClB,cAAc,IAAI,EAClB,QAAQ,IAAI,EACZ,qBAAqB,GAAG,EACxB,YAAY,IAAI,EAChB,cAAc,IAAI,EAClB,kBAAkB,GAAG,EACrB,aAAa,SAAS,EACtB,gBAAgB,QAAQ,EACxB,QAAQ,IAAI,EACZ,GAAG,OACJ;;IACC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,OAAO,QAAQ,OAAO;YAC5B,MAAM,QAAQ,SAAS,OAAO;YAE9B,IAAI,CAAC,MAAM;YAEX,IAAI;YACJ,IAAI;YAEJ,MAAM;sDAAkB,CAAC;oBACvB,IAAI,CAAC,WAAW;oBAEhB,MAAM,OAAO,KAAK,qBAAqB;oBACvC,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;oBACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;oBAEzC,MAAM,SAAS,EAAE,OAAO,GAAG;oBAC3B,MAAM,SAAS,EAAE,OAAO,GAAG;oBAE3B,MAAM,UAAU,AAAC,SAAS,CAAC,KAAK,MAAM,GAAG,CAAC,IAAK;oBAC/C,MAAM,UAAU,AAAC,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC,IAAK;oBAE9C,YAAY;8DAAsB;4BAChC,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC;sBACV,EAAE,YAAY;kBAClB,EAAE,CAAC,QAAQ;kBACX,EAAE,QAAQ;kBACV,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM;QACtC,CAAC;4BAED,IAAI,SAAS,aAAa;gCACxB,MAAM,SAAS,AAAC,SAAS,KAAK,KAAK,GAAI;gCACvC,MAAM,SAAS,AAAC,SAAS,KAAK,MAAM,GAAI;gCACxC,MAAM,eAAe,KAAK,GAAG,CAC3B,KAAK,IAAI,CAAC,SAAS,SAAS,SAAS,UACrC,KAAK,IAAI,CAAC,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,IAC7D,iBACA;gCAGF,MAAM,KAAK,CAAC,UAAU,GAAG,CAAC;;wBAEZ,EAAE,KAAK,OAAO,EAAE,EAAE,KAAK,OAAO;cACxC,EAAE,WAAW;;;UAGjB,CAAC;gCACD,MAAM,KAAK,CAAC,OAAO,GAAG;4BACxB;wBACF;;gBACF;;YAEA,MAAM;uDAAmB;oBACvB,aAAa;oBACb,KAAK,KAAK,CAAC,UAAU,GAAG,CAAC,UAAU,EAAE,mBAAmB,uCAAuC,CAAC;gBAClG;;YAEA,MAAM;uDAAmB;oBACvB,aAAa;oBAEb,IAAI,OAAO;wBACT,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC;sBACV,EAAE,YAAY;;;;QAI5B,CAAC;wBAED,IAAI,SAAS,aAAa;4BACxB,MAAM,KAAK,CAAC,OAAO,GAAG;wBACxB;oBACF;oBAEA,UAAU;+DAAW;4BACnB,KAAK,KAAK,CAAC,UAAU,GAAG;wBAC1B;8DAAG;gBACL;;YAEA,8BAA8B;YAC9B,MAAM;8DAA0B,CAAC;oBAC/B,IAAI,CAAC,aAAa,CAAC,WAAW;oBAE9B,MAAM,UAAU,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC,gBAAgB,EAAE;oBACnD,MAAM,UAAU,EAAE,KAAK,GAAG,CAAC,gBAAgB,EAAE;oBAE7C,YAAY;sEAAsB;4BAChC,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC;sBACV,EAAE,YAAY;kBAClB,EAAE,QAAQ;kBACV,EAAE,QAAQ;kBACV,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM;QACtC,CAAC;wBACH;;gBACF;;YAEA,KAAK,gBAAgB,CAAC,aAAa;YACnC,KAAK,gBAAgB,CAAC,cAAc;YACpC,KAAK,gBAAgB,CAAC,cAAc;YAEpC,IAAI,aAAa,OAAO,sBAAsB,EAAE;gBAC9C,OAAO,gBAAgB,CAAC,qBAAqB;YAC/C;YAEA;sCAAO;oBACL,KAAK,mBAAmB,CAAC,aAAa;oBACtC,KAAK,mBAAmB,CAAC,cAAc;oBACvC,KAAK,mBAAmB,CAAC,cAAc;oBAEvC,IAAI,aAAa,OAAO,sBAAsB,EAAE;wBAC9C,OAAO,mBAAmB,CAAC,qBAAqB;oBAClD;oBAEA,IAAI,WAAW;wBACb,qBAAqB;oBACvB;oBAEA,IAAI,SAAS;wBACX,aAAa;oBACf;gBACF;;QACF;6BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QACC,KAAK;QAKL,OAAO;YACL,gBAAgB;QAClB;QACC,GAAG,KAAK;mDAAL,SAAA,2BAAA,mBAPO,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0BACA;;YAQD,6BACC,6LAAC;gBACC,KAAK;gBAEL,OAAO;oBACL,YAAY,CAAC,mCAAmC,EAAE,WAAW,qBAAqB,CAAC;oBACnF,SAAS;oBACT,YAAY,CAAC,QAAQ,EAAE,mBAAmB,WAAW,CAAC;oBACtD,cAAc;oBACd,QAAQ;gBACV;0DAPU;;;;;;0BAYd,6LAAC;0DAAc;0BACZ;;;;;;0BAIH,6LAAC;gBAEC,OAAO;oBACL,YAAY;oBACZ,WAAW;oBACX,WAAW,YAAY,oCAAoC;gBAC7D;0DALU;;;;;;;;;;;;;;;;AAkBlB;GA9MM;KAAA;AAiNC,MAAM,cAAc;IACzB,QAAQ;QACN,eAAe;QACf,eAAe;QACf,OAAO;QACP,iBAAiB;IACnB;IAEA,UAAU;QACR,eAAe;QACf,eAAe;QACf,OAAO;QACP,iBAAiB;IACnB;IAEA,UAAU;QACR,eAAe;QACf,eAAe;QACf,OAAO;QACP,iBAAiB;IACnB;IAEA,MAAM;QACJ,eAAe;QACf,eAAe;QACf,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,aAAa;IACf;IAEA,KAAK;QACH,eAAe;QACf,eAAe;QACf,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,aAAa;QACb,oBAAoB;IACtB;AACF;AAGO,MAAM,mBAAmB,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO;IACnD,MAAM,eAAe,SAAS,WAAW,CAAC,OAAO,GAAG,CAAC;IACrD,MAAM,cAAc;QAAE,GAAG,YAAY;QAAE,GAAG,KAAK;IAAC;IAEhD,qBAAO,6LAAC;QAAU,GAAG,WAAW;;;;;;AAClC;MALa;uCAOE", "debugId": null}}, {"offset": {"line": 2397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/data/sampleCards.js"], "sourcesContent": ["// Advanced AI-Powered Business Card Data\n\nexport const sampleCards = [\n  {\n    id: 'ai-ceo-001',\n    name: 'Dr. <PERSON>',\n    title: 'CEO & AI Research Director',\n    company: 'QuantumMind Technologies',\n    industry: 'Artificial Intelligence',\n    location: 'San Francisco, CA',\n    \n    // Contact Information\n    email: '<EMAIL>',\n    phone: '+****************',\n    website: 'quantummind.ai',\n    linkedin: 'linkedin.com/in/sarahchen-ai',\n    twitter: '@sarahchen_ai',\n    \n    // Visual Identity\n    avatar: '🧠',\n    profileImage: '/avatars/sarah-chen.jpg',\n    companyLogo: '/logos/quantummind.svg',\n    \n    // AI-Enhanced Features\n    aiPersonality: {\n      voiceIntro: 'Hi! I\\'m <PERSON>, and I\\'m passionate about building AI that enhances human potential.',\n      personalityTraits: ['Innovative', 'Analytical', 'Visionary', 'Collaborative'],\n      communicationStyle: 'Direct and inspiring',\n      interests: ['Machine Learning', 'Quantum Computing', 'Sustainable Tech', 'Mentoring']\n    },\n    \n    // Dynamic Content\n    recentAchievements: [\n      'Led breakthrough in quantum-classical hybrid AI models',\n      'Raised $50M Series B for QuantumMind Technologies',\n      'Keynote speaker at AI Summit 2024',\n      'Published 15 papers in top-tier AI journals'\n    ],\n    \n    currentProjects: [\n      'Quantum Neural Networks for Drug Discovery',\n      'AI Ethics Framework for Enterprise',\n      'Next-gen Natural Language Processing'\n    ],\n    \n    // Interactive Elements\n    availableActions: [\n      { type: 'schedule_meeting', label: 'Schedule a Meeting', icon: '📅' },\n      { type: 'view_portfolio', label: 'View Research Portfolio', icon: '📊' },\n      { type: 'connect_linkedin', label: 'Connect on LinkedIn', icon: '🔗' },\n      { type: 'download_whitepaper', label: 'Download Latest Whitepaper', icon: '📄' },\n      { type: 'join_newsletter', label: 'Join AI Newsletter', icon: '📧' }\n    ],\n    \n    // AR/3D Customization\n    cardTheme: {\n      primary: '#00f5ff',\n      secondary: '#8b5cf6',\n      accent: '#ff006e',\n      background: 'quantum-gradient',\n      effects: ['neural-network', 'particle-flow', 'hologram-glitch'],\n      animation: 'quantum-float'\n    },\n    \n    // Smart Features\n    smartFeatures: {\n      autoTranslate: true,\n      voiceActivated: true,\n      contextAware: true,\n      realTimeUpdates: true,\n      aiRecommendations: true\n    },\n    \n    // Analytics & Insights\n    cardAnalytics: {\n      totalViews: 15420,\n      uniqueConnections: 892,\n      engagementRate: 78,\n      topInteractions: ['schedule_meeting', 'view_portfolio', 'connect_linkedin']\n    },\n    \n    // Verification & Trust\n    verification: {\n      verified: true,\n      verificationLevel: 'Enterprise',\n      badges: ['AI Expert', 'Thought Leader', 'Verified CEO', 'Speaker'],\n      trustScore: 98\n    }\n  },\n  \n  {\n    id: 'creative-director-002',\n    name: 'Marcus Rodriguez',\n    title: 'Creative Director & Digital Artist',\n    company: 'Neon Studios',\n    industry: 'Digital Design',\n    location: 'New York, NY',\n    \n    email: '<EMAIL>',\n    phone: '+****************',\n    website: 'neonstudios.com',\n    instagram: '@marcusrodriguez_art',\n    behance: 'behance.net/marcusrodriguez',\n    \n    avatar: '🎨',\n    profileImage: '/avatars/marcus-rodriguez.jpg',\n    companyLogo: '/logos/neon-studios.svg',\n    \n    aiPersonality: {\n      voiceIntro: 'Hey there! I\\'m Marcus, and I create digital experiences that blur the line between reality and imagination.',\n      personalityTraits: ['Creative', 'Bold', 'Experimental', 'Collaborative'],\n      communicationStyle: 'Energetic and visual',\n      interests: ['Digital Art', 'AR/VR Design', 'Generative AI', 'Interactive Media']\n    },\n    \n    recentAchievements: [\n      'Won Cannes Lions Gold for AR Campaign',\n      'Featured in Adobe\\'s Creative Spotlight',\n      'Collaborated with major brands on metaverse experiences',\n      'Exhibited at Digital Art Museum NYC'\n    ],\n    \n    currentProjects: [\n      'AI-Generated Art Installation',\n      'Immersive Brand Experience for Fashion Week',\n      'NFT Collection with Environmental Theme'\n    ],\n    \n    availableActions: [\n      { type: 'view_portfolio', label: 'View Creative Portfolio', icon: '🎨' },\n      { type: 'commission_work', label: 'Commission Artwork', icon: '💼' },\n      { type: 'collaborate', label: 'Discuss Collaboration', icon: '🤝' },\n      { type: 'follow_instagram', label: 'Follow on Instagram', icon: '📸' },\n      { type: 'book_consultation', label: 'Book Design Consultation', icon: '💡' }\n    ],\n    \n    cardTheme: {\n      primary: '#ff0080',\n      secondary: '#8000ff',\n      accent: '#00ffff',\n      background: 'neon-gradient',\n      effects: ['color-shift', 'paint-splash', 'digital-glitch'],\n      animation: 'creative-pulse'\n    },\n    \n    smartFeatures: {\n      autoTranslate: true,\n      voiceActivated: false,\n      contextAware: true,\n      realTimeUpdates: true,\n      aiRecommendations: true\n    },\n    \n    cardAnalytics: {\n      totalViews: 8750,\n      uniqueConnections: 445,\n      engagementRate: 85,\n      topInteractions: ['view_portfolio', 'follow_instagram', 'commission_work']\n    },\n    \n    verification: {\n      verified: true,\n      verificationLevel: 'Creative Professional',\n      badges: ['Award Winner', 'Featured Artist', 'Verified Creator'],\n      trustScore: 94\n    }\n  },\n  \n  {\n    id: 'blockchain-dev-003',\n    name: 'Alex Kim',\n    title: 'Senior Blockchain Developer',\n    company: 'CryptoForge Labs',\n    industry: 'Blockchain Technology',\n    location: 'Austin, TX',\n    \n    email: '<EMAIL>',\n    phone: '+****************',\n    website: 'cryptoforge.dev',\n    github: 'github.com/alexkim-blockchain',\n    twitter: '@alexkim_crypto',\n    \n    avatar: '⛓️',\n    profileImage: '/avatars/alex-kim.jpg',\n    companyLogo: '/logos/cryptoforge.svg',\n    \n    aiPersonality: {\n      voiceIntro: 'Hello! I\\'m Alex, and I build the decentralized infrastructure that powers the future of finance.',\n      personalityTraits: ['Technical', 'Precise', 'Forward-thinking', 'Security-focused'],\n      communicationStyle: 'Technical but approachable',\n      interests: ['DeFi Protocols', 'Smart Contracts', 'Cryptography', 'Web3 Gaming']\n    },\n    \n    recentAchievements: [\n      'Deployed $100M+ TVL DeFi protocol',\n      'Discovered critical vulnerability in major DEX',\n      'Open-sourced revolutionary consensus algorithm',\n      'Speaker at Ethereum Developer Conference'\n    ],\n    \n    currentProjects: [\n      'Layer 2 Scaling Solution for NFTs',\n      'Cross-chain Bridge Protocol',\n      'Zero-Knowledge Privacy Tools'\n    ],\n    \n    availableActions: [\n      { type: 'view_github', label: 'View GitHub Profile', icon: '💻' },\n      { type: 'technical_discussion', label: 'Technical Discussion', icon: '🔧' },\n      { type: 'code_review', label: 'Request Code Review', icon: '👀' },\n      { type: 'join_project', label: 'Collaborate on Project', icon: '🚀' },\n      { type: 'follow_twitter', label: 'Follow on Twitter', icon: '🐦' }\n    ],\n    \n    cardTheme: {\n      primary: '#00ff41',\n      secondary: '#008f11',\n      accent: '#00ff88',\n      background: 'matrix-gradient',\n      effects: ['code-rain', 'blockchain-nodes', 'crypto-pulse'],\n      animation: 'tech-float'\n    },\n    \n    smartFeatures: {\n      autoTranslate: true,\n      voiceActivated: true,\n      contextAware: true,\n      realTimeUpdates: true,\n      aiRecommendations: true\n    },\n    \n    cardAnalytics: {\n      totalViews: 12300,\n      uniqueConnections: 678,\n      engagementRate: 72,\n      topInteractions: ['view_github', 'technical_discussion', 'follow_twitter']\n    },\n    \n    verification: {\n      verified: true,\n      verificationLevel: 'Technical Expert',\n      badges: ['Blockchain Expert', 'Security Researcher', 'Open Source Contributor'],\n      trustScore: 96\n    }\n  },\n\n  {\n    id: 'marketing-guru-004',\n    name: 'Isabella Martinez',\n    title: 'Chief Marketing Officer',\n    company: 'GrowthHack Labs',\n    industry: 'Digital Marketing',\n    location: 'Miami, FL',\n\n    email: '<EMAIL>',\n    phone: '+****************',\n    website: 'growthhack.com',\n    linkedin: 'linkedin.com/in/isabella-martinez',\n    instagram: '@isabella_growth',\n\n    avatar: '📈',\n    profileImage: '/avatars/isabella-martinez.jpg',\n    companyLogo: '/logos/growthhack.svg',\n\n    aiPersonality: {\n      voiceIntro: 'Hi! I\\'m Isabella, and I turn data into growth stories that captivate audiences and drive results.',\n      personalityTraits: ['Strategic', 'Creative', 'Data-driven', 'Energetic'],\n      communicationStyle: 'Enthusiastic and results-focused',\n      interests: ['Growth Hacking', 'Content Strategy', 'Analytics', 'Brand Building']\n    },\n\n    recentAchievements: [\n      'Scaled startup from 0 to 1M users in 18 months',\n      'Won Marketing Campaign of the Year 2024',\n      'Generated $50M in revenue through viral campaigns',\n      'Built marketing team from 2 to 25 professionals'\n    ],\n\n    currentProjects: [\n      'AI-Powered Content Generation Platform',\n      'Omnichannel Customer Journey Optimization',\n      'Influencer Marketing Automation Tool'\n    ],\n\n    availableActions: [\n      { type: 'growth_consultation', label: 'Growth Strategy Session', icon: '🚀' },\n      { type: 'case_study', label: 'View Case Studies', icon: '📊' },\n      { type: 'marketing_audit', label: 'Free Marketing Audit', icon: '🔍' },\n      { type: 'connect_linkedin', label: 'Connect on LinkedIn', icon: '🔗' },\n      { type: 'follow_instagram', label: 'Follow on Instagram', icon: '📸' }\n    ],\n\n    cardTheme: {\n      primary: '#ff0080',\n      secondary: '#ff6b35',\n      accent: '#ffd700',\n      background: 'sunset-gradient',\n      effects: ['growth-chart', 'data-flow', 'success-sparkles'],\n      animation: 'growth-pulse'\n    },\n\n    smartFeatures: {\n      autoTranslate: true,\n      voiceActivated: true,\n      contextAware: true,\n      realTimeUpdates: true,\n      aiRecommendations: true\n    },\n\n    cardAnalytics: {\n      totalViews: 18750,\n      uniqueConnections: 1240,\n      engagementRate: 89,\n      topInteractions: ['growth_consultation', 'case_study', 'marketing_audit']\n    },\n\n    verification: {\n      verified: true,\n      verificationLevel: 'Marketing Expert',\n      badges: ['Growth Hacker', 'Campaign Master', 'Analytics Pro'],\n      trustScore: 97\n    }\n  },\n\n  {\n    id: 'startup-founder-005',\n    name: 'David Park',\n    title: 'Founder & CEO',\n    company: 'EcoTech Innovations',\n    industry: 'Clean Technology',\n    location: 'Seattle, WA',\n\n    email: '<EMAIL>',\n    phone: '+****************',\n    website: 'ecotech.io',\n    linkedin: 'linkedin.com/in/davidpark-ecotech',\n    twitter: '@davidpark_eco',\n\n    avatar: '🌱',\n    profileImage: '/avatars/david-park.jpg',\n    companyLogo: '/logos/ecotech.svg',\n\n    aiPersonality: {\n      voiceIntro: 'Hello! I\\'m David, and I\\'m building technology that makes our planet more sustainable, one innovation at a time.',\n      personalityTraits: ['Visionary', 'Sustainable', 'Innovative', 'Purpose-driven'],\n      communicationStyle: 'Passionate and mission-focused',\n      interests: ['Clean Energy', 'Sustainability', 'Climate Tech', 'Social Impact']\n    },\n\n    recentAchievements: [\n      'Raised $25M Series A for clean energy solutions',\n      'Patent holder for revolutionary solar technology',\n      'Named in Forbes 30 Under 30 for Energy',\n      'Reduced carbon footprint by 1M tons through innovations'\n    ],\n\n    currentProjects: [\n      'Next-Gen Solar Panel Technology',\n      'Carbon Capture AI System',\n      'Sustainable Manufacturing Platform'\n    ],\n\n    availableActions: [\n      { type: 'pitch_meeting', label: 'Schedule Pitch Meeting', icon: '🎯' },\n      { type: 'partnership', label: 'Explore Partnership', icon: '🤝' },\n      { type: 'investment_deck', label: 'View Investment Deck', icon: '💼' },\n      { type: 'sustainability_report', label: 'Impact Report', icon: '🌍' },\n      { type: 'follow_journey', label: 'Follow Our Journey', icon: '📱' }\n    ],\n\n    cardTheme: {\n      primary: '#00ff88',\n      secondary: '#00cc6a',\n      accent: '#66ff99',\n      background: 'eco-gradient',\n      effects: ['leaf-particles', 'energy-flow', 'growth-animation'],\n      animation: 'eco-pulse'\n    },\n\n    smartFeatures: {\n      autoTranslate: true,\n      voiceActivated: true,\n      contextAware: true,\n      realTimeUpdates: true,\n      aiRecommendations: true\n    },\n\n    cardAnalytics: {\n      totalViews: 9850,\n      uniqueConnections: 567,\n      engagementRate: 92,\n      topInteractions: ['pitch_meeting', 'partnership', 'sustainability_report']\n    },\n\n    verification: {\n      verified: true,\n      verificationLevel: 'Startup Founder',\n      badges: ['Climate Leader', 'Innovation Award', 'Forbes 30 Under 30'],\n      trustScore: 95\n    }\n  }\n];\n\n// AI-powered card recommendation engine\nexport const getRecommendedCards = (userInterests = [], industry = '') => {\n  return sampleCards.filter(card => {\n    const cardInterests = card.aiPersonality.interests.map(i => i.toLowerCase());\n    const matchingInterests = userInterests.filter(interest => \n      cardInterests.some(cardInterest => \n        cardInterest.includes(interest.toLowerCase()) || \n        interest.toLowerCase().includes(cardInterest)\n      )\n    );\n    \n    const industryMatch = industry && card.industry.toLowerCase().includes(industry.toLowerCase());\n    \n    return matchingInterests.length > 0 || industryMatch;\n  });\n};\n\n// Dynamic card content generator\nexport const generateDynamicContent = (cardId, context = {}) => {\n  const card = sampleCards.find(c => c.id === cardId);\n  if (!card) return null;\n  \n  const { timeOfDay, userLocation, userIndustry } = context;\n  \n  // Generate contextual greeting\n  let greeting = card.aiPersonality.voiceIntro;\n  if (timeOfDay === 'morning') {\n    greeting = `Good morning! ${greeting}`;\n  } else if (timeOfDay === 'evening') {\n    greeting = `Good evening! ${greeting}`;\n  }\n  \n  // Add location-based content\n  if (userLocation && userLocation !== card.location) {\n    greeting += ` I see you're connecting from ${userLocation} - I'd love to learn more about your local tech scene!`;\n  }\n  \n  // Industry-specific recommendations\n  const relevantProjects = card.currentProjects.filter(project => \n    userIndustry && project.toLowerCase().includes(userIndustry.toLowerCase())\n  );\n  \n  return {\n    ...card,\n    dynamicGreeting: greeting,\n    relevantProjects,\n    contextualActions: card.availableActions.slice(0, 3) // Show top 3 most relevant actions\n  };\n};\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;AAElC,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QAEV,sBAAsB;QACtB,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,SAAS;QAET,kBAAkB;QAClB,QAAQ;QACR,cAAc;QACd,aAAa;QAEb,uBAAuB;QACvB,eAAe;YACb,YAAY;YACZ,mBAAmB;gBAAC;gBAAc;gBAAc;gBAAa;aAAgB;YAC7E,oBAAoB;YACpB,WAAW;gBAAC;gBAAoB;gBAAqB;gBAAoB;aAAY;QACvF;QAEA,kBAAkB;QAClB,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QAED,iBAAiB;YACf;YACA;YACA;SACD;QAED,uBAAuB;QACvB,kBAAkB;YAChB;gBAAE,MAAM;gBAAoB,OAAO;gBAAsB,MAAM;YAAK;YACpE;gBAAE,MAAM;gBAAkB,OAAO;gBAA2B,MAAM;YAAK;YACvE;gBAAE,MAAM;gBAAoB,OAAO;gBAAuB,MAAM;YAAK;YACrE;gBAAE,MAAM;gBAAuB,OAAO;gBAA8B,MAAM;YAAK;YAC/E;gBAAE,MAAM;gBAAmB,OAAO;gBAAsB,MAAM;YAAK;SACpE;QAED,sBAAsB;QACtB,WAAW;YACT,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAC;gBAAkB;gBAAiB;aAAkB;YAC/D,WAAW;QACb;QAEA,iBAAiB;QACjB,eAAe;YACb,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;QACrB;QAEA,uBAAuB;QACvB,eAAe;YACb,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;gBAAC;gBAAoB;gBAAkB;aAAmB;QAC7E;QAEA,uBAAuB;QACvB,cAAc;YACZ,UAAU;YACV,mBAAmB;YACnB,QAAQ;gBAAC;gBAAa;gBAAkB;gBAAgB;aAAU;YAClE,YAAY;QACd;IACF;IAEA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QAEV,OAAO;QACP,OAAO;QACP,SAAS;QACT,WAAW;QACX,SAAS;QAET,QAAQ;QACR,cAAc;QACd,aAAa;QAEb,eAAe;YACb,YAAY;YACZ,mBAAmB;gBAAC;gBAAY;gBAAQ;gBAAgB;aAAgB;YACxE,oBAAoB;YACpB,WAAW;gBAAC;gBAAe;gBAAgB;gBAAiB;aAAoB;QAClF;QAEA,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QAED,iBAAiB;YACf;YACA;YACA;SACD;QAED,kBAAkB;YAChB;gBAAE,MAAM;gBAAkB,OAAO;gBAA2B,MAAM;YAAK;YACvE;gBAAE,MAAM;gBAAmB,OAAO;gBAAsB,MAAM;YAAK;YACnE;gBAAE,MAAM;gBAAe,OAAO;gBAAyB,MAAM;YAAK;YAClE;gBAAE,MAAM;gBAAoB,OAAO;gBAAuB,MAAM;YAAK;YACrE;gBAAE,MAAM;gBAAqB,OAAO;gBAA4B,MAAM;YAAK;SAC5E;QAED,WAAW;YACT,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAC;gBAAe;gBAAgB;aAAiB;YAC1D,WAAW;QACb;QAEA,eAAe;YACb,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;QACrB;QAEA,eAAe;YACb,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;gBAAC;gBAAkB;gBAAoB;aAAkB;QAC5E;QAEA,cAAc;YACZ,UAAU;YACV,mBAAmB;YACnB,QAAQ;gBAAC;gBAAgB;gBAAmB;aAAmB;YAC/D,YAAY;QACd;IACF;IAEA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QAEV,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QAET,QAAQ;QACR,cAAc;QACd,aAAa;QAEb,eAAe;YACb,YAAY;YACZ,mBAAmB;gBAAC;gBAAa;gBAAW;gBAAoB;aAAmB;YACnF,oBAAoB;YACpB,WAAW;gBAAC;gBAAkB;gBAAmB;gBAAgB;aAAc;QACjF;QAEA,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QAED,iBAAiB;YACf;YACA;YACA;SACD;QAED,kBAAkB;YAChB;gBAAE,MAAM;gBAAe,OAAO;gBAAuB,MAAM;YAAK;YAChE;gBAAE,MAAM;gBAAwB,OAAO;gBAAwB,MAAM;YAAK;YAC1E;gBAAE,MAAM;gBAAe,OAAO;gBAAuB,MAAM;YAAK;YAChE;gBAAE,MAAM;gBAAgB,OAAO;gBAA0B,MAAM;YAAK;YACpE;gBAAE,MAAM;gBAAkB,OAAO;gBAAqB,MAAM;YAAK;SAClE;QAED,WAAW;YACT,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAC;gBAAa;gBAAoB;aAAe;YAC1D,WAAW;QACb;QAEA,eAAe;YACb,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;QACrB;QAEA,eAAe;YACb,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;gBAAC;gBAAe;gBAAwB;aAAiB;QAC5E;QAEA,cAAc;YACZ,UAAU;YACV,mBAAmB;YACnB,QAAQ;gBAAC;gBAAqB;gBAAuB;aAA0B;YAC/E,YAAY;QACd;IACF;IAEA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QAEV,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;QAEX,QAAQ;QACR,cAAc;QACd,aAAa;QAEb,eAAe;YACb,YAAY;YACZ,mBAAmB;gBAAC;gBAAa;gBAAY;gBAAe;aAAY;YACxE,oBAAoB;YACpB,WAAW;gBAAC;gBAAkB;gBAAoB;gBAAa;aAAiB;QAClF;QAEA,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QAED,iBAAiB;YACf;YACA;YACA;SACD;QAED,kBAAkB;YAChB;gBAAE,MAAM;gBAAuB,OAAO;gBAA2B,MAAM;YAAK;YAC5E;gBAAE,MAAM;gBAAc,OAAO;gBAAqB,MAAM;YAAK;YAC7D;gBAAE,MAAM;gBAAmB,OAAO;gBAAwB,MAAM;YAAK;YACrE;gBAAE,MAAM;gBAAoB,OAAO;gBAAuB,MAAM;YAAK;YACrE;gBAAE,MAAM;gBAAoB,OAAO;gBAAuB,MAAM;YAAK;SACtE;QAED,WAAW;YACT,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAC;gBAAgB;gBAAa;aAAmB;YAC1D,WAAW;QACb;QAEA,eAAe;YACb,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;QACrB;QAEA,eAAe;YACb,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;gBAAC;gBAAuB;gBAAc;aAAkB;QAC3E;QAEA,cAAc;YACZ,UAAU;YACV,mBAAmB;YACnB,QAAQ;gBAAC;gBAAiB;gBAAmB;aAAgB;YAC7D,YAAY;QACd;IACF;IAEA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QAEV,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,SAAS;QAET,QAAQ;QACR,cAAc;QACd,aAAa;QAEb,eAAe;YACb,YAAY;YACZ,mBAAmB;gBAAC;gBAAa;gBAAe;gBAAc;aAAiB;YAC/E,oBAAoB;YACpB,WAAW;gBAAC;gBAAgB;gBAAkB;gBAAgB;aAAgB;QAChF;QAEA,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QAED,iBAAiB;YACf;YACA;YACA;SACD;QAED,kBAAkB;YAChB;gBAAE,MAAM;gBAAiB,OAAO;gBAA0B,MAAM;YAAK;YACrE;gBAAE,MAAM;gBAAe,OAAO;gBAAuB,MAAM;YAAK;YAChE;gBAAE,MAAM;gBAAmB,OAAO;gBAAwB,MAAM;YAAK;YACrE;gBAAE,MAAM;gBAAyB,OAAO;gBAAiB,MAAM;YAAK;YACpE;gBAAE,MAAM;gBAAkB,OAAO;gBAAsB,MAAM;YAAK;SACnE;QAED,WAAW;YACT,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAC;gBAAkB;gBAAe;aAAmB;YAC9D,WAAW;QACb;QAEA,eAAe;YACb,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;QACrB;QAEA,eAAe;YACb,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;gBAAC;gBAAiB;gBAAe;aAAwB;QAC5E;QAEA,cAAc;YACZ,UAAU;YACV,mBAAmB;YACnB,QAAQ;gBAAC;gBAAkB;gBAAoB;aAAqB;YACpE,YAAY;QACd;IACF;CACD;AAGM,MAAM,sBAAsB,CAAC,gBAAgB,EAAE,EAAE,WAAW,EAAE;IACnE,OAAO,YAAY,MAAM,CAAC,CAAA;QACxB,MAAM,gBAAgB,KAAK,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;QACzE,MAAM,oBAAoB,cAAc,MAAM,CAAC,CAAA,WAC7C,cAAc,IAAI,CAAC,CAAA,eACjB,aAAa,QAAQ,CAAC,SAAS,WAAW,OAC1C,SAAS,WAAW,GAAG,QAAQ,CAAC;QAIpC,MAAM,gBAAgB,YAAY,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;QAE3F,OAAO,kBAAkB,MAAM,GAAG,KAAK;IACzC;AACF;AAGO,MAAM,yBAAyB,CAAC,QAAQ,UAAU,CAAC,CAAC;IACzD,MAAM,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC5C,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;IAElD,+BAA+B;IAC/B,IAAI,WAAW,KAAK,aAAa,CAAC,UAAU;IAC5C,IAAI,cAAc,WAAW;QAC3B,WAAW,CAAC,cAAc,EAAE,UAAU;IACxC,OAAO,IAAI,cAAc,WAAW;QAClC,WAAW,CAAC,cAAc,EAAE,UAAU;IACxC;IAEA,6BAA6B;IAC7B,IAAI,gBAAgB,iBAAiB,KAAK,QAAQ,EAAE;QAClD,YAAY,CAAC,8BAA8B,EAAE,aAAa,sDAAsD,CAAC;IACnH;IAEA,oCAAoC;IACpC,MAAM,mBAAmB,KAAK,eAAe,CAAC,MAAM,CAAC,CAAA,UACnD,gBAAgB,QAAQ,WAAW,GAAG,QAAQ,CAAC,aAAa,WAAW;IAGzE,OAAO;QACL,GAAG,IAAI;QACP,iBAAiB;QACjB;QACA,mBAAmB,KAAK,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,mCAAmC;IAC1F;AACF", "debugId": null}}, {"offset": {"line": 3001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/app/demo/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { generateQRCode } from '@/lib/utils';\nimport SimpleCard3D from '@/components/3d/SimpleCard3D';\nimport SimpleCarousel from '@/components/3d/SimpleCarousel';\nimport ARSimulation from '@/components/3d/ARSimulation';\nimport VoiceInteraction from '@/components/ai/VoiceInteraction';\nimport TiltCard, { EnhancedTiltCard } from '@/components/effects/TiltCard';\nimport { sampleCards, generateDynamicContent } from '@/data/sampleCards';\n\nexport default function DemoPage() {\n  const [activeDemo, setActiveDemo] = useState('live-showcase');\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [isScanning, setIsScanning] = useState(false);\n  const [scannedCard, setScannedCard] = useState(null);\n  const [selectedCard, setSelectedCard] = useState(sampleCards[0]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [cameraActive, setCameraActive] = useState(false);\n  const [voiceActive, setVoiceActive] = useState(false);\n  const videoRef = useRef(null);\n\n  // Generate QR code on component mount\n  useEffect(() => {\n    const generateQR = async () => {\n      const cardUrl = `https://namecardai.com/card/${selectedCard.id}`;\n      const qrUrl = await generateQRCode(cardUrl);\n      if (qrUrl) {\n        setQrCodeUrl(qrUrl);\n      }\n    };\n    generateQR();\n  }, [selectedCard.id]);\n\n  const demos = [\n    {\n      id: 'live-showcase',\n      title: '🌟 Live Card Showcase',\n      description: 'See 5 finished AI business cards in action - ready to use!',\n      icon: '🎭',\n      featured: true,\n      priority: 1\n    },\n    {\n      id: 'ai-showcase',\n      title: 'AI-Powered Cards',\n      description: 'Experience next-level AI business cards with voice interaction',\n      icon: '🤖',\n      featured: true,\n      priority: 2\n    },\n    {\n      id: '3d-hologram',\n      title: '3D Holographic Cards',\n      description: 'Immersive 3D business cards with real-time animations',\n      icon: '🎯',\n      featured: true\n    },\n    {\n      id: 'ar-simulation',\n      title: 'AR Face Recognition',\n      description: 'Advanced AR overlay with face detection and card matching',\n      icon: '👁️',\n      featured: true\n    },\n    {\n      id: 'smart-search',\n      title: 'AI Smart Search',\n      description: 'Intelligent card discovery with context-aware recommendations',\n      icon: '🔍'\n    },\n    {\n      id: 'qr-scan',\n      title: 'QR Code Scan',\n      description: 'Instant card access via QR code with 3D reveal animation',\n      icon: '📱'\n    },\n    {\n      id: 'voice-assistant',\n      title: 'Voice Assistant',\n      description: 'Talk to AI personas and get personalized responses',\n      icon: '🎤'\n    },\n    {\n      id: 'card-customizer',\n      title: 'Live Customizer',\n      description: 'Real-time card editing with instant 3D preview',\n      icon: '🎨'\n    }\n  ];\n\n  // Enhanced demo handlers\n  const handleScanSimulation = () => {\n    setIsScanning(true);\n    setTimeout(() => {\n      setIsScanning(false);\n      setScannedCard(selectedCard);\n    }, 2000);\n  };\n\n  const handleCardSelection = (card) => {\n    setSelectedCard(card);\n    setScannedCard(null);\n  };\n\n  const handleSmartSearch = async (query) => {\n    setIsSearching(true);\n    setSearchQuery(query);\n\n    // Simulate AI-powered search\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    const results = sampleCards.filter(card =>\n      card.name.toLowerCase().includes(query.toLowerCase()) ||\n      card.company.toLowerCase().includes(query.toLowerCase()) ||\n      card.title.toLowerCase().includes(query.toLowerCase()) ||\n      card.industry.toLowerCase().includes(query.toLowerCase()) ||\n      card.aiPersonality.interests.some(interest =>\n        interest.toLowerCase().includes(query.toLowerCase())\n      )\n    );\n\n    setSearchResults(results);\n    setIsSearching(false);\n  };\n\n  const handleVoiceInteraction = (data) => {\n    console.log('Voice interaction:', data);\n    // Handle voice interaction data\n  };\n\n  const handleARCardDetected = (card) => {\n    setScannedCard(card);\n    console.log('AR card detected:', card);\n  };\n\n  const Card3DPreview = ({ card }) => (\n    <div className=\"relative w-full max-w-md mx-auto\">\n      <div className=\"aspect-[1.6/1] bg-gradient-to-br from-neon-blue via-electric-purple to-hologram-pink rounded-2xl shadow-2xl shadow-neon-blue/30 transform hover:scale-105 hover:rotate-3 transition-all duration-500 group\">\n        <div className=\"absolute inset-0 bg-white/10 rounded-2xl backdrop-blur-sm p-6 flex flex-col justify-between\">\n          {/* Header */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"text-4xl\">{card.avatar}</div>\n            <div>\n              <h3 className=\"text-xl font-bold text-white\">{card.name}</h3>\n              <p className=\"text-white/80 text-sm\">{card.title}</p>\n            </div>\n          </div>\n          \n          {/* Company */}\n          <div className=\"text-center\">\n            <p className=\"text-white/90 font-semibold\">{card.company}</p>\n            <p className=\"text-white/70 text-sm italic\">{card.aiPersonality?.voiceIntro?.substring(0, 50)}...</p>\n          </div>\n          \n          {/* Contact Info */}\n          <div className=\"space-y-1\">\n            <p className=\"text-white/80 text-xs\">{card.email}</p>\n            <p className=\"text-white/80 text-xs\">{card.phone}</p>\n          </div>\n        </div>\n        \n        {/* Floating particles */}\n        <div className=\"absolute -top-2 -right-2 w-3 h-3 bg-neon-blue rounded-full animate-ping\"></div>\n        <div className=\"absolute -bottom-2 -left-2 w-2 h-2 bg-electric-purple rounded-full animate-pulse\"></div>\n        <div className=\"absolute top-1/2 -right-1 w-1.5 h-1.5 bg-hologram-pink rounded-full animate-bounce\"></div>\n      </div>\n    </div>\n  );\n\n  const QRScanDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-2xl font-bold text-text-primary mb-4\">QR Code Scan Demo</h3>\n        <p className=\"text-text-secondary\">Scan the QR code below to see the magic happen</p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\n        {/* QR Code */}\n        <div className=\"text-center\">\n          <div className=\"bg-white p-6 rounded-2xl inline-block mb-4\">\n            {qrCodeUrl ? (\n              <img src={qrCodeUrl} alt=\"Demo QR Code\" className=\"w-48 h-48\" />\n            ) : (\n              <div className=\"w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center\">\n                <div className=\"animate-spin w-8 h-8 border-2 border-neon-blue border-t-transparent rounded-full\"></div>\n              </div>\n            )}\n          </div>\n          \n          <Button \n            onClick={handleScanSimulation}\n            disabled={isScanning}\n            variant=\"primary\"\n            size=\"lg\"\n          >\n            {isScanning ? 'Scanning...' : 'Simulate Scan'}\n          </Button>\n        </div>\n        \n        {/* Result */}\n        <div>\n          {isScanning ? (\n            <div className=\"text-center\">\n              <div className=\"animate-pulse text-6xl mb-4\">📱</div>\n              <p className=\"text-text-secondary\">Scanning QR code...</p>\n            </div>\n          ) : scannedCard ? (\n            <div className=\"space-y-4\">\n              <h4 className=\"text-lg font-semibold text-text-primary\">Card Found!</h4>\n              <Card3DPreview card={scannedCard} />\n              <div className=\"text-center\">\n                <Button variant=\"outline\" size=\"sm\">\n                  Save to Contacts\n                </Button>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center text-text-secondary\">\n              <div className=\"text-6xl mb-4\">👆</div>\n              <p>Scan the QR code to see the result</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  const CameraDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-2xl font-bold text-text-primary mb-4\">Camera Recognition Demo</h3>\n        <p className=\"text-text-secondary\">Point your camera to find digital business cards</p>\n      </div>\n      \n      <div className=\"bg-dark-space/50 rounded-2xl p-6 border border-electric-purple/20\">\n        <div className=\"aspect-video bg-gradient-to-br from-midnight-blue to-deep-purple rounded-lg flex items-center justify-center relative overflow-hidden\">\n          <div className=\"text-center\">\n            <div className=\"text-4xl mb-4\">📷</div>\n            <p className=\"text-text-secondary\">Camera feed simulation</p>\n            <p className=\"text-sm text-text-secondary mt-2\">In real app: Live camera with face detection</p>\n          </div>\n          \n          {/* Scanning overlay */}\n          <div className=\"absolute inset-4 border-2 border-neon-blue/50 rounded-lg\">\n            <div className=\"absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-neon-blue\"></div>\n            <div className=\"absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-neon-blue\"></div>\n            <div className=\"absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-neon-blue\"></div>\n            <div className=\"absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-neon-blue\"></div>\n          </div>\n        </div>\n        \n        <div className=\"mt-4 text-center\">\n          <Button variant=\"primary\">\n            Start Camera Recognition\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const Card3DDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-2xl font-bold text-text-primary mb-4\">3D Card Renderer</h3>\n        <p className=\"text-text-secondary\">Interactive 3D business cards with real-time animations</p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        <div>\n          <Card3DPreview card={selectedCard} />\n        </div>\n        \n        <div className=\"space-y-4\">\n          <h4 className=\"text-lg font-semibold text-text-primary\">Customization Options</h4>\n          \n          <div className=\"space-y-3\">\n            <div>\n              <label className=\"block text-sm text-text-secondary mb-2\">Theme</label>\n              <div className=\"grid grid-cols-3 gap-2\">\n                {['cyber-blue', 'neon-purple', 'matrix-green'].map((theme) => (\n                  <button\n                    key={theme}\n                    className=\"p-2 rounded-lg border border-electric-purple/30 hover:border-neon-blue/50 transition-colors\"\n                  >\n                    <div className={`w-full h-8 rounded bg-gradient-to-r ${\n                      theme === 'cyber-blue' ? 'from-neon-blue to-electric-purple' :\n                      theme === 'neon-purple' ? 'from-electric-purple to-hologram-pink' :\n                      'from-cyber-green to-matrix-green'\n                    }`}></div>\n                  </button>\n                ))}\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm text-text-secondary mb-2\">Effects</label>\n              <div className=\"space-y-2\">\n                {['Matrix Rain', 'Particle Glow', 'Hologram Flicker'].map((effect) => (\n                  <label key={effect} className=\"flex items-center space-x-2\">\n                    <input type=\"checkbox\" className=\"rounded\" />\n                    <span className=\"text-sm text-text-secondary\">{effect}</span>\n                  </label>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  // Live Showcase Demo - 5 Finished Cards\n  const LiveShowcaseDemo = () => {\n    const [showcaseMode, setShowcaseMode] = useState('carousel'); // 'carousel', 'grid', 'tilt'\n    const [selectedShowcaseCard, setSelectedShowcaseCard] = useState(0);\n\n    const handleCarouselSelect = (card, index) => {\n      setSelectedShowcaseCard(index);\n      setSelectedCard(card);\n    };\n\n    return (\n      <div className=\"space-y-8\">\n        <div className=\"text-center\">\n          <h3 className=\"text-4xl font-bold text-text-primary mb-4\">\n            🌟 <span className=\"bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent\">\n              Live Card Showcase\n            </span>\n          </h3>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto mb-6\">\n            Experience 5 production-ready AI business cards. These are fully functional demos showing\n            exactly what users will get with NameCardAI.\n          </p>\n\n          {/* Showcase Mode Selector */}\n          <div className=\"flex justify-center gap-4 mb-8\">\n            {[\n              { id: 'carousel', label: '360° Carousel', icon: '🎠' },\n              { id: 'grid', label: '3D Grid', icon: '🎯' },\n              { id: 'tilt', label: 'Tilt Effects', icon: '🎭' }\n            ].map((mode) => (\n              <button\n                key={mode.id}\n                onClick={() => setShowcaseMode(mode.id)}\n                className={`px-6 py-3 rounded-lg border transition-all duration-300 ${\n                  showcaseMode === mode.id\n                    ? 'border-neon-blue bg-neon-blue/10 text-neon-blue scale-105'\n                    : 'border-electric-purple/30 bg-surface-dark/50 text-text-secondary hover:border-neon-blue/50'\n                }`}\n              >\n                <span className=\"text-lg mr-2\">{mode.icon}</span>\n                {mode.label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Showcase Content */}\n        {showcaseMode === 'carousel' && (\n          <div className=\"space-y-6\">\n            <h4 className=\"text-2xl font-semibold text-text-primary text-center\">\n              360° Rotating Carousel\n            </h4>\n            <SimpleCarousel\n              cards={sampleCards}\n              onCardSelect={handleCarouselSelect}\n              activeCardIndex={selectedShowcaseCard}\n            />\n\n            {/* Selected Card Details */}\n            <div className=\"max-w-4xl mx-auto\">\n              <Card variant=\"glass\" className=\"p-6\">\n                <CardContent>\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    <div>\n                      <h5 className=\"text-xl font-bold text-text-primary mb-4\">\n                        {sampleCards[selectedShowcaseCard]?.name}\n                      </h5>\n                      <div className=\"space-y-3\">\n                        <div>\n                          <span className=\"text-sm text-text-secondary\">Position:</span>\n                          <p className=\"text-text-primary\">{sampleCards[selectedShowcaseCard]?.title}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-sm text-text-secondary\">Company:</span>\n                          <p className=\"text-text-primary\">{sampleCards[selectedShowcaseCard]?.company}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-sm text-text-secondary\">AI Personality:</span>\n                          <p className=\"text-text-primary text-sm\">\n                            {sampleCards[selectedShowcaseCard]?.aiPersonality?.voiceIntro}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-4\">\n                      <h6 className=\"text-lg font-semibold text-text-primary\">Live Stats</h6>\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        <div className=\"p-3 bg-surface-dark/50 rounded-lg text-center\">\n                          <div className=\"text-lg font-bold text-neon-blue\">\n                            {sampleCards[selectedShowcaseCard]?.cardAnalytics?.totalViews?.toLocaleString()}\n                          </div>\n                          <div className=\"text-xs text-text-secondary\">Total Views</div>\n                        </div>\n                        <div className=\"p-3 bg-surface-dark/50 rounded-lg text-center\">\n                          <div className=\"text-lg font-bold text-cyber-green\">\n                            {sampleCards[selectedShowcaseCard]?.cardAnalytics?.engagementRate}%\n                          </div>\n                          <div className=\"text-xs text-text-secondary\">Engagement</div>\n                        </div>\n                        <div className=\"p-3 bg-surface-dark/50 rounded-lg text-center\">\n                          <div className=\"text-lg font-bold text-quantum-gold\">\n                            {sampleCards[selectedShowcaseCard]?.verification?.trustScore}%\n                          </div>\n                          <div className=\"text-xs text-text-secondary\">Trust Score</div>\n                        </div>\n                        <div className=\"p-3 bg-surface-dark/50 rounded-lg text-center\">\n                          <div className=\"text-lg font-bold text-hologram-pink\">\n                            {sampleCards[selectedShowcaseCard]?.cardAnalytics?.uniqueConnections?.toLocaleString()}\n                          </div>\n                          <div className=\"text-xs text-text-secondary\">Connections</div>\n                        </div>\n                      </div>\n\n                      {/* Action Buttons */}\n                      <div className=\"space-y-2\">\n                        {sampleCards[selectedShowcaseCard]?.availableActions?.slice(0, 3).map((action, index) => (\n                          <Button\n                            key={index}\n                            variant=\"outline\"\n                            size=\"sm\"\n                            className=\"w-full justify-start\"\n                          >\n                            <span className=\"mr-2\">{action.icon}</span>\n                            {action.label}\n                          </Button>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        )}\n\n        {showcaseMode === 'grid' && (\n          <div className=\"space-y-6\">\n            <h4 className=\"text-2xl font-semibold text-text-primary text-center\">\n              3D Interactive Grid\n            </h4>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {sampleCards.map((card, index) => (\n                <div key={card.id} className=\"h-80\">\n                  <SimpleCard3D\n                    cardData={card}\n                    theme={card.cardTheme?.background || 'cyber'}\n                    className=\"h-full cursor-pointer\"\n                    onClick={() => {\n                      setSelectedCard(card);\n                      setSelectedShowcaseCard(index);\n                    }}\n                  />\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {showcaseMode === 'tilt' && (\n          <div className=\"space-y-6\">\n            <h4 className=\"text-2xl font-semibold text-text-primary text-center\">\n              3D Tilt Effects\n            </h4>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {sampleCards.map((card, index) => (\n                <EnhancedTiltCard\n                  key={card.id}\n                  preset=\"nft\"\n                  className=\"h-64 cursor-pointer\"\n                  onClick={() => {\n                    setSelectedCard(card);\n                    setSelectedShowcaseCard(index);\n                  }}\n                >\n                  <Card variant=\"glass\" className=\"h-full\">\n                    <CardContent className=\"p-6 h-full flex flex-col justify-between\">\n                      <div className=\"flex items-center gap-3 mb-4\">\n                        <div className=\"text-3xl\">{card.avatar}</div>\n                        <div>\n                          <h5 className=\"font-bold text-text-primary\">{card.name}</h5>\n                          <p className=\"text-sm text-text-secondary\">{card.title}</p>\n                        </div>\n                      </div>\n\n                      <div className=\"text-center mb-4\">\n                        <p className=\"font-semibold text-electric-purple\">{card.company}</p>\n                        <p className=\"text-xs text-text-secondary mt-2\">\n                          {card.industry}\n                        </p>\n                      </div>\n\n                      <div className=\"flex justify-between text-xs\">\n                        <span className=\"text-cyber-green\">\n                          {card.cardAnalytics?.totalViews?.toLocaleString()} views\n                        </span>\n                        <span className=\"text-quantum-gold\">\n                          {card.verification?.trustScore}% trust\n                        </span>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </EnhancedTiltCard>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-12 p-8 bg-gradient-to-r from-neon-blue/10 to-electric-purple/10 rounded-2xl border border-neon-blue/20\">\n          <h4 className=\"text-2xl font-bold text-text-primary mb-4\">\n            Ready to Create Your AI Business Card?\n          </h4>\n          <p className=\"text-text-secondary mb-6\">\n            These are real, production-ready examples. Your card will have the same level of sophistication and interactivity.\n          </p>\n          <div className=\"flex justify-center gap-4\">\n            <Button variant=\"primary\" size=\"lg\">\n              🚀 Start Creating\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              📞 Book Demo Call\n            </Button>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  // Advanced Demo Components\n  const AIShowcaseDemo = () => (\n    <div className=\"space-y-8\">\n      <div className=\"text-center\">\n        <h3 className=\"text-3xl font-bold text-text-primary mb-4\">\n          AI-Powered Business Cards\n        </h3>\n        <p className=\"text-text-secondary max-w-2xl mx-auto\">\n          Experience the future of networking with AI-enhanced business cards featuring voice interaction,\n          dynamic content, and intelligent recommendations.\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Card Selection */}\n        <div className=\"space-y-4\">\n          <h4 className=\"text-lg font-semibold text-text-primary\">Select a Card</h4>\n          {sampleCards.map((card, index) => (\n            <button\n              key={card.id}\n              onClick={() => handleCardSelection(card)}\n              className={`w-full p-4 rounded-lg border transition-all duration-300 text-left ${\n                selectedCard.id === card.id\n                  ? 'border-neon-blue bg-neon-blue/10'\n                  : 'border-electric-purple/30 bg-surface-dark/50 hover:border-electric-purple/60'\n              }`}\n            >\n              <div className=\"flex items-center gap-3\">\n                <div className=\"text-2xl\">{card.avatar}</div>\n                <div>\n                  <div className=\"font-semibold text-text-primary\">{card.name}</div>\n                  <div className=\"text-sm text-text-secondary\">{card.title}</div>\n                  <div className=\"text-xs text-electric-purple\">{card.company}</div>\n                </div>\n              </div>\n            </button>\n          ))}\n        </div>\n\n        {/* 3D Card Display */}\n        <div className=\"space-y-4\">\n          <h4 className=\"text-lg font-semibold text-text-primary\">3D Holographic Card</h4>\n          <SimpleCard3D\n            cardData={selectedCard}\n            theme={selectedCard.cardTheme?.background || 'cyber'}\n            className=\"h-80\"\n          />\n\n          {/* Card Stats */}\n          <div className=\"grid grid-cols-2 gap-4 text-center\">\n            <div className=\"p-3 bg-surface-dark/50 rounded-lg\">\n              <div className=\"text-lg font-bold text-neon-blue\">\n                {selectedCard.cardAnalytics?.totalViews.toLocaleString()}\n              </div>\n              <div className=\"text-xs text-text-secondary\">Total Views</div>\n            </div>\n            <div className=\"p-3 bg-surface-dark/50 rounded-lg\">\n              <div className=\"text-lg font-bold text-cyber-green\">\n                {selectedCard.cardAnalytics?.engagementRate}%\n              </div>\n              <div className=\"text-xs text-text-secondary\">Engagement</div>\n            </div>\n          </div>\n        </div>\n\n        {/* AI Features */}\n        <div className=\"space-y-4\">\n          <h4 className=\"text-lg font-semibold text-text-primary\">AI Features</h4>\n\n          {/* Voice Interaction */}\n          <VoiceInteraction\n            cardData={selectedCard}\n            isActive={voiceActive}\n            onInteraction={handleVoiceInteraction}\n          />\n        </div>\n      </div>\n    </div>\n  );\n\n  const HologramDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-3xl font-bold text-text-primary mb-4\">\n          3D Holographic Business Cards\n        </h3>\n        <p className=\"text-text-secondary\">\n          Immersive 3D business cards with real-time animations and interactive elements\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        <div>\n          <SimpleCard3D\n            cardData={selectedCard}\n            theme={selectedCard.cardTheme?.background || 'cyber'}\n            className=\"h-96\"\n          />\n        </div>\n\n        <div className=\"space-y-6\">\n          <div>\n            <h4 className=\"text-lg font-semibold text-text-primary mb-4\">Customization</h4>\n\n            {/* Theme Selection */}\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm text-text-secondary\">Theme</label>\n              <div className=\"grid grid-cols-3 gap-2\">\n                {['cyber', 'matrix', 'neon'].map((theme) => (\n                  <button\n                    key={theme}\n                    onClick={() => setSelectedCard({...selectedCard, cardTheme: {...selectedCard.cardTheme, background: theme}})}\n                    className={`p-3 rounded-lg border transition-colors ${\n                      selectedCard.cardTheme?.background === theme\n                        ? 'border-neon-blue bg-neon-blue/10'\n                        : 'border-electric-purple/30 hover:border-neon-blue/50'\n                    }`}\n                  >\n                    <div className={`w-full h-8 rounded mb-2 ${\n                      theme === 'cyber' ? 'bg-gradient-to-r from-neon-blue to-electric-purple' :\n                      theme === 'matrix' ? 'bg-gradient-to-r from-cyber-green to-matrix-green' :\n                      'bg-gradient-to-r from-hologram-pink to-quantum-gold'\n                    }`}></div>\n                    <div className=\"text-xs text-text-secondary capitalize\">{theme}</div>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* AI Personality Traits */}\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm text-text-secondary\">AI Personality</label>\n              <div className=\"flex flex-wrap gap-2\">\n                {selectedCard.aiPersonality?.personalityTraits.map((trait, index) => (\n                  <span\n                    key={index}\n                    className=\"px-3 py-1 bg-electric-purple/20 text-electric-purple rounded-full text-xs\"\n                  >\n                    {trait}\n                  </span>\n                ))}\n              </div>\n            </div>\n\n            {/* Recent Achievements */}\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm text-text-secondary\">Recent Achievements</label>\n              <div className=\"space-y-2 max-h-32 overflow-y-auto\">\n                {selectedCard.recentAchievements?.map((achievement, index) => (\n                  <div key={index} className=\"text-xs text-text-secondary p-2 bg-surface-dark/50 rounded\">\n                    • {achievement}\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const ARSimulationDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-3xl font-bold text-text-primary mb-4\">\n          AR Face Recognition & Card Overlay\n        </h3>\n        <p className=\"text-text-secondary\">\n          Advanced augmented reality with real-time face detection and intelligent card matching\n        </p>\n      </div>\n\n      <ARSimulation\n        cardData={selectedCard}\n        isActive={cameraActive}\n        onCardDetected={handleARCardDetected}\n      />\n\n      {scannedCard && (\n        <div className=\"mt-6 p-4 bg-surface-dark/50 rounded-lg border border-cyber-green/30\">\n          <h4 className=\"text-lg font-semibold text-cyber-green mb-2\">Card Detected!</h4>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <p className=\"text-text-secondary text-sm mb-2\">Detected Person:</p>\n              <div className=\"flex items-center gap-3\">\n                <div className=\"text-2xl\">{scannedCard.avatar}</div>\n                <div>\n                  <div className=\"font-semibold text-text-primary\">{scannedCard.name}</div>\n                  <div className=\"text-sm text-text-secondary\">{scannedCard.title}</div>\n                </div>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <Button variant=\"primary\" size=\"sm\" className=\"w-full\">\n                Connect on LinkedIn\n              </Button>\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                Save to Contacts\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const SmartSearchDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-3xl font-bold text-text-primary mb-4\">\n          AI-Powered Smart Search\n        </h3>\n        <p className=\"text-text-secondary\">\n          Intelligent card discovery with context-aware recommendations and semantic search\n        </p>\n      </div>\n\n      <div className=\"max-w-2xl mx-auto\">\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            placeholder=\"Search by name, company, skills, or interests...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            onKeyPress={(e) => e.key === 'Enter' && handleSmartSearch(searchQuery)}\n            className=\"w-full p-4 bg-surface-dark/50 border border-electric-purple/30 rounded-lg text-text-primary placeholder-text-secondary focus:border-neon-blue focus:outline-none\"\n          />\n          <Button\n            onClick={() => handleSmartSearch(searchQuery)}\n            disabled={isSearching}\n            className=\"absolute right-2 top-2\"\n            size=\"sm\"\n          >\n            {isSearching ? '🔄' : '🔍'}\n          </Button>\n        </div>\n\n        {/* Quick Search Suggestions */}\n        <div className=\"flex flex-wrap gap-2 mt-4\">\n          {['AI', 'Blockchain', 'Design', 'CEO', 'Developer'].map((suggestion) => (\n            <button\n              key={suggestion}\n              onClick={() => handleSmartSearch(suggestion)}\n              className=\"px-3 py-1 bg-electric-purple/20 text-electric-purple rounded-full text-sm hover:bg-electric-purple/30 transition-colors\"\n            >\n              {suggestion}\n            </button>\n          ))}\n        </div>\n\n        {/* Search Results */}\n        {searchResults.length > 0 && (\n          <div className=\"mt-6 space-y-4\">\n            <h4 className=\"text-lg font-semibold text-text-primary\">\n              Search Results ({searchResults.length})\n            </h4>\n            {searchResults.map((card) => (\n              <div\n                key={card.id}\n                className=\"p-4 bg-surface-dark/50 border border-electric-purple/30 rounded-lg hover:border-neon-blue/50 transition-colors cursor-pointer\"\n                onClick={() => handleCardSelection(card)}\n              >\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"text-3xl\">{card.avatar}</div>\n                  <div className=\"flex-1\">\n                    <div className=\"font-semibold text-text-primary\">{card.name}</div>\n                    <div className=\"text-sm text-text-secondary\">{card.title} at {card.company}</div>\n                    <div className=\"text-xs text-electric-purple mt-1\">\n                      {card.aiPersonality.interests.slice(0, 3).join(', ')}\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm text-cyber-green\">\n                      {card.verification.trustScore}% Trust Score\n                    </div>\n                    <div className=\"text-xs text-text-secondary\">\n                      {card.cardAnalytics.totalViews.toLocaleString()} views\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  const renderDemo = () => {\n    switch (activeDemo) {\n      case 'live-showcase':\n        return <LiveShowcaseDemo />;\n      case 'ai-showcase':\n        return <AIShowcaseDemo />;\n      case '3d-hologram':\n        return <HologramDemo />;\n      case 'ar-simulation':\n        return <ARSimulationDemo />;\n      case 'smart-search':\n        return <SmartSearchDemo />;\n      case 'qr-scan':\n        return <QRScanDemo />;\n      case 'voice-assistant':\n        return (\n          <div className=\"max-w-2xl mx-auto\">\n            <VoiceInteraction\n              cardData={selectedCard}\n              isActive={true}\n              onInteraction={handleVoiceInteraction}\n            />\n          </div>\n        );\n      case 'card-customizer':\n        return <HologramDemo />; // Reuse hologram demo for customization\n      default:\n        return <AIShowcaseDemo />;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-dark-space via-deep-purple to-midnight-blue\">\n      {/* Hero Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl md:text-6xl font-bold font-display mb-6\">\n              <span className=\"bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent\">\n                Live Demo\n              </span>\n            </h1>\n            <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n              Experience the future of networking with our interactive demos. See how NameCardAI transforms traditional business cards into immersive AR experiences.\n            </p>\n          </div>\n\n          {/* Featured Demos */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-2xl font-bold text-text-primary mb-4 text-center\">\n              🌟 Featured AI Demos\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n              {demos.filter(demo => demo.featured).map((demo) => (\n                <button\n                  key={demo.id}\n                  onClick={() => setActiveDemo(demo.id)}\n                  className={`p-6 rounded-2xl border-2 transition-all duration-300 text-left ${\n                    activeDemo === demo.id\n                      ? 'border-neon-blue bg-neon-blue/10 scale-105 shadow-2xl shadow-neon-blue/20'\n                      : 'border-electric-purple/30 bg-surface-dark/50 hover:border-neon-blue/50 hover:bg-surface-light/50 hover:scale-102'\n                  }`}\n                >\n                  <div className=\"text-4xl mb-4\">{demo.icon}</div>\n                  <h3 className=\"font-bold text-text-primary text-lg mb-2\">{demo.title}</h3>\n                  <p className=\"text-sm text-text-secondary leading-relaxed\">{demo.description}</p>\n                  {activeDemo === demo.id && (\n                    <div className=\"mt-4 flex items-center gap-2 text-neon-blue text-sm\">\n                      <div className=\"w-2 h-2 bg-neon-blue rounded-full animate-pulse\"></div>\n                      Currently Active\n                    </div>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* All Demos Navigation */}\n          <div className=\"mb-12\">\n            <h3 className=\"text-lg font-semibold text-text-primary mb-4 text-center\">\n              All Demo Features\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3\">\n              {demos.map((demo) => (\n                <button\n                  key={demo.id}\n                  onClick={() => setActiveDemo(demo.id)}\n                  className={`p-3 rounded-lg border transition-all duration-300 ${\n                    activeDemo === demo.id\n                      ? 'border-neon-blue bg-neon-blue/10 scale-105'\n                      : 'border-electric-purple/30 bg-surface-dark/50 hover:border-electric-purple/60 hover:bg-surface-light/50'\n                  } ${demo.featured ? 'ring-2 ring-quantum-gold/30' : ''}`}\n                >\n                  <div className=\"text-2xl mb-1\">{demo.icon}</div>\n                  <h4 className=\"font-semibold text-text-primary text-xs mb-1\">{demo.title}</h4>\n                  <p className=\"text-xs text-text-secondary line-clamp-2\">{demo.description}</p>\n                  {demo.featured && (\n                    <div className=\"text-xs text-quantum-gold mt-1\">⭐ Featured</div>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Demo Content */}\n          <Card variant=\"glass\" className=\"p-8\">\n            <CardContent>\n              {renderDemo()}\n            </CardContent>\n          </Card>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAae,SAAS;;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAExB,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;iDAAa;oBACjB,MAAM,UAAU,CAAC,4BAA4B,EAAE,aAAa,EAAE,EAAE;oBAChE,MAAM,QAAQ,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;oBACnC,IAAI,OAAO;wBACT,aAAa;oBACf;gBACF;;YACA;QACF;6BAAG;QAAC,aAAa,EAAE;KAAC;IAEpB,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,cAAc;QACd,WAAW;YACT,cAAc;YACd,eAAe;QACjB,GAAG;IACL;IAEA,MAAM,sBAAsB,CAAC;QAC3B,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,eAAe;QACf,eAAe;QAEf,6BAA6B;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,UAAU,6HAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,OACjC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OAClD,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACrD,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACnD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACtD,KAAK,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,WAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;QAIrD,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,yBAAyB,CAAC;QAC9B,QAAQ,GAAG,CAAC,sBAAsB;IAClC,gCAAgC;IAClC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;QACf,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,iBAC7B,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAY,KAAK,MAAM;;;;;;kDACtC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAgC,KAAK,IAAI;;;;;;0DACvD,6LAAC;gDAAE,WAAU;0DAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;0CAKpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA+B,KAAK,OAAO;;;;;;kDACxD,6LAAC;wCAAE,WAAU;;4CAAgC,KAAK,aAAa,EAAE,YAAY,UAAU,GAAG;4CAAI;;;;;;;;;;;;;0CAIhG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAyB,KAAK,KAAK;;;;;;kDAChD,6LAAC;wCAAE,WAAU;kDAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;kCAKpD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAKrB,MAAM,aAAa,kBACjB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAGrC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,0BACC,6LAAC;wCAAI,KAAK;wCAAW,KAAI;wCAAe,WAAU;;;;;6DAElD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;8CAKrB,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAS;oCACT,UAAU;oCACV,SAAQ;oCACR,MAAK;8CAEJ,aAAa,gBAAgB;;;;;;;;;;;;sCAKlC,6LAAC;sCACE,2BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;uCAEnC,4BACF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAc,MAAM;;;;;;kDACrB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;qDAMxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQf,MAAM,aAAa,kBACjB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAGrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAIlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAInB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,UAAM;gCAAC,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;;;;;;IAQlC,MAAM,aAAa,kBACjB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAGrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCACC,cAAA,6LAAC;gCAAc,MAAM;;;;;;;;;;;sCAGvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAExD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAyC;;;;;;8DAC1D,6LAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAc;wDAAe;qDAAe,CAAC,GAAG,CAAC,CAAC,sBAClD,6LAAC;4DAEC,WAAU;sEAEV,cAAA,6LAAC;gEAAI,WAAW,CAAC,oCAAoC,EACnD,UAAU,eAAe,sCACzB,UAAU,gBAAgB,0CAC1B,oCACA;;;;;;2DAPG;;;;;;;;;;;;;;;;sDAab,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAyC;;;;;;8DAC1D,6LAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAe;wDAAiB;qDAAmB,CAAC,GAAG,CAAC,CAAC,uBACzD,6LAAC;4DAAmB,WAAU;;8EAC5B,6LAAC;oEAAM,MAAK;oEAAW,WAAU;;;;;;8EACjC,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;;2DAFrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAa5B,wCAAwC;IACxC,MAAM,mBAAmB;;QACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,6BAA6B;QAC3F,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAEjE,MAAM,uBAAuB,CAAC,MAAM;YAClC,wBAAwB;YACxB,gBAAgB;QAClB;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAA4C;8CACrD,6LAAC;oCAAK,WAAU;8CAAmF;;;;;;;;;;;;sCAIxG,6LAAC;4BAAE,WAAU;sCAAqD;;;;;;sCAMlE,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,OAAO;oCAAiB,MAAM;gCAAK;gCACrD;oCAAE,IAAI;oCAAQ,OAAO;oCAAW,MAAM;gCAAK;gCAC3C;oCAAE,IAAI;oCAAQ,OAAO;oCAAgB,MAAM;gCAAK;6BACjD,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;oCAEC,SAAS,IAAM,gBAAgB,KAAK,EAAE;oCACtC,WAAW,CAAC,wDAAwD,EAClE,iBAAiB,KAAK,EAAE,GACpB,8DACA,8FACJ;;sDAEF,6LAAC;4CAAK,WAAU;sDAAgB,KAAK,IAAI;;;;;;wCACxC,KAAK,KAAK;;mCATN,KAAK,EAAE;;;;;;;;;;;;;;;;gBAgBnB,iBAAiB,4BAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC,4IAAA,CAAA,UAAc;4BACb,OAAO,6HAAA,CAAA,cAAW;4BAClB,cAAc;4BACd,iBAAiB;;;;;;sCAInB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,kIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAQ,WAAU;0CAC9B,cAAA,6LAAC,kIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,6HAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE;;;;;;kEAEtC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,6LAAC;wEAAE,WAAU;kFAAqB,6HAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE;;;;;;;;;;;;0EAEvE,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,6LAAC;wEAAE,WAAU;kFAAqB,6HAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE;;;;;;;;;;;;0EAEvE,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,6LAAC;wEAAE,WAAU;kFACV,6HAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;;0DAM3D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA0C;;;;;;kEACxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,6HAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE,eAAe,YAAY;;;;;;kFAEjE,6LAAC;wEAAI,WAAU;kFAA8B;;;;;;;;;;;;0EAE/C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,6HAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE,eAAe;4EAAe;;;;;;;kFAEpE,6LAAC;wEAAI,WAAU;kFAA8B;;;;;;;;;;;;0EAE/C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,6HAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE,cAAc;4EAAW;;;;;;;kFAE/D,6LAAC;wEAAI,WAAU;kFAA8B;;;;;;;;;;;;0EAE/C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,6HAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE,eAAe,mBAAmB;;;;;;kFAExE,6LAAC;wEAAI,WAAU;kFAA8B;;;;;;;;;;;;;;;;;;kEAKjD,6LAAC;wDAAI,WAAU;kEACZ,6HAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE,kBAAkB,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,sBAC7E,6LAAC,oIAAA,CAAA,UAAM;gEAEL,SAAQ;gEACR,MAAK;gEACL,WAAU;;kFAEV,6LAAC;wEAAK,WAAU;kFAAQ,OAAO,IAAI;;;;;;oEAClC,OAAO,KAAK;;+DANR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAkBxB,iBAAiB,wBAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAI,WAAU;sCACZ,6HAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;oCAAkB,WAAU;8CAC3B,cAAA,6LAAC,0IAAA,CAAA,UAAY;wCACX,UAAU;wCACV,OAAO,KAAK,SAAS,EAAE,cAAc;wCACrC,WAAU;wCACV,SAAS;4CACP,gBAAgB;4CAChB,wBAAwB;wCAC1B;;;;;;mCARM,KAAK,EAAE;;;;;;;;;;;;;;;;gBAgBxB,iBAAiB,wBAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAI,WAAU;sCACZ,6HAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,2IAAA,CAAA,mBAAgB;oCAEf,QAAO;oCACP,WAAU;oCACV,SAAS;wCACP,gBAAgB;wCAChB,wBAAwB;oCAC1B;8CAEA,cAAA,6LAAC,kIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAQ,WAAU;kDAC9B,cAAA,6LAAC,kIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAY,KAAK,MAAM;;;;;;sEACtC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA+B,KAAK,IAAI;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA+B,KAAK,KAAK;;;;;;;;;;;;;;;;;;8DAI1D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAsC,KAAK,OAAO;;;;;;sEAC/D,6LAAC;4DAAE,WAAU;sEACV,KAAK,QAAQ;;;;;;;;;;;;8DAIlB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEACb,KAAK,aAAa,EAAE,YAAY;gEAAiB;;;;;;;sEAEpD,6LAAC;4DAAK,WAAU;;gEACb,KAAK,YAAY,EAAE;gEAAW;;;;;;;;;;;;;;;;;;;;;;;;mCA9BlC,KAAK,EAAE;;;;;;;;;;;;;;;;8BA0CtB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,6LAAC;4BAAE,WAAU;sCAA2B;;;;;;sCAGxC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAO9C;QAlOM;IAoON,2BAA2B;IAC3B,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAMvD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;gCACvD,6HAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;wCAEC,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,mEAAmE,EAC7E,aAAa,EAAE,KAAK,KAAK,EAAE,GACvB,qCACA,gFACJ;kDAEF,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAY,KAAK,MAAM;;;;;;8DACtC,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAmC,KAAK,IAAI;;;;;;sEAC3D,6LAAC;4DAAI,WAAU;sEAA+B,KAAK,KAAK;;;;;;sEACxD,6LAAC;4DAAI,WAAU;sEAAgC,KAAK,OAAO;;;;;;;;;;;;;;;;;;uCAb1D,KAAK,EAAE;;;;;;;;;;;sCAqBlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,6LAAC,0IAAA,CAAA,UAAY;oCACX,UAAU;oCACV,OAAO,aAAa,SAAS,EAAE,cAAc;oCAC7C,WAAU;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,aAAa,aAAa,EAAE,WAAW;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;8DAA8B;;;;;;;;;;;;sDAE/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,aAAa,aAAa,EAAE;wDAAe;;;;;;;8DAE9C,6LAAC;oDAAI,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;sCAMnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,6LAAC,8IAAA,CAAA,UAAgB;oCACf,UAAU;oCACV,UAAU;oCACV,eAAe;;;;;;;;;;;;;;;;;;;;;;;;IAOzB,MAAM,eAAe,kBACnB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAKrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCACC,cAAA,6LAAC,0IAAA,CAAA,UAAY;gCACX,UAAU;gCACV,OAAO,aAAa,SAAS,EAAE,cAAc;gCAC7C,WAAU;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA+C;;;;;;kDAG7D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAS;oDAAU;iDAAO,CAAC,GAAG,CAAC,CAAC,sBAChC,6LAAC;wDAEC,SAAS,IAAM,gBAAgB;gEAAC,GAAG,YAAY;gEAAE,WAAW;oEAAC,GAAG,aAAa,SAAS;oEAAE,YAAY;gEAAK;4DAAC;wDAC1G,WAAW,CAAC,wCAAwC,EAClD,aAAa,SAAS,EAAE,eAAe,QACnC,qCACA,uDACJ;;0EAEF,6LAAC;gEAAI,WAAW,CAAC,wBAAwB,EACvC,UAAU,UAAU,uDACpB,UAAU,WAAW,sDACrB,uDACA;;;;;;0EACF,6LAAC;gEAAI,WAAU;0EAA0C;;;;;;;uDAbpD;;;;;;;;;;;;;;;;kDAoBb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;0DACZ,aAAa,aAAa,EAAE,kBAAkB,IAAI,CAAC,OAAO,sBACzD,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;kDAUb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;0DACZ,aAAa,kBAAkB,EAAE,IAAI,CAAC,aAAa,sBAClD,6LAAC;wDAAgB,WAAU;;4DAA6D;4DACnF;;uDADK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAY1B,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAKrC,6LAAC,0IAAA,CAAA,UAAY;oBACX,UAAU;oBACV,UAAU;oBACV,gBAAgB;;;;;;gBAGjB,6BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;sDAChD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAY,YAAY,MAAM;;;;;;8DAC7C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAmC,YAAY,IAAI;;;;;;sEAClE,6LAAC;4DAAI,WAAU;sEAA+B,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAIrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAS;;;;;;sDAGvD,6LAAC,oIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUnE,MAAM,kBAAkB,kBACtB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAKrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,kBAAkB;oCAC1D,WAAU;;;;;;8CAEZ,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAS,IAAM,kBAAkB;oCACjC,UAAU;oCACV,WAAU;oCACV,MAAK;8CAEJ,cAAc,OAAO;;;;;;;;;;;;sCAK1B,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAM;gCAAc;gCAAU;gCAAO;6BAAY,CAAC,GAAG,CAAC,CAAC,2BACvD,6LAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAU;8CAET;mCAJI;;;;;;;;;;wBAUV,cAAc,MAAM,GAAG,mBACtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAA0C;wCACrC,cAAc,MAAM;wCAAC;;;;;;;gCAEvC,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDAEnC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAY,KAAK,MAAM;;;;;;8DACtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAmC,KAAK,IAAI;;;;;;sEAC3D,6LAAC;4DAAI,WAAU;;gEAA+B,KAAK,KAAK;gEAAC;gEAAK,KAAK,OAAO;;;;;;;sEAC1E,6LAAC;4DAAI,WAAU;sEACZ,KAAK,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;;;;;;8DAGnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,YAAY,CAAC,UAAU;gEAAC;;;;;;;sEAEhC,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,aAAa,CAAC,UAAU,CAAC,cAAc;gEAAG;;;;;;;;;;;;;;;;;;;uCAlBjD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;IA8B1B,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;;;;;YACV,KAAK;gBACH,qBAAO,6LAAC;;;;;YACV,KAAK;gBACH,qBAAO,6LAAC;;;;;YACV,KAAK;gBACH,qBAAO,6LAAC;;;;;YACV,KAAK;gBACH,qBAAO,6LAAC;;;;;YACV,KAAK;gBACH,qBAAO,6LAAC;;;;;YACV,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,8IAAA,CAAA,UAAgB;wBACf,UAAU;wBACV,UAAU;wBACV,eAAe;;;;;;;;;;;YAIvB,KAAK;gBACH,qBAAO,6LAAC;;;;0BAAiB,wCAAwC;YACnE;gBACE,qBAAO,6LAAC;;;;;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAK,WAAU;8CAAmF;;;;;;;;;;;0CAIrG,6LAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAM/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;0CACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAC,qBACxC,6LAAC;wCAEC,SAAS,IAAM,cAAc,KAAK,EAAE;wCACpC,WAAW,CAAC,+DAA+D,EACzE,eAAe,KAAK,EAAE,GAClB,8EACA,oHACJ;;0DAEF,6LAAC;gDAAI,WAAU;0DAAiB,KAAK,IAAI;;;;;;0DACzC,6LAAC;gDAAG,WAAU;0DAA4C,KAAK,KAAK;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAA+C,KAAK,WAAW;;;;;;4CAC3E,eAAe,KAAK,EAAE,kBACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;oDAAwD;;;;;;;;uCAbtE,KAAK,EAAE;;;;;;;;;;;;;;;;kCAuBpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wCAEC,SAAS,IAAM,cAAc,KAAK,EAAE;wCACpC,WAAW,CAAC,kDAAkD,EAC5D,eAAe,KAAK,EAAE,GAClB,+CACA,yGACL,CAAC,EAAE,KAAK,QAAQ,GAAG,gCAAgC,IAAI;;0DAExD,6LAAC;gDAAI,WAAU;0DAAiB,KAAK,IAAI;;;;;;0DACzC,6LAAC;gDAAG,WAAU;0DAAgD,KAAK,KAAK;;;;;;0DACxE,6LAAC;gDAAE,WAAU;0DAA4C,KAAK,WAAW;;;;;;4CACxE,KAAK,QAAQ,kBACZ,6LAAC;gDAAI,WAAU;0DAAiC;;;;;;;uCAZ7C,KAAK,EAAE;;;;;;;;;;;;;;;;kCAoBpB,6LAAC,kIAAA,CAAA,OAAI;wBAAC,SAAQ;wBAAQ,WAAU;kCAC9B,cAAA,6LAAC,kIAAA,CAAA,cAAW;sCACT;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAt6BwB;KAAA", "debugId": null}}]}