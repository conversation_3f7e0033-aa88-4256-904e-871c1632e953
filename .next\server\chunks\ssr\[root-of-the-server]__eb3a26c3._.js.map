{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Utility function to merge Tailwind CSS classes\n * @param {...string} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Generate a random ID\n * @param {number} length - Length of the ID\n * @returns {string} Random ID\n */\nexport function generateId(length = 8) {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n/**\n * Debounce function to limit function calls\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Throttle function to limit function calls\n * @param {Function} func - Function to throttle\n * @param {number} limit - Time limit in milliseconds\n * @returns {Function} Throttled function\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function executedFunction(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * Format number with commas\n * @param {number} num - Number to format\n * @returns {string} Formatted number\n */\nexport function formatNumber(num) {\n  return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\n}\n\n/**\n * Generate random color from predefined palette\n * @returns {string} Random color class\n */\nexport function getRandomColor() {\n  const colors = [\n    'neon-blue',\n    'electric-purple',\n    'cyber-green',\n    'hologram-pink',\n    'quantum-gold',\n    'matrix-green'\n  ];\n  return colors[Math.floor(Math.random() * colors.length)];\n}\n\n/**\n * Check if element is in viewport\n * @param {Element} element - DOM element to check\n * @returns {boolean} Whether element is in viewport\n */\nexport function isInViewport(element) {\n  const rect = element.getBoundingClientRect();\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\n    rect.right <= (window.innerWidth || document.documentElement.clientWidth)\n  );\n}\n\n/**\n * Smooth scroll to element\n * @param {string} elementId - ID of element to scroll to\n * @param {number} offset - Offset from top\n */\nexport function scrollToElement(elementId, offset = 0) {\n  const element = document.getElementById(elementId);\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n\n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    });\n  }\n}\n\n/**\n * Local storage utilities\n */\nexport const storage = {\n  get: (key, defaultValue = null) => {\n    if (typeof window === 'undefined') return defaultValue;\n    try {\n      const item = window.localStorage.getItem(key);\n      return item ? JSON.parse(item) : defaultValue;\n    } catch (error) {\n      console.error('Error reading from localStorage:', error);\n      return defaultValue;\n    }\n  },\n\n  set: (key, value) => {\n    if (typeof window === 'undefined') return;\n    try {\n      window.localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Error writing to localStorage:', error);\n    }\n  },\n\n  remove: (key) => {\n    if (typeof window === 'undefined') return;\n    try {\n      window.localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Error removing from localStorage:', error);\n    }\n  },\n\n  clear: () => {\n    if (typeof window === 'undefined') return;\n    try {\n      window.localStorage.clear();\n    } catch (error) {\n      console.error('Error clearing localStorage:', error);\n    }\n  }\n};\n\n/**\n * Animation easing functions\n */\nexport const easing = {\n  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',\n  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',\n  elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'\n};\n\n/**\n * Device detection utilities\n */\nexport const device = {\n  isMobile: () => {\n    if (typeof window === 'undefined') return false;\n    return window.innerWidth < 768;\n  },\n\n  isTablet: () => {\n    if (typeof window === 'undefined') return false;\n    return window.innerWidth >= 768 && window.innerWidth < 1024;\n  },\n\n  isDesktop: () => {\n    if (typeof window === 'undefined') return false;\n    return window.innerWidth >= 1024;\n  },\n\n  isTouchDevice: () => {\n    if (typeof window === 'undefined') return false;\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n  }\n};\n\n/**\n * Random effect selector for sections\n * @returns {string} Random effect name\n */\nexport function getRandomEffect() {\n  const effects = [\n    'matrix',\n    'tilt3d',\n    'audioResponsive',\n    'scrollTrigger',\n    'typingText',\n    'smoke',\n    'fireflies',\n    'carousel',\n    'miniDemo'\n  ];\n  return effects[Math.floor(Math.random() * effects.length)];\n}\n\n/**\n * Generate QR code data URL\n * @param {string} text - Text to encode\n * @param {Object} options - QR code options\n * @returns {Promise<string>} Data URL of QR code\n */\nexport async function generateQRCode(text, options = {}) {\n  try {\n    const QRCode = (await import('qrcode')).default;\n    return await QRCode.toDataURL(text, {\n      width: 256,\n      margin: 2,\n      color: {\n        dark: '#00f5ff',\n        light: '#0a0a0f'\n      },\n      ...options\n    });\n  } catch (error) {\n    console.error('Error generating QR code:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAOO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAOO,SAAS,WAAW,SAAS,CAAC;IACnC,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAQO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,IAAI,EAAE;YACjB,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAOO,SAAS,aAAa,GAAG;IAC9B,OAAO,IAAI,QAAQ,GAAG,OAAO,CAAC,yBAAyB;AACzD;AAMO,SAAS;IACd,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAOO,SAAS,aAAa,OAAO;IAClC,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,OACE,KAAK,GAAG,IAAI,KACZ,KAAK,IAAI,IAAI,KACb,KAAK,MAAM,IAAI,CAAC,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,YAAY,KAC3E,KAAK,KAAK,IAAI,CAAC,OAAO,UAAU,IAAI,SAAS,eAAe,CAAC,WAAW;AAE5E;AAOO,SAAS,gBAAgB,SAAS,EAAE,SAAS,CAAC;IACnD,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAKO,MAAM,UAAU;IACrB,KAAK,CAAC,KAAK,eAAe,IAAI;QAC5B,wCAAmC,OAAO;;IAQ5C;IAEA,KAAK,CAAC,KAAK;QACT,wCAAmC;;IAMrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAMrC;IAEA,OAAO;QACL,wCAAmC;;IAMrC;AACF;AAKO,MAAM,SAAS;IACpB,WAAW;IACX,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,SAAS;AACX;AAKO,MAAM,SAAS;IACpB,UAAU;QACR,wCAAmC,OAAO;;IAE5C;IAEA,UAAU;QACR,wCAAmC,OAAO;;IAE5C;IAEA,WAAW;QACT,wCAAmC,OAAO;;IAE5C;IAEA,eAAe;QACb,wCAAmC,OAAO;;IAE5C;AACF;AAMO,SAAS;IACd,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;AAC5D;AAQO,eAAe,eAAe,IAAI,EAAE,UAAU,CAAC,CAAC;IACrD,IAAI;QACF,MAAM,SAAS,CAAC,yIAAsB,EAAE,OAAO;QAC/C,OAAO,MAAM,OAAO,SAAS,CAAC,MAAM;YAClC,OAAO;YACP,QAAQ;YACR,OAAO;gBACL,MAAM;gBACN,OAAO;YACT;YACA,GAAG,OAAO;QACZ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Button = forwardRef(({ \n  className, \n  variant = 'primary', \n  size = 'md', \n  children, \n  disabled = false,\n  loading = false,\n  icon,\n  iconPosition = 'left',\n  ...props \n}, ref) => {\n  const baseStyles = `\n    inline-flex items-center justify-center gap-2 \n    font-medium transition-all duration-300 \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    relative overflow-hidden group\n  `;\n\n  const variants = {\n    primary: `\n      bg-gradient-to-r from-neon-blue to-electric-purple\n      text-white border border-transparent\n      hover:from-electric-purple hover:to-hologram-pink\n      focus:ring-neon-blue\n      shadow-lg shadow-neon-blue/25\n      hover:shadow-xl hover:shadow-electric-purple/30\n    `,\n    secondary: `\n      bg-surface-dark border border-electric-purple/30\n      text-text-primary hover:text-neon-blue\n      hover:border-neon-blue/50 hover:bg-surface-light\n      focus:ring-electric-purple\n      shadow-md shadow-electric-purple/10\n    `,\n    outline: `\n      bg-transparent border-2 border-neon-blue\n      text-neon-blue hover:bg-neon-blue hover:text-dark-space\n      focus:ring-neon-blue\n      hover:shadow-lg hover:shadow-neon-blue/25\n    `,\n    ghost: `\n      bg-transparent border border-transparent\n      text-text-secondary hover:text-neon-blue\n      hover:bg-surface-dark/50\n      focus:ring-electric-purple\n    `,\n    danger: `\n      bg-gradient-to-r from-hologram-pink to-red-500\n      text-white border border-transparent\n      hover:from-red-500 hover:to-hologram-pink\n      focus:ring-hologram-pink\n      shadow-lg shadow-hologram-pink/25\n    `,\n    success: `\n      bg-gradient-to-r from-cyber-green to-green-500\n      text-dark-space border border-transparent\n      hover:from-green-500 hover:to-cyber-green\n      focus:ring-cyber-green\n      shadow-lg shadow-cyber-green/25\n    `\n  };\n\n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm rounded-md',\n    md: 'px-4 py-2 text-base rounded-lg',\n    lg: 'px-6 py-3 text-lg rounded-xl',\n    xl: 'px-8 py-4 text-xl rounded-2xl'\n  };\n\n  const LoadingSpinner = () => (\n    <svg \n      className=\"animate-spin h-4 w-4\" \n      xmlns=\"http://www.w3.org/2000/svg\" \n      fill=\"none\" \n      viewBox=\"0 0 24 24\"\n    >\n      <circle \n        className=\"opacity-25\" \n        cx=\"12\" \n        cy=\"12\" \n        r=\"10\" \n        stroke=\"currentColor\" \n        strokeWidth=\"4\"\n      />\n      <path \n        className=\"opacity-75\" \n        fill=\"currentColor\" \n        d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n      />\n    </svg>\n  );\n\n  const GlowEffect = () => (\n    <div className=\"absolute inset-0 bg-gradient-to-r from-neon-blue/20 to-electric-purple/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-inherit\" />\n  );\n\n  return (\n    <button\n      ref={ref}\n      className={cn(\n        baseStyles,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      disabled={disabled || loading}\n      {...props}\n    >\n      <GlowEffect />\n      \n      {loading && <LoadingSpinner />}\n      \n      {icon && iconPosition === 'left' && !loading && (\n        <span className=\"flex-shrink-0\">{icon}</span>\n      )}\n      \n      <span className=\"relative z-10\">{children}</span>\n      \n      {icon && iconPosition === 'right' && !loading && (\n        <span className=\"flex-shrink-0\">{icon}</span>\n      )}\n    </button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACzB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAC;;;;;;EAMpB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;;;;;IAOV,CAAC;QACD,WAAW,CAAC;;;;;;IAMZ,CAAC;QACD,SAAS,CAAC;;;;;IAKV,CAAC;QACD,OAAO,CAAC;;;;;IAKR,CAAC;QACD,QAAQ,CAAC;;;;;;IAMT,CAAC;QACD,SAAS,CAAC;;;;;;IAMV,CAAC;IACH;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB,kBACrB,8OAAC;YACC,WAAU;YACV,OAAM;YACN,MAAK;YACL,SAAQ;;8BAER,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,GAAE;;;;;;;;;;;;IAKR,MAAM,aAAa,kBACjB,8OAAC;YAAI,WAAU;;;;;;IAGjB,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;0BAET,8OAAC;;;;;YAEA,yBAAW,8OAAC;;;;;YAEZ,QAAQ,iBAAiB,UAAU,CAAC,yBACnC,8OAAC;gBAAK,WAAU;0BAAiB;;;;;;0BAGnC,8OAAC;gBAAK,WAAU;0BAAiB;;;;;;YAEhC,QAAQ,iBAAiB,WAAW,CAAC,yBACpC,8OAAC;gBAAK,WAAU;0BAAiB;;;;;;;;;;;;AAIzC;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/layout/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\nconst Navigation = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Navigation items\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' }\n  ];\n\n  const Logo = () => (\n    <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n      {/* Custom SVG Logo */}\n      <div className=\"relative\">\n        <svg \n          width=\"40\" \n          height=\"40\" \n          viewBox=\"0 0 40 40\" \n          className=\"transition-transform duration-300 group-hover:scale-110\"\n        >\n          {/* Outer ring */}\n          <circle \n            cx=\"20\" \n            cy=\"20\" \n            r=\"18\" \n            fill=\"none\" \n            stroke=\"url(#logoGradient)\" \n            strokeWidth=\"2\"\n            className=\"animate-pulse\"\n          />\n          \n          {/* Inner design */}\n          <path \n            d=\"M12 20 L20 12 L28 20 L20 28 Z\" \n            fill=\"url(#logoGradient)\" \n            className=\"opacity-80\"\n          />\n          \n          {/* Center dot */}\n          <circle \n            cx=\"20\" \n            cy=\"20\" \n            r=\"3\" \n            fill=\"#00f5ff\"\n            className=\"animate-ping\"\n          />\n          \n          {/* Gradient definition */}\n          <defs>\n            <linearGradient id=\"logoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n              <stop offset=\"50%\" stopColor=\"#8b5cf6\" />\n              <stop offset=\"100%\" stopColor=\"#ff006e\" />\n            </linearGradient>\n          </defs>\n        </svg>\n        \n        {/* Glow effect */}\n        <div className=\"absolute inset-0 bg-neon-blue/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n      </div>\n      \n      {/* Brand name */}\n      <span className=\"text-xl font-bold font-display bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent\">\n        NameCardAI\n      </span>\n    </Link>\n  );\n\n  const NavLink = ({ href, label, mobile = false }) => {\n    const isActive = pathname === href;\n    \n    return (\n      <Link\n        href={href}\n        className={cn(\n          'relative px-3 py-2 text-sm font-medium transition-all duration-300',\n          'hover:text-neon-blue',\n          mobile ? 'block w-full text-left' : 'inline-block',\n          isActive \n            ? 'text-neon-blue' \n            : 'text-text-secondary hover:text-text-primary'\n        )}\n        onClick={() => mobile && setIsMobileMenuOpen(false)}\n      >\n        {label}\n        \n        {/* Active indicator */}\n        {isActive && (\n          <div className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-neon-blue to-electric-purple\" />\n        )}\n        \n        {/* Hover effect */}\n        <div className=\"absolute inset-0 bg-neon-blue/10 rounded-md opacity-0 hover:opacity-100 transition-opacity duration-300\" />\n      </Link>\n    );\n  };\n\n  const MobileMenuButton = () => (\n    <button\n      onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n      className=\"md:hidden p-2 text-text-secondary hover:text-neon-blue transition-colors duration-300\"\n      aria-label=\"Toggle mobile menu\"\n    >\n      <svg \n        className=\"w-6 h-6\" \n        fill=\"none\" \n        stroke=\"currentColor\" \n        viewBox=\"0 0 24 24\"\n      >\n        {isMobileMenuOpen ? (\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n        ) : (\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n        )}\n      </svg>\n    </button>\n  );\n\n  return (\n    <>\n      <nav \n        className={cn(\n          'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n          isScrolled \n            ? 'bg-dark-space/95 backdrop-blur-md border-b border-electric-purple/20 shadow-lg shadow-neon-blue/10' \n            : 'bg-transparent'\n        )}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Logo */}\n            <Logo />\n            \n            {/* Desktop Navigation */}\n            <div className=\"hidden md:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <NavLink key={item.href} {...item} />\n              ))}\n            </div>\n            \n            {/* CTA Button */}\n            <div className=\"hidden md:block\">\n              <Button \n                variant=\"primary\" \n                size=\"sm\"\n                className=\"font-semibold\"\n              >\n                Get Started\n              </Button>\n            </div>\n            \n            {/* Mobile menu button */}\n            <MobileMenuButton />\n          </div>\n        </div>\n        \n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden bg-dark-space/95 backdrop-blur-md border-t border-electric-purple/20\">\n            <div className=\"px-4 py-4 space-y-2\">\n              {navItems.map((item) => (\n                <NavLink key={item.href} {...item} mobile />\n              ))}\n              \n              <div className=\"pt-4 border-t border-electric-purple/20\">\n                <Button \n                  variant=\"primary\" \n                  size=\"sm\" \n                  className=\"w-full font-semibold\"\n                >\n                  Get Started\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n      \n      {/* Spacer to prevent content from hiding behind fixed nav */}\n      <div className=\"h-16\" />\n    </>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,MAAM,OAAO,kBACX,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAK;YAAI,WAAU;;8BAEvB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,WAAU;;8CAGV,8OAAC;oCACC,IAAG;oCACH,IAAG;oCACH,GAAE;oCACF,MAAK;oCACL,QAAO;oCACP,aAAY;oCACZ,WAAU;;;;;;8CAIZ,8OAAC;oCACC,GAAE;oCACF,MAAK;oCACL,WAAU;;;;;;8CAIZ,8OAAC;oCACC,IAAG;oCACH,IAAG;oCACH,GAAE;oCACF,MAAK;oCACL,WAAU;;;;;;8CAIZ,8OAAC;8CACC,cAAA,8OAAC;wCAAe,IAAG;wCAAe,IAAG;wCAAK,IAAG;wCAAK,IAAG;wCAAO,IAAG;;0DAC7D,8OAAC;gDAAK,QAAO;gDAAK,WAAU;;;;;;0DAC5B,8OAAC;gDAAK,QAAO;gDAAM,WAAU;;;;;;0DAC7B,8OAAC;gDAAK,QAAO;gDAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMpC,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC;oBAAK,WAAU;8BAAkH;;;;;;;;;;;;IAMtI,MAAM,UAAU,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,KAAK,EAAE;QAC9C,MAAM,WAAW,aAAa;QAE9B,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM;YACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA,wBACA,SAAS,2BAA2B,gBACpC,WACI,mBACA;YAEN,SAAS,IAAM,UAAU,oBAAoB;;gBAE5C;gBAGA,0BACC,8OAAC;oBAAI,WAAU;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,MAAM,mBAAmB,kBACvB,8OAAC;YACC,SAAS,IAAM,oBAAoB,CAAC;YACpC,WAAU;YACV,cAAW;sBAEX,cAAA,8OAAC;gBACC,WAAU;gBACV,MAAK;gBACL,QAAO;gBACP,SAAQ;0BAEP,iCACC,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;yCAErE,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;;;;;;IAM7E,qBACE;;0BACE,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,uGACA;;kCAGN,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;;;;8CAGD,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;4CAAyB,GAAG,IAAI;2CAAnB,KAAK,IAAI;;;;;;;;;;8CAK3B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAMH,8OAAC;;;;;;;;;;;;;;;;oBAKJ,kCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;wCAAyB,GAAG,IAAI;wCAAE,MAAM;uCAA3B,KAAK,IAAI;;;;;8CAGzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAI,WAAU;;;;;;;;AAGrB;uCAEe", "debugId": null}}]}