@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;700;900&family=JetBrains+Mono:wght@400;500&display=swap');

:root {
  /* Futuristic AI Color Palette */
  --neon-blue: #00f5ff;
  --electric-purple: #8b5cf6;
  --cyber-green: #00ff88;
  --hologram-pink: #ff006e;
  --quantum-gold: #ffd700;
  --matrix-green: #00ff41;

  /* Background Colors */
  --dark-space: #0a0a0f;
  --deep-purple: #1a0b2e;
  --midnight-blue: #16213e;
  --surface-dark: #1a1a2e;
  --surface-light: #16213e;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b8bcc8;
  --text-accent: #00f5ff;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--neon-blue), var(--electric-purple));
  --gradient-secondary: linear-gradient(135deg, var(--cyber-green), var(--hologram-pink));
  --gradient-background: linear-gradient(135deg, var(--dark-space), var(--deep-purple));
}

@theme inline {
  /* Custom Colors */
  --color-neon-blue: var(--neon-blue);
  --color-electric-purple: var(--electric-purple);
  --color-cyber-green: var(--cyber-green);
  --color-hologram-pink: var(--hologram-pink);
  --color-quantum-gold: var(--quantum-gold);
  --color-matrix-green: var(--matrix-green);

  --color-dark-space: var(--dark-space);
  --color-deep-purple: var(--deep-purple);
  --color-midnight-blue: var(--midnight-blue);
  --color-surface-dark: var(--surface-dark);
  --color-surface-light: var(--surface-light);

  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-accent: var(--text-accent);

  /* Typography */
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-display: 'Orbitron', monospace;
  --font-mono: 'JetBrains Mono', monospace;

  /* Spacing & Sizing */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Animation Durations */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.6s;

  /* Z-Index Layers */
  --z-background: -1;
  --z-base: 0;
  --z-overlay: 10;
  --z-modal: 50;
  --z-tooltip: 100;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--dark-space);
  color: var(--text-primary);
  font-family: var(--font-sans);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-space);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--neon-blue), var(--electric-purple));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--electric-purple), var(--hologram-pink));
}

/* Selection Styles */
::selection {
  background: var(--neon-blue);
  color: var(--dark-space);
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid var(--neon-blue);
  outline-offset: 2px;
}
