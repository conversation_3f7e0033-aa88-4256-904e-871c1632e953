{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_6d9032cf.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_6d9032cf-module__7XMLfa__className\",\n  \"variable\": \"inter_6d9032cf-module__7XMLfa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_6d9032cf.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/orbitron_17e848d1.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"orbitron_17e848d1-module__t4m47a__className\",\n  \"variable\": \"orbitron_17e848d1-module__t4m47a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/orbitron_17e848d1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Orbitron%22,%22arguments%22:[{%22variable%22:%22--font-orbitron%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22orbitron%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Orbitron', 'Orbitron Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_515fd822.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"jetbrains_mono_515fd822-module__SRVNyW__className\",\n  \"variable\": \"jetbrains_mono_515fd822-module__SRVNyW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_515fd822.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22JetBrains_Mono%22,%22arguments%22:[{%22variable%22:%22--font-jetbrains-mono%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22jetbrainsMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'JetBrains Mono', 'JetBrains Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/layout/Navigation.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/layout/Navigation.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/app/layout.js"], "sourcesContent": ["import { Inter, Orbitron, JetBrains_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport Navigation from \"@/components/layout/Navigation\";\n\nconst inter = Inter({\n  variable: \"--font-inter\",\n  subsets: [\"latin\"],\n  display: 'swap',\n});\n\nconst orbitron = Orbitron({\n  variable: \"--font-orbitron\",\n  subsets: [\"latin\"],\n  display: 'swap',\n});\n\nconst jetbrainsMono = JetBrains_Mono({\n  variable: \"--font-jetbrains-mono\",\n  subsets: [\"latin\"],\n  display: 'swap',\n});\n\nexport const metadata = {\n  title: \"NameCardAI - Your Name. Reinvented.\",\n  description: \"AR-enhanced digital business cards that let you share stunning, interactive profiles via QR, NFC, facial recognition, or camera scan—no app required.\",\n  keywords: \"digital business cards, AR business cards, QR code, NFC, networking, professional identity\",\n  authors: [{ name: \"NameCardAI Team\" }],\n  creator: \"NameCardAI\",\n  publisher: \"NameCardAI\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL('https://namecardai.com'),\n  alternates: {\n    canonical: '/',\n  },\n  openGraph: {\n    title: \"NameCardAI - Your Name. Reinvented.\",\n    description: \"AR-enhanced digital business cards that make networking unforgettable\",\n    url: 'https://namecardai.com',\n    siteName: 'NameCardAI',\n    images: [\n      {\n        url: '/og-image.jpg',\n        width: 1200,\n        height: 630,\n        alt: 'NameCardAI - AR-Enhanced Digital Business Cards',\n      },\n    ],\n    locale: 'en_US',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: \"NameCardAI - Your Name. Reinvented.\",\n    description: \"AR-enhanced digital business cards that make networking unforgettable\",\n    images: ['/og-image.jpg'],\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n  icons: {\n    icon: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',\n    shortcut: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',\n    apple: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',\n  },\n};\n\nexport default function RootLayout({ children }) {\n  return (\n    <html lang=\"en\" className=\"scroll-smooth\">\n      <body\n        className={`${inter.variable} ${orbitron.variable} ${jetbrainsMono.variable} antialiased`}\n      >\n        <Navigation />\n        <main className=\"min-h-screen\">\n          {children}\n        </main>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;;;;;;;AAoBO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAkB;KAAE;IACtC,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc,IAAI,IAAI;IACtB,YAAY;QACV,WAAW;IACb;IACA,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAgB;IAC3B;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;AACF;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,4IAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,kJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,YAAY,CAAC;;8BAEzF,8OAAC,yIAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}