import HeroSection from '@/components/sections/HeroSection';
import ProblemSection from '@/components/sections/ProblemSection';
import SolutionSection from '@/components/sections/SolutionSection';
import ProcessSection from '@/components/sections/ProcessSection';
import FeaturesSection from '@/components/sections/FeaturesSection';
import ComparisonSection from '@/components/sections/ComparisonSection';
import TestimonialsSection from '@/components/sections/TestimonialsSection';
import ValueSection from '@/components/sections/ValueSection';
import PricingSection from '@/components/sections/PricingSection';
import TrustSection from '@/components/sections/TrustSection';
import EarlyAdopterSection from '@/components/sections/EarlyAdopterSection';
import FooterSection from '@/components/sections/FooterSection';

export default function Home() {
  return (
    <div className="relative">
      {/* Hero Section - Matrix Effect Background */}
      <HeroSection />

      {/* Problem Statement - 3D Tilt Cards */}
      <ProblemSection />

      {/* Solution Overview - Typing Text Animation */}
      <SolutionSection />

      {/* 3-Step Process - Scroll-triggered Reveals */}
      <ProcessSection />

      {/* MVP Feature Preview - Carousel with Parallax */}
      <FeaturesSection />

      {/* Competitor Comparison - Interactive Table */}
      <ComparisonSection />

      {/* Testimonials - Floating Cards */}
      <TestimonialsSection />

      {/* Value Proposition - Audio-responsive Visuals */}
      <ValueSection />

      {/* Pricing Plans - Hover Transformations */}
      <PricingSection />

      {/* Trust Elements - Smoke Effects */}
      <TrustSection />

      {/* Early Adopter CTA - Fireflies Background */}
      <EarlyAdopterSection />

      {/* Footer - Gradient Animations */}
      <FooterSection />
    </div>
  );
}
