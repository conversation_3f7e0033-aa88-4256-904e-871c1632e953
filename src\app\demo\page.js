'use client';

import { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { generateQRCode } from '@/lib/utils';

export default function DemoPage() {
  const [activeDemo, setActiveDemo] = useState('qr-scan');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [scannedCard, setScannedCard] = useState(null);
  const videoRef = useRef(null);

  // Sample card data
  const sampleCard = {
    id: 'demo-card-1',
    name: '<PERSON>',
    title: 'Senior Product Designer',
    company: 'TechCorp',
    email: '<EMAIL>',
    phone: '+****************',
    linkedin: 'linkedin.com/in/alexchen',
    avatar: '👨‍💻',
    theme: 'cyber-blue',
    effects: ['matrix', 'glow'],
    intro: 'Designing the future, one pixel at a time.'
  };

  // Generate QR code on component mount
  useEffect(() => {
    const generateQR = async () => {
      const cardUrl = `https://namecardai.com/card/${sampleCard.id}`;
      const qrUrl = await generateQRCode(cardUrl);
      if (qrUrl) {
        setQrCodeUrl(qrUrl);
      }
    };
    generateQR();
  }, []);

  const demos = [
    {
      id: 'qr-scan',
      title: 'QR Code Scan',
      description: 'Scan a QR code to instantly view a 3D business card',
      icon: '📱'
    },
    {
      id: 'camera-recognition',
      title: 'Camera Recognition',
      description: 'Point your camera at someone to find their digital card',
      icon: '📷'
    },
    {
      id: '3d-card',
      title: '3D Card Renderer',
      description: 'Experience immersive 3D business cards with animations',
      icon: '🎯'
    },
    {
      id: 'ar-overlay',
      title: 'AR Overlay',
      description: 'See business cards overlaid in augmented reality',
      icon: '✨'
    },
    {
      id: 'name-search',
      title: 'Name Search',
      description: 'Find cards by typing a name or company',
      icon: '🔍'
    }
  ];

  const handleScanSimulation = () => {
    setIsScanning(true);
    setTimeout(() => {
      setIsScanning(false);
      setScannedCard(sampleCard);
    }, 2000);
  };

  const Card3DPreview = ({ card }) => (
    <div className="relative w-full max-w-md mx-auto">
      <div className="aspect-[1.6/1] bg-gradient-to-br from-neon-blue via-electric-purple to-hologram-pink rounded-2xl shadow-2xl shadow-neon-blue/30 transform hover:scale-105 hover:rotate-3 transition-all duration-500 group">
        <div className="absolute inset-0 bg-white/10 rounded-2xl backdrop-blur-sm p-6 flex flex-col justify-between">
          {/* Header */}
          <div className="flex items-center space-x-4">
            <div className="text-4xl">{card.avatar}</div>
            <div>
              <h3 className="text-xl font-bold text-white">{card.name}</h3>
              <p className="text-white/80 text-sm">{card.title}</p>
            </div>
          </div>
          
          {/* Company */}
          <div className="text-center">
            <p className="text-white/90 font-semibold">{card.company}</p>
            <p className="text-white/70 text-sm italic">{card.intro}</p>
          </div>
          
          {/* Contact Info */}
          <div className="space-y-1">
            <p className="text-white/80 text-xs">{card.email}</p>
            <p className="text-white/80 text-xs">{card.phone}</p>
          </div>
        </div>
        
        {/* Floating particles */}
        <div className="absolute -top-2 -right-2 w-3 h-3 bg-neon-blue rounded-full animate-ping"></div>
        <div className="absolute -bottom-2 -left-2 w-2 h-2 bg-electric-purple rounded-full animate-pulse"></div>
        <div className="absolute top-1/2 -right-1 w-1.5 h-1.5 bg-hologram-pink rounded-full animate-bounce"></div>
      </div>
    </div>
  );

  const QRScanDemo = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-text-primary mb-4">QR Code Scan Demo</h3>
        <p className="text-text-secondary">Scan the QR code below to see the magic happen</p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* QR Code */}
        <div className="text-center">
          <div className="bg-white p-6 rounded-2xl inline-block mb-4">
            {qrCodeUrl ? (
              <img src={qrCodeUrl} alt="Demo QR Code" className="w-48 h-48" />
            ) : (
              <div className="w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                <div className="animate-spin w-8 h-8 border-2 border-neon-blue border-t-transparent rounded-full"></div>
              </div>
            )}
          </div>
          
          <Button 
            onClick={handleScanSimulation}
            disabled={isScanning}
            variant="primary"
            size="lg"
          >
            {isScanning ? 'Scanning...' : 'Simulate Scan'}
          </Button>
        </div>
        
        {/* Result */}
        <div>
          {isScanning ? (
            <div className="text-center">
              <div className="animate-pulse text-6xl mb-4">📱</div>
              <p className="text-text-secondary">Scanning QR code...</p>
            </div>
          ) : scannedCard ? (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-text-primary">Card Found!</h4>
              <Card3DPreview card={scannedCard} />
              <div className="text-center">
                <Button variant="outline" size="sm">
                  Save to Contacts
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center text-text-secondary">
              <div className="text-6xl mb-4">👆</div>
              <p>Scan the QR code to see the result</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const CameraDemo = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-text-primary mb-4">Camera Recognition Demo</h3>
        <p className="text-text-secondary">Point your camera to find digital business cards</p>
      </div>
      
      <div className="bg-dark-space/50 rounded-2xl p-6 border border-electric-purple/20">
        <div className="aspect-video bg-gradient-to-br from-midnight-blue to-deep-purple rounded-lg flex items-center justify-center relative overflow-hidden">
          <div className="text-center">
            <div className="text-4xl mb-4">📷</div>
            <p className="text-text-secondary">Camera feed simulation</p>
            <p className="text-sm text-text-secondary mt-2">In real app: Live camera with face detection</p>
          </div>
          
          {/* Scanning overlay */}
          <div className="absolute inset-4 border-2 border-neon-blue/50 rounded-lg">
            <div className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-neon-blue"></div>
            <div className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-neon-blue"></div>
            <div className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-neon-blue"></div>
            <div className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-neon-blue"></div>
          </div>
        </div>
        
        <div className="mt-4 text-center">
          <Button variant="primary">
            Start Camera Recognition
          </Button>
        </div>
      </div>
    </div>
  );

  const Card3DDemo = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-text-primary mb-4">3D Card Renderer</h3>
        <p className="text-text-secondary">Interactive 3D business cards with real-time animations</p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <Card3DPreview card={sampleCard} />
        </div>
        
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-text-primary">Customization Options</h4>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-text-secondary mb-2">Theme</label>
              <div className="grid grid-cols-3 gap-2">
                {['cyber-blue', 'neon-purple', 'matrix-green'].map((theme) => (
                  <button
                    key={theme}
                    className="p-2 rounded-lg border border-electric-purple/30 hover:border-neon-blue/50 transition-colors"
                  >
                    <div className={`w-full h-8 rounded bg-gradient-to-r ${
                      theme === 'cyber-blue' ? 'from-neon-blue to-electric-purple' :
                      theme === 'neon-purple' ? 'from-electric-purple to-hologram-pink' :
                      'from-cyber-green to-matrix-green'
                    }`}></div>
                  </button>
                ))}
              </div>
            </div>
            
            <div>
              <label className="block text-sm text-text-secondary mb-2">Effects</label>
              <div className="space-y-2">
                {['Matrix Rain', 'Particle Glow', 'Hologram Flicker'].map((effect) => (
                  <label key={effect} className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm text-text-secondary">{effect}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDemo = () => {
    switch (activeDemo) {
      case 'qr-scan':
        return <QRScanDemo />;
      case 'camera-recognition':
        return <CameraDemo />;
      case '3d-card':
        return <Card3DDemo />;
      case 'ar-overlay':
        return (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🥽</div>
            <h3 className="text-2xl font-bold text-text-primary mb-4">AR Overlay Demo</h3>
            <p className="text-text-secondary">Coming soon - WebXR integration</p>
          </div>
        );
      case 'name-search':
        return (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-bold text-text-primary mb-4">Name Search Demo</h3>
            <p className="text-text-secondary">Coming soon - AI-powered search</p>
          </div>
        );
      default:
        return <QRScanDemo />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-space via-deep-purple to-midnight-blue">
      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-bold font-display mb-6">
              <span className="bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent">
                Live Demo
              </span>
            </h1>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Experience the future of networking with our interactive demos. See how NameCardAI transforms traditional business cards into immersive AR experiences.
            </p>
          </div>

          {/* Demo Navigation */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-12">
            {demos.map((demo) => (
              <button
                key={demo.id}
                onClick={() => setActiveDemo(demo.id)}
                className={`p-4 rounded-xl border transition-all duration-300 ${
                  activeDemo === demo.id
                    ? 'border-neon-blue bg-neon-blue/10 scale-105'
                    : 'border-electric-purple/30 bg-surface-dark/50 hover:border-electric-purple/60 hover:bg-surface-light/50'
                }`}
              >
                <div className="text-3xl mb-2">{demo.icon}</div>
                <h3 className="font-semibold text-text-primary text-sm mb-1">{demo.title}</h3>
                <p className="text-xs text-text-secondary">{demo.description}</p>
              </button>
            ))}
          </div>

          {/* Demo Content */}
          <Card variant="glass" className="p-8">
            <CardContent>
              {renderDemo()}
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
