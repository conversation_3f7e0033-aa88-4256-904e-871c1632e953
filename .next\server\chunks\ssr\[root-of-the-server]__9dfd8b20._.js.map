{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/ui/Card.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = forwardRef(({ \n  className, \n  variant = 'default',\n  hover = true,\n  glow = false,\n  children, \n  ...props \n}, ref) => {\n  const baseStyles = `\n    relative rounded-xl border backdrop-blur-sm\n    transition-all duration-300 ease-out\n    overflow-hidden group\n  `;\n\n  const variants = {\n    default: `\n      bg-surface-dark/80 border-electric-purple/20\n      hover:border-neon-blue/40 hover:bg-surface-light/80\n    `,\n    glass: `\n      bg-white/5 border-white/10\n      hover:bg-white/10 hover:border-white/20\n      backdrop-blur-md\n    `,\n    neon: `\n      bg-dark-space/90 border-neon-blue/50\n      hover:border-neon-blue hover:bg-dark-space\n      shadow-lg shadow-neon-blue/10\n    `,\n    purple: `\n      bg-deep-purple/80 border-electric-purple/30\n      hover:border-electric-purple hover:bg-deep-purple\n      shadow-lg shadow-electric-purple/10\n    `,\n    gradient: `\n      bg-gradient-to-br from-surface-dark/80 to-deep-purple/80\n      border-gradient-to-r from-neon-blue/30 to-electric-purple/30\n      hover:from-surface-light/80 hover:to-midnight-blue/80\n    `\n  };\n\n  const hoverEffects = hover ? `\n    hover:scale-[1.02] hover:-translate-y-1\n    hover:shadow-2xl hover:shadow-neon-blue/20\n  ` : '';\n\n  const glowEffect = glow ? `\n    before:absolute before:inset-0 before:rounded-xl\n    before:bg-gradient-to-r before:from-neon-blue/20 before:to-electric-purple/20\n    before:opacity-0 before:transition-opacity before:duration-300\n    hover:before:opacity-100\n  ` : '';\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        baseStyles,\n        variants[variant],\n        hoverEffects,\n        glowEffect,\n        className\n      )}\n      {...props}\n    >\n      {/* Animated border gradient */}\n      <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-neon-blue via-electric-purple to-hologram-pink opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n      \n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n      \n      {/* Corner accent */}\n      <div className=\"absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-neon-blue/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n    </div>\n  );\n});\n\nCard.displayName = 'Card';\n\n// Card Header Component\nconst CardHeader = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('p-6 pb-4', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardHeader.displayName = 'CardHeader';\n\n// Card Title Component\nconst CardTitle = forwardRef(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-xl font-semibold text-text-primary mb-2',\n      'bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </h3>\n));\n\nCardTitle.displayName = 'CardTitle';\n\n// Card Description Component\nconst CardDescription = forwardRef(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-text-secondary leading-relaxed', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\n\nCardDescription.displayName = 'CardDescription';\n\n// Card Content Component\nconst CardContent = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('px-6 pb-6', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardContent.displayName = 'CardContent';\n\n// Card Footer Component\nconst CardFooter = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'px-6 py-4 border-t border-electric-purple/20',\n      'bg-gradient-to-r from-transparent via-electric-purple/5 to-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardFooter.displayName = 'CardFooter';\n\n// Export all components\nexport {\n  Card,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n  CardContent,\n  CardFooter\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACvB,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAC;;;;EAIpB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;IAGV,CAAC;QACD,OAAO,CAAC;;;;IAIR,CAAC;QACD,MAAM,CAAC;;;;IAIP,CAAC;QACD,QAAQ,CAAC;;;;IAIT,CAAC;QACD,UAAU,CAAC;;;;IAIX,CAAC;IACH;IAEA,MAAM,eAAe,QAAQ,CAAC;;;EAG9B,CAAC,GAAG;IAEJ,MAAM,aAAa,OAAO,CAAC;;;;;EAK3B,CAAC,GAAG;IAEJ,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,cACA,YACA;QAED,GAAG,KAAK;;0BAGT,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;AAEA,KAAK,WAAW,GAAG;AAEnB,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;AAIL,WAAW,WAAW,GAAG;AAEzB,uBAAuB;AACvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC/D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gDACA,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIL,UAAU,WAAW,GAAG;AAExB,6BAA6B;AAC7B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACrE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;QACpD,GAAG,KAAK;kBAER;;;;;;AAIL,gBAAgB,WAAW,GAAG;AAE9B,yBAAyB;AACzB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACjE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;kBAER;;;;;;AAIL,YAAY,WAAW,GAAG;AAE1B,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gDACA,0EACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIL,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/3d/Card3D.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState } from 'react';\nimport { Canvas, useFrame, useThree } from '@react-three/fiber';\nimport { Text, Box, Sphere, MeshDistortMaterial, Float, Html, Environment, ContactShadows } from '@react-three/drei';\nimport * as THREE from 'three';\n\n// Advanced 3D Business Card Component\nconst BusinessCard3D = ({ cardData, isActive = true, theme = 'cyber' }) => {\n  const meshRef = useRef();\n  const particlesRef = useRef();\n  const [hovered, setHovered] = useState(false);\n  const [clicked, setClicked] = useState(false);\n\n  // Animation loop\n  useFrame((state) => {\n    if (meshRef.current && isActive) {\n      // Floating animation\n      meshRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.1;\n      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n      \n      // Hover effects\n      if (hovered) {\n        meshRef.current.scale.setScalar(1.05);\n        meshRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 2) * 0.05;\n      } else {\n        meshRef.current.scale.setScalar(1);\n        meshRef.current.rotation.z = 0;\n      }\n    }\n\n    // Animate particles\n    if (particlesRef.current) {\n      particlesRef.current.rotation.y += 0.01;\n    }\n  });\n\n  // Theme configurations\n  const themes = {\n    cyber: {\n      primary: '#00f5ff',\n      secondary: '#8b5cf6',\n      accent: '#ff006e',\n      background: '#0a0a0f'\n    },\n    matrix: {\n      primary: '#00ff41',\n      secondary: '#008f11',\n      accent: '#00ff88',\n      background: '#000000'\n    },\n    neon: {\n      primary: '#ff0080',\n      secondary: '#8000ff',\n      accent: '#00ffff',\n      background: '#1a0033'\n    }\n  };\n\n  const currentTheme = themes[theme] || themes.cyber;\n\n  // Particle system for background effects\n  const ParticleField = () => {\n    const particleCount = 100;\n    const positions = new Float32Array(particleCount * 3);\n    \n    for (let i = 0; i < particleCount; i++) {\n      positions[i * 3] = (Math.random() - 0.5) * 10;\n      positions[i * 3 + 1] = (Math.random() - 0.5) * 10;\n      positions[i * 3 + 2] = (Math.random() - 0.5) * 10;\n    }\n\n    return (\n      <points ref={particlesRef}>\n        <bufferGeometry>\n          <bufferAttribute\n            attach=\"attributes-position\"\n            count={particleCount}\n            array={positions}\n            itemSize={3}\n          />\n        </bufferGeometry>\n        <pointsMaterial\n          size={0.05}\n          color={currentTheme.primary}\n          transparent\n          opacity={0.6}\n          sizeAttenuation\n        />\n      </points>\n    );\n  };\n\n  // Holographic card surface\n  const CardSurface = () => (\n    <Box\n      ref={meshRef}\n      args={[3.4, 2.1, 0.1]}\n      onPointerOver={() => setHovered(true)}\n      onPointerOut={() => setHovered(false)}\n      onClick={() => setClicked(!clicked)}\n    >\n      <MeshDistortMaterial\n        color={currentTheme.background}\n        transparent\n        opacity={0.8}\n        distort={hovered ? 0.3 : 0.1}\n        speed={2}\n        roughness={0.1}\n        metalness={0.8}\n      />\n      \n      {/* Card Content Overlay */}\n      <Html\n        transform\n        occlude\n        position={[0, 0, 0.06]}\n        style={{\n          width: '340px',\n          height: '210px',\n          background: `linear-gradient(135deg, ${currentTheme.primary}20, ${currentTheme.secondary}20)`,\n          backdropFilter: 'blur(10px)',\n          border: `1px solid ${currentTheme.primary}40`,\n          borderRadius: '12px',\n          padding: '16px',\n          color: 'white',\n          fontFamily: 'Inter, sans-serif',\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between'\n        }}\n      >\n        {/* Header */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div style={{ \n            fontSize: '32px',\n            filter: `drop-shadow(0 0 10px ${currentTheme.primary})`\n          }}>\n            {cardData.avatar}\n          </div>\n          <div>\n            <h3 style={{ \n              margin: 0, \n              fontSize: '18px', \n              fontWeight: 'bold',\n              background: `linear-gradient(45deg, ${currentTheme.primary}, ${currentTheme.accent})`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent'\n            }}>\n              {cardData.name}\n            </h3>\n            <p style={{ \n              margin: 0, \n              fontSize: '12px', \n              opacity: 0.8,\n              color: currentTheme.secondary\n            }}>\n              {cardData.title}\n            </p>\n          </div>\n        </div>\n\n        {/* Company & Intro */}\n        <div style={{ textAlign: 'center' }}>\n          <p style={{ \n            margin: '8px 0', \n            fontSize: '14px', \n            fontWeight: 'bold',\n            color: currentTheme.primary\n          }}>\n            {cardData.company}\n          </p>\n          <p style={{ \n            margin: 0, \n            fontSize: '10px', \n            fontStyle: 'italic',\n            opacity: 0.7\n          }}>\n            {cardData.intro}\n          </p>\n        </div>\n\n        {/* Contact Info */}\n        <div style={{ fontSize: '10px', opacity: 0.8 }}>\n          <div>{cardData.email}</div>\n          <div>{cardData.phone}</div>\n        </div>\n\n        {/* AI Status Indicator */}\n        <div style={{ \n          position: 'absolute',\n          top: '8px',\n          right: '8px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '4px',\n          fontSize: '8px',\n          color: currentTheme.primary\n        }}>\n          <div style={{\n            width: '6px',\n            height: '6px',\n            borderRadius: '50%',\n            backgroundColor: currentTheme.primary,\n            animation: 'pulse 2s infinite'\n          }} />\n          AI ACTIVE\n        </div>\n      </Html>\n    </Box>\n  );\n\n  // Floating accent elements\n  const AccentElements = () => (\n    <>\n      <Float speed={1.5} rotationIntensity={0.5} floatIntensity={0.5}>\n        <Sphere args={[0.1]} position={[2, 1, 0.5]}>\n          <meshStandardMaterial\n            color={currentTheme.primary}\n            emissive={currentTheme.primary}\n            emissiveIntensity={0.5}\n            transparent\n            opacity={0.7}\n          />\n        </Sphere>\n      </Float>\n      \n      <Float speed={2} rotationIntensity={0.3} floatIntensity={0.3}>\n        <Sphere args={[0.05]} position={[-2, -1, 0.3]}>\n          <meshStandardMaterial\n            color={currentTheme.accent}\n            emissive={currentTheme.accent}\n            emissiveIntensity={0.8}\n            transparent\n            opacity={0.8}\n          />\n        </Sphere>\n      </Float>\n      \n      <Float speed={1.8} rotationIntensity={0.4} floatIntensity={0.4}>\n        <Sphere args={[0.08]} position={[1.5, -1.2, 0.4]}>\n          <meshStandardMaterial\n            color={currentTheme.secondary}\n            emissive={currentTheme.secondary}\n            emissiveIntensity={0.6}\n            transparent\n            opacity={0.6}\n          />\n        </Sphere>\n      </Float>\n    </>\n  );\n\n  return (\n    <group>\n      <ParticleField />\n      <CardSurface />\n      <AccentElements />\n      \n      {/* Contact shadows for realism */}\n      <ContactShadows\n        position={[0, -1.5, 0]}\n        opacity={0.4}\n        scale={5}\n        blur={2}\n        far={2}\n      />\n    </group>\n  );\n};\n\n// Main 3D Card Container\nconst Card3D = ({ cardData, className = '', theme = 'cyber', ...props }) => {\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    // Simulate loading time for dramatic effect\n    const timer = setTimeout(() => setIsLoaded(true), 500);\n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <div className={`relative w-full h-96 ${className}`} {...props}>\n      {!isLoaded && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-dark-space/50 backdrop-blur-sm rounded-2xl\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin w-8 h-8 border-2 border-neon-blue border-t-transparent rounded-full mx-auto mb-4\"></div>\n            <p className=\"text-text-secondary\">Initializing 3D Card...</p>\n          </div>\n        </div>\n      )}\n      \n      <Canvas\n        camera={{ position: [0, 0, 5], fov: 50 }}\n        style={{ \n          background: 'transparent',\n          opacity: isLoaded ? 1 : 0,\n          transition: 'opacity 0.5s ease-in-out'\n        }}\n      >\n        <ambientLight intensity={0.5} />\n        <pointLight position={[10, 10, 10]} intensity={1} color=\"#00f5ff\" />\n        <pointLight position={[-10, -10, -10]} intensity={0.5} color=\"#8b5cf6\" />\n        \n        <Environment preset=\"night\" />\n        \n        <BusinessCard3D cardData={cardData} theme={theme} />\n      </Canvas>\n      \n      {/* CSS for animations */}\n      <style jsx>{`\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.5; }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Card3D;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;;AAOA,sCAAsC;AACtC,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAE,QAAQ,OAAO,EAAE;IACpE,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACrB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,IAAI,UAAU;YAC/B,qBAAqB;YACrB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;YACjE,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;YAEvE,gBAAgB;YAChB,IAAI,SAAS;gBACX,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBAChC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YACvE,OAAO;gBACL,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBAChC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG;YAC/B;QACF;QAEA,oBAAoB;QACpB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;QACrC;IACF;IAEA,uBAAuB;IACvB,MAAM,SAAS;QACb,OAAO;YACL,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA,QAAQ;YACN,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA,MAAM;YACJ,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;QACd;IACF;IAEA,MAAM,eAAe,MAAM,CAAC,MAAM,IAAI,OAAO,KAAK;IAElD,yCAAyC;IACzC,MAAM,gBAAgB;QACpB,MAAM,gBAAgB;QACtB,MAAM,YAAY,IAAI,aAAa,gBAAgB;QAEnD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;YACtC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC3C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC/C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QACjD;QAEA,qBACE,8OAAC;YAAO,KAAK;;8BACX,8OAAC;8BACC,cAAA,8OAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;;;;;;8BAGd,8OAAC;oBACC,MAAM;oBACN,OAAO,aAAa,OAAO;oBAC3B,WAAW;oBACX,SAAS;oBACT,eAAe;;;;;;;;;;;;IAIvB;IAEA,2BAA2B;IAC3B,MAAM,cAAc,kBAClB,8OAAC,0JAAA,CAAA,MAAG;YACF,KAAK;YACL,MAAM;gBAAC;gBAAK;gBAAK;aAAI;YACrB,eAAe,IAAM,WAAW;YAChC,cAAc,IAAM,WAAW;YAC/B,SAAS,IAAM,WAAW,CAAC;;8BAE3B,8OAAC,uKAAA,CAAA,sBAAmB;oBAClB,OAAO,aAAa,UAAU;oBAC9B,WAAW;oBACX,SAAS;oBACT,SAAS,UAAU,MAAM;oBACzB,OAAO;oBACP,WAAW;oBACX,WAAW;;;;;;8BAIb,8OAAC,uJAAA,CAAA,OAAI;oBACH,SAAS;oBACT,OAAO;oBACP,UAAU;wBAAC;wBAAG;wBAAG;qBAAK;oBACtB,OAAO;wBACL,OAAO;wBACP,QAAQ;wBACR,YAAY,CAAC,wBAAwB,EAAE,aAAa,OAAO,CAAC,IAAI,EAAE,aAAa,SAAS,CAAC,GAAG,CAAC;wBAC7F,gBAAgB;wBAChB,QAAQ,CAAC,UAAU,EAAE,aAAa,OAAO,CAAC,EAAE,CAAC;wBAC7C,cAAc;wBACd,SAAS;wBACT,OAAO;wBACP,YAAY;wBACZ,SAAS;wBACT,eAAe;wBACf,gBAAgB;oBAClB;;sCAGA,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAO;;8CAC/D,8OAAC;oCAAI,OAAO;wCACV,UAAU;wCACV,QAAQ,CAAC,qBAAqB,EAAE,aAAa,OAAO,CAAC,CAAC,CAAC;oCACzD;8CACG,SAAS,MAAM;;;;;;8CAElB,8OAAC;;sDACC,8OAAC;4CAAG,OAAO;gDACT,QAAQ;gDACR,UAAU;gDACV,YAAY;gDACZ,YAAY,CAAC,uBAAuB,EAAE,aAAa,OAAO,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,CAAC,CAAC;gDACrF,sBAAsB;gDACtB,qBAAqB;4CACvB;sDACG,SAAS,IAAI;;;;;;sDAEhB,8OAAC;4CAAE,OAAO;gDACR,QAAQ;gDACR,UAAU;gDACV,SAAS;gDACT,OAAO,aAAa,SAAS;4CAC/B;sDACG,SAAS,KAAK;;;;;;;;;;;;;;;;;;sCAMrB,8OAAC;4BAAI,OAAO;gCAAE,WAAW;4BAAS;;8CAChC,8OAAC;oCAAE,OAAO;wCACR,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO,aAAa,OAAO;oCAC7B;8CACG,SAAS,OAAO;;;;;;8CAEnB,8OAAC;oCAAE,OAAO;wCACR,QAAQ;wCACR,UAAU;wCACV,WAAW;wCACX,SAAS;oCACX;8CACG,SAAS,KAAK;;;;;;;;;;;;sCAKnB,8OAAC;4BAAI,OAAO;gCAAE,UAAU;gCAAQ,SAAS;4BAAI;;8CAC3C,8OAAC;8CAAK,SAAS,KAAK;;;;;;8CACpB,8OAAC;8CAAK,SAAS,KAAK;;;;;;;;;;;;sCAItB,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,KAAK;gCACL,OAAO;gCACP,SAAS;gCACT,YAAY;gCACZ,KAAK;gCACL,UAAU;gCACV,OAAO,aAAa,OAAO;4BAC7B;;8CACE,8OAAC;oCAAI,OAAO;wCACV,OAAO;wCACP,QAAQ;wCACR,cAAc;wCACd,iBAAiB,aAAa,OAAO;wCACrC,WAAW;oCACb;;;;;;gCAAK;;;;;;;;;;;;;;;;;;;IAOb,2BAA2B;IAC3B,MAAM,iBAAiB,kBACrB;;8BACE,8OAAC,yJAAA,CAAA,QAAK;oBAAC,OAAO;oBAAK,mBAAmB;oBAAK,gBAAgB;8BACzD,cAAA,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;yBAAI;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAI;kCACxC,cAAA,8OAAC;4BACC,OAAO,aAAa,OAAO;4BAC3B,UAAU,aAAa,OAAO;4BAC9B,mBAAmB;4BACnB,WAAW;4BACX,SAAS;;;;;;;;;;;;;;;;8BAKf,8OAAC,yJAAA,CAAA,QAAK;oBAAC,OAAO;oBAAG,mBAAmB;oBAAK,gBAAgB;8BACvD,cAAA,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;yBAAK;wBAAE,UAAU;4BAAC,CAAC;4BAAG,CAAC;4BAAG;yBAAI;kCAC3C,cAAA,8OAAC;4BACC,OAAO,aAAa,MAAM;4BAC1B,UAAU,aAAa,MAAM;4BAC7B,mBAAmB;4BACnB,WAAW;4BACX,SAAS;;;;;;;;;;;;;;;;8BAKf,8OAAC,yJAAA,CAAA,QAAK;oBAAC,OAAO;oBAAK,mBAAmB;oBAAK,gBAAgB;8BACzD,cAAA,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;yBAAK;wBAAE,UAAU;4BAAC;4BAAK,CAAC;4BAAK;yBAAI;kCAC9C,cAAA,8OAAC;4BACC,OAAO,aAAa,SAAS;4BAC7B,UAAU,aAAa,SAAS;4BAChC,mBAAmB;4BACnB,WAAW;4BACX,SAAS;;;;;;;;;;;;;;;;;;IAOnB,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BAGD,8OAAC,kKAAA,CAAA,iBAAc;gBACb,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;gBACtB,SAAS;gBACT,OAAO;gBACP,MAAM;gBACN,KAAK;;;;;;;;;;;;AAIb;AAEA,yBAAyB;AACzB,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,QAAQ,OAAO,EAAE,GAAG,OAAO;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4CAA4C;QAC5C,MAAM,QAAQ,WAAW,IAAM,YAAY,OAAO;QAClD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAqD,GAAG,KAAK;mDAAL,SAAA,2BAAA,mBAAzC,CAAC,qBAAqB,EAAE,WAAW;;YAChD,CAAC,0BACA,8OAAC;0DAAc;0BACb,cAAA,8OAAC;8DAAc;;sCACb,8OAAC;sEAAc;;;;;;sCACf,8OAAC;sEAAY;sCAAsB;;;;;;;;;;;;;;;;;0BAKzC,8OAAC,mMAAA,CAAA,SAAM;gBACL,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;oBAAE,KAAK;gBAAG;gBACvC,OAAO;oBACL,YAAY;oBACZ,SAAS,WAAW,IAAI;oBACxB,YAAY;gBACd;;kCAEA,8OAAC;wBAAa,WAAW;;;;;;;kCACzB,8OAAC;wBAAW,UAAU;4BAAC;4BAAI;4BAAI;yBAAG;wBAAE,WAAW;wBAAG,OAAM;;;;;;;kCACxD,8OAAC;wBAAW,UAAU;4BAAC,CAAC;4BAAI,CAAC;4BAAI,CAAC;yBAAG;wBAAE,WAAW;wBAAK,OAAM;;;;;;;kCAE7D,8OAAC,+JAAA,CAAA,cAAW;wBAAC,QAAO;;;;;;kCAEpB,8OAAC;wBAAe,UAAU;wBAAU,OAAO;;;;;;;;;;;;;;;;;;;;;;AAYnD;uCAEe", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/3d/ARSimulation.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { Text, Html, useTexture, Plane } from '@react-three/drei';\nimport * as THREE from 'three';\n\n// AR Camera Feed Simulation\nconst ARCameraFeed = ({ onFaceDetected, isScanning }) => {\n  const videoRef = useRef();\n  const canvasRef = useRef();\n  const [faceDetected, setFaceDetected] = useState(false);\n  const [scanProgress, setScanProgress] = useState(0);\n\n  useEffect(() => {\n    if (isScanning) {\n      // Simulate face detection process\n      const detectFace = () => {\n        setScanProgress(prev => {\n          const newProgress = prev + 2;\n          if (newProgress >= 100) {\n            setFaceDetected(true);\n            onFaceDetected && onFaceDetected({\n              confidence: 0.95,\n              position: { x: 0.3, y: 0.2 },\n              size: { width: 0.4, height: 0.6 }\n            });\n            return 100;\n          }\n          return newProgress;\n        });\n      };\n\n      const interval = setInterval(detectFace, 50);\n      return () => clearInterval(interval);\n    } else {\n      setScanProgress(0);\n      setFaceDetected(false);\n    }\n  }, [isScanning, onFaceDetected]);\n\n  return (\n    <div className=\"relative w-full h-full bg-gradient-to-br from-midnight-blue to-deep-purple rounded-lg overflow-hidden\">\n      {/* Simulated Camera Feed */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-dark-space/80 to-midnight-blue/80\">\n        {/* Grid overlay for AR feel */}\n        <div className=\"absolute inset-0 opacity-20\">\n          <svg width=\"100%\" height=\"100%\" className=\"absolute inset-0\">\n            <defs>\n              <pattern id=\"grid\" width=\"40\" height=\"40\" patternUnits=\"userSpaceOnUse\">\n                <path d=\"M 40 0 L 0 0 0 40\" fill=\"none\" stroke=\"#00f5ff\" strokeWidth=\"0.5\"/>\n              </pattern>\n            </defs>\n            <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\n          </svg>\n        </div>\n\n        {/* Face Detection Overlay */}\n        {isScanning && (\n          <div className=\"absolute inset-0 flex items-center justify-center\">\n            {/* Scanning Frame */}\n            <div className=\"relative w-64 h-80 border-2 border-neon-blue/50 rounded-lg\">\n              {/* Corner brackets */}\n              <div className=\"absolute top-0 left-0 w-8 h-8 border-t-2 border-l-2 border-neon-blue\"></div>\n              <div className=\"absolute top-0 right-0 w-8 h-8 border-t-2 border-r-2 border-neon-blue\"></div>\n              <div className=\"absolute bottom-0 left-0 w-8 h-8 border-b-2 border-l-2 border-neon-blue\"></div>\n              <div className=\"absolute bottom-0 right-0 w-8 h-8 border-b-2 border-r-2 border-neon-blue\"></div>\n\n              {/* Scanning line */}\n              <div \n                className=\"absolute left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-neon-blue to-transparent transition-all duration-100\"\n                style={{ \n                  top: `${scanProgress}%`,\n                  boxShadow: '0 0 10px #00f5ff'\n                }}\n              />\n\n              {/* Face detected indicator */}\n              {faceDetected && (\n                <div className=\"absolute inset-4 border-2 border-cyber-green rounded-lg bg-cyber-green/10 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl mb-2\">👤</div>\n                    <div className=\"text-cyber-green text-sm font-semibold\">FACE DETECTED</div>\n                    <div className=\"text-cyber-green text-xs\">Confidence: 95%</div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* AR UI Elements */}\n        <div className=\"absolute top-4 left-4 text-neon-blue text-sm font-mono\">\n          <div>AR MODE: ACTIVE</div>\n          <div>SCAN PROGRESS: {scanProgress}%</div>\n          <div>STATUS: {faceDetected ? 'FACE DETECTED' : isScanning ? 'SCANNING...' : 'READY'}</div>\n        </div>\n\n        {/* Center crosshair */}\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\">\n          <div className=\"w-8 h-8 border border-neon-blue/50 rounded-full flex items-center justify-center\">\n            <div className=\"w-2 h-2 bg-neon-blue rounded-full animate-pulse\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// 3D AR Card Overlay\nconst ARCardOverlay = ({ cardData, position = [0, 0, 0], visible = false }) => {\n  const groupRef = useRef();\n  const [scale, setScale] = useState(0);\n\n  useFrame((state) => {\n    if (groupRef.current && visible) {\n      // Entrance animation\n      setScale(prev => Math.min(prev + 0.05, 1));\n      \n      // Floating animation\n      groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;\n      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1;\n    } else {\n      setScale(0);\n    }\n  });\n\n  if (!visible) return null;\n\n  return (\n    <group ref={groupRef} position={position} scale={[scale, scale, scale]}>\n      {/* Card Background */}\n      <Plane args={[2, 1.2]} position={[0, 0, -0.01]}>\n        <meshStandardMaterial\n          color=\"#0a0a0f\"\n          transparent\n          opacity={0.9}\n          side={THREE.DoubleSide}\n        />\n      </Plane>\n\n      {/* Card Border */}\n      <Plane args={[2.1, 1.3]} position={[0, 0, -0.02]}>\n        <meshStandardMaterial\n          color=\"#00f5ff\"\n          transparent\n          opacity={0.3}\n          side={THREE.DoubleSide}\n        />\n      </Plane>\n\n      {/* Card Content */}\n      <Html\n        transform\n        distanceFactor={10}\n        position={[0, 0, 0.01]}\n        style={{\n          width: '200px',\n          height: '120px',\n          background: 'linear-gradient(135deg, rgba(0,245,255,0.1), rgba(139,92,246,0.1))',\n          backdropFilter: 'blur(10px)',\n          border: '1px solid rgba(0,245,255,0.3)',\n          borderRadius: '8px',\n          padding: '12px',\n          color: 'white',\n          fontSize: '10px',\n          fontFamily: 'Inter, sans-serif'\n        }}\n      >\n        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>\n          <div style={{ fontSize: '16px' }}>{cardData.avatar}</div>\n          <div>\n            <div style={{ fontWeight: 'bold', fontSize: '12px' }}>{cardData.name}</div>\n            <div style={{ opacity: 0.8, fontSize: '9px' }}>{cardData.title}</div>\n          </div>\n        </div>\n        \n        <div style={{ textAlign: 'center', marginBottom: '8px' }}>\n          <div style={{ fontWeight: 'bold', color: '#00f5ff' }}>{cardData.company}</div>\n        </div>\n        \n        <div style={{ fontSize: '8px', opacity: 0.7 }}>\n          <div>{cardData.email}</div>\n          <div>{cardData.phone}</div>\n        </div>\n\n        {/* Action buttons */}\n        <div style={{ \n          display: 'flex', \n          gap: '4px', \n          marginTop: '8px',\n          justifyContent: 'center'\n        }}>\n          <button style={{\n            background: 'rgba(0,245,255,0.2)',\n            border: '1px solid #00f5ff',\n            borderRadius: '4px',\n            color: '#00f5ff',\n            fontSize: '8px',\n            padding: '2px 6px',\n            cursor: 'pointer'\n          }}>\n            Connect\n          </button>\n          <button style={{\n            background: 'rgba(139,92,246,0.2)',\n            border: '1px solid #8b5cf6',\n            borderRadius: '4px',\n            color: '#8b5cf6',\n            fontSize: '8px',\n            padding: '2px 6px',\n            cursor: 'pointer'\n          }}>\n            Save\n          </button>\n        </div>\n      </Html>\n\n      {/* Particle effects */}\n      <points>\n        <bufferGeometry>\n          <bufferAttribute\n            attach=\"attributes-position\"\n            count={20}\n            array={new Float32Array(Array.from({ length: 60 }, () => (Math.random() - 0.5) * 3))}\n            itemSize={3}\n          />\n        </bufferGeometry>\n        <pointsMaterial\n          size={0.02}\n          color=\"#00f5ff\"\n          transparent\n          opacity={0.6}\n        />\n      </points>\n    </group>\n  );\n};\n\n// Main AR Simulation Component\nconst ARSimulation = ({ cardData, isActive = false, onCardDetected }) => {\n  const [isScanning, setIsScanning] = useState(false);\n  const [cardVisible, setCardVisible] = useState(false);\n  const [detectedFace, setDetectedFace] = useState(null);\n\n  const handleStartScan = () => {\n    setIsScanning(true);\n    setCardVisible(false);\n  };\n\n  const handleFaceDetected = (faceData) => {\n    setDetectedFace(faceData);\n    setTimeout(() => {\n      setCardVisible(true);\n      setIsScanning(false);\n      onCardDetected && onCardDetected(cardData);\n    }, 1000);\n  };\n\n  const handleStopScan = () => {\n    setIsScanning(false);\n    setCardVisible(false);\n    setDetectedFace(null);\n  };\n\n  return (\n    <div className=\"relative w-full h-96 bg-dark-space rounded-2xl overflow-hidden\">\n      {/* AR Camera Feed */}\n      <ARCameraFeed \n        onFaceDetected={handleFaceDetected}\n        isScanning={isScanning}\n      />\n\n      {/* 3D AR Overlay */}\n      <div className=\"absolute inset-0\">\n        <Canvas\n          camera={{ position: [0, 0, 5], fov: 50 }}\n          style={{ background: 'transparent' }}\n        >\n          <ambientLight intensity={0.5} />\n          <pointLight position={[10, 10, 10]} intensity={1} color=\"#00f5ff\" />\n          \n          <ARCardOverlay\n            cardData={cardData}\n            position={[0, 0, 0]}\n            visible={cardVisible}\n          />\n        </Canvas>\n      </div>\n\n      {/* Control Panel */}\n      <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-4\">\n        {!isScanning && !cardVisible && (\n          <button\n            onClick={handleStartScan}\n            className=\"px-6 py-2 bg-neon-blue/20 border border-neon-blue rounded-lg text-neon-blue hover:bg-neon-blue/30 transition-colors\"\n          >\n            Start AR Scan\n          </button>\n        )}\n        \n        {(isScanning || cardVisible) && (\n          <button\n            onClick={handleStopScan}\n            className=\"px-6 py-2 bg-hologram-pink/20 border border-hologram-pink rounded-lg text-hologram-pink hover:bg-hologram-pink/30 transition-colors\"\n          >\n            Stop Scan\n          </button>\n        )}\n      </div>\n\n      {/* Status Indicator */}\n      <div className=\"absolute top-4 right-4 flex items-center gap-2\">\n        <div className={`w-3 h-3 rounded-full ${\n          cardVisible ? 'bg-cyber-green animate-pulse' :\n          isScanning ? 'bg-neon-blue animate-ping' :\n          'bg-text-secondary/50'\n        }`} />\n        <span className=\"text-sm text-text-secondary\">\n          {cardVisible ? 'Card Detected' :\n           isScanning ? 'Scanning...' :\n           'AR Ready'}\n        </span>\n      </div>\n    </div>\n  );\n};\n\nexport default ARSimulation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AALA;;;;;;AAOA,4BAA4B;AAC5B,MAAM,eAAe,CAAC,EAAE,cAAc,EAAE,UAAU,EAAE;IAClD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,kCAAkC;YAClC,MAAM,aAAa;gBACjB,gBAAgB,CAAA;oBACd,MAAM,cAAc,OAAO;oBAC3B,IAAI,eAAe,KAAK;wBACtB,gBAAgB;wBAChB,kBAAkB,eAAe;4BAC/B,YAAY;4BACZ,UAAU;gCAAE,GAAG;gCAAK,GAAG;4BAAI;4BAC3B,MAAM;gCAAE,OAAO;gCAAK,QAAQ;4BAAI;wBAClC;wBACA,OAAO;oBACT;oBACA,OAAO;gBACT;YACF;YAEA,MAAM,WAAW,YAAY,YAAY;YACzC,OAAO,IAAM,cAAc;QAC7B,OAAO;YACL,gBAAgB;YAChB,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAY;KAAe;IAE/B,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,OAAM;wBAAO,QAAO;wBAAO,WAAU;;0CACxC,8OAAC;0CACC,cAAA,8OAAC;oCAAQ,IAAG;oCAAO,OAAM;oCAAK,QAAO;oCAAK,cAAa;8CACrD,cAAA,8OAAC;wCAAK,GAAE;wCAAoB,MAAK;wCAAO,QAAO;wCAAU,aAAY;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAK,OAAM;gCAAO,QAAO;gCAAO,MAAK;;;;;;;;;;;;;;;;;gBAKzC,4BACC,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,KAAK,GAAG,aAAa,CAAC,CAAC;oCACvB,WAAW;gCACb;;;;;;4BAID,8BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAAyC;;;;;;sDACxD,8OAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAI;;;;;;sCACL,8OAAC;;gCAAI;gCAAgB;gCAAa;;;;;;;sCAClC,8OAAC;;gCAAI;gCAAS,eAAe,kBAAkB,aAAa,gBAAgB;;;;;;;;;;;;;8BAI9E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;AAEA,qBAAqB;AACrB,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,WAAW;IAAC;IAAG;IAAG;CAAE,EAAE,UAAU,KAAK,EAAE;IACxE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,SAAS,OAAO,IAAI,SAAS;YAC/B,qBAAqB;YACrB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,MAAM;YAEvC,qBAAqB;YACrB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YACpF,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;QACpE,OAAO;YACL,SAAS;QACX;IACF;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;QAAU,OAAO;YAAC;YAAO;YAAO;SAAM;;0BAEpE,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;0BAC5C,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;oBACT,MAAM,+IAAA,CAAA,aAAgB;;;;;;;;;;;0BAK1B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;0BAC9C,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;oBACT,MAAM,+IAAA,CAAA,aAAgB;;;;;;;;;;;0BAK1B,8OAAC,uJAAA,CAAA,OAAI;gBACH,SAAS;gBACT,gBAAgB;gBAChB,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBACtB,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,cAAc;oBACd,SAAS;oBACT,OAAO;oBACP,UAAU;oBACV,YAAY;gBACd;;kCAEA,8OAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;4BAAO,cAAc;wBAAM;;0CACnF,8OAAC;gCAAI,OAAO;oCAAE,UAAU;gCAAO;0CAAI,SAAS,MAAM;;;;;;0CAClD,8OAAC;;kDACC,8OAAC;wCAAI,OAAO;4CAAE,YAAY;4CAAQ,UAAU;wCAAO;kDAAI,SAAS,IAAI;;;;;;kDACpE,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAK,UAAU;wCAAM;kDAAI,SAAS,KAAK;;;;;;;;;;;;;;;;;;kCAIlE,8OAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,cAAc;wBAAM;kCACrD,cAAA,8OAAC;4BAAI,OAAO;gCAAE,YAAY;gCAAQ,OAAO;4BAAU;sCAAI,SAAS,OAAO;;;;;;;;;;;kCAGzE,8OAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAO,SAAS;wBAAI;;0CAC1C,8OAAC;0CAAK,SAAS,KAAK;;;;;;0CACpB,8OAAC;0CAAK,SAAS,KAAK;;;;;;;;;;;;kCAItB,8OAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,KAAK;4BACL,WAAW;4BACX,gBAAgB;wBAClB;;0CACE,8OAAC;gCAAO,OAAO;oCACb,YAAY;oCACZ,QAAQ;oCACR,cAAc;oCACd,OAAO;oCACP,UAAU;oCACV,SAAS;oCACT,QAAQ;gCACV;0CAAG;;;;;;0CAGH,8OAAC;gCAAO,OAAO;oCACb,YAAY;oCACZ,QAAQ;oCACR,cAAc;oCACd,OAAO;oCACP,UAAU;oCACV,SAAS;oCACT,QAAQ;gCACV;0CAAG;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;;kCACC,8OAAC;kCACC,cAAA,8OAAC;4BACC,QAAO;4BACP,OAAO;4BACP,OAAO,IAAI,aAAa,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAG,GAAG,IAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BACjF,UAAU;;;;;;;;;;;kCAGd,8OAAC;wBACC,MAAM;wBACN,OAAM;wBACN,WAAW;wBACX,SAAS;;;;;;;;;;;;;;;;;;AAKnB;AAEA,+BAA+B;AAC/B,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,cAAc,EAAE;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,kBAAkB;QACtB,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;QAChB,WAAW;YACT,eAAe;YACf,cAAc;YACd,kBAAkB,eAAe;QACnC,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,eAAe;QACf,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,gBAAgB;gBAChB,YAAY;;;;;;0BAId,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mMAAA,CAAA,SAAM;oBACL,QAAQ;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,KAAK;oBAAG;oBACvC,OAAO;wBAAE,YAAY;oBAAc;;sCAEnC,8OAAC;4BAAa,WAAW;;;;;;sCACzB,8OAAC;4BAAW,UAAU;gCAAC;gCAAI;gCAAI;6BAAG;4BAAE,WAAW;4BAAG,OAAM;;;;;;sCAExD,8OAAC;4BACC,UAAU;4BACV,UAAU;gCAAC;gCAAG;gCAAG;6BAAE;4BACnB,SAAS;;;;;;;;;;;;;;;;;0BAMf,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,cAAc,CAAC,6BACf,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;oBAKF,CAAC,cAAc,WAAW,mBACzB,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,qBAAqB,EACpC,cAAc,iCACd,aAAa,8BACb,wBACA;;;;;;kCACF,8OAAC;wBAAK,WAAU;kCACb,cAAc,kBACd,aAAa,gBACb;;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/ai/VoiceInteraction.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst VoiceInteraction = ({ cardData, isActive = false, onInteraction }) => {\n  const [isListening, setIsListening] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isSpeaking, setIsSpeaking] = useState(false);\n  const [transcript, setTranscript] = useState('');\n  const [aiResponse, setAiResponse] = useState('');\n  const [conversation, setConversation] = useState([]);\n  const [voiceLevel, setVoiceLevel] = useState(0);\n  \n  const recognitionRef = useRef(null);\n  const synthRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n\n  // Initialize speech recognition and synthesis\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Speech Recognition\n      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {\n        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n        recognitionRef.current = new SpeechRecognition();\n        recognitionRef.current.continuous = true;\n        recognitionRef.current.interimResults = true;\n        recognitionRef.current.lang = 'en-US';\n\n        recognitionRef.current.onresult = (event) => {\n          let finalTranscript = '';\n          for (let i = event.resultIndex; i < event.results.length; i++) {\n            if (event.results[i].isFinal) {\n              finalTranscript += event.results[i][0].transcript;\n            }\n          }\n          if (finalTranscript) {\n            setTranscript(finalTranscript);\n            handleVoiceInput(finalTranscript);\n          }\n        };\n\n        recognitionRef.current.onerror = (event) => {\n          console.error('Speech recognition error:', event.error);\n          setIsListening(false);\n        };\n      }\n\n      // Speech Synthesis\n      if ('speechSynthesis' in window) {\n        synthRef.current = window.speechSynthesis;\n      }\n    }\n  }, []);\n\n  // AI Response Generator\n  const generateAIResponse = (userInput) => {\n    const input = userInput.toLowerCase();\n    const { aiPersonality, recentAchievements, currentProjects, availableActions } = cardData;\n\n    // Context-aware responses\n    if (input.includes('hello') || input.includes('hi') || input.includes('introduce')) {\n      return aiPersonality.voiceIntro;\n    }\n    \n    if (input.includes('project') || input.includes('working on')) {\n      const project = currentProjects[Math.floor(Math.random() * currentProjects.length)];\n      return `I'm currently working on ${project}. It's really exciting because it combines ${aiPersonality.interests.slice(0, 2).join(' and ')}.`;\n    }\n    \n    if (input.includes('achievement') || input.includes('accomplishment')) {\n      const achievement = recentAchievements[Math.floor(Math.random() * recentAchievements.length)];\n      return `One of my recent achievements is ${achievement}. I'm particularly proud of this because it aligns with my passion for ${aiPersonality.interests[0]}.`;\n    }\n    \n    if (input.includes('meet') || input.includes('schedule') || input.includes('coffee')) {\n      return `I'd love to meet! You can schedule a meeting through my calendar link, or we can connect on LinkedIn first. What works best for you?`;\n    }\n    \n    if (input.includes('contact') || input.includes('reach')) {\n      return `The best way to reach me is via email at ${cardData.email}, or you can connect with me on LinkedIn. I typically respond within 24 hours.`;\n    }\n    \n    if (input.includes('company') || input.includes('work')) {\n      return `I work at ${cardData.company}, where we focus on ${cardData.industry}. We're doing some groundbreaking work in ${aiPersonality.interests[0]}.`;\n    }\n\n    // Default responses based on personality\n    const responses = [\n      `That's interesting! As someone who's ${aiPersonality.personalityTraits.join(', ')}, I'd love to explore that topic further.`,\n      `Great question! My experience in ${cardData.industry} has taught me that ${aiPersonality.interests[0]} is key to innovation.`,\n      `I appreciate your curiosity! Feel free to reach out anytime to discuss ${aiPersonality.interests.slice(0, 2).join(' or ')}.`\n    ];\n    \n    return responses[Math.floor(Math.random() * responses.length)];\n  };\n\n  // Handle voice input\n  const handleVoiceInput = async (input) => {\n    setIsListening(false);\n    setIsProcessing(true);\n    \n    // Add user message to conversation\n    const userMessage = { type: 'user', content: input, timestamp: Date.now() };\n    setConversation(prev => [...prev, userMessage]);\n    \n    // Simulate AI processing delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    // Generate AI response\n    const response = generateAIResponse(input);\n    setAiResponse(response);\n    \n    // Add AI response to conversation\n    const aiMessage = { type: 'ai', content: response, timestamp: Date.now() };\n    setConversation(prev => [...prev, aiMessage]);\n    \n    setIsProcessing(false);\n    \n    // Speak the response\n    speakResponse(response);\n    \n    // Trigger interaction callback\n    onInteraction && onInteraction({ type: 'voice_interaction', input, response });\n  };\n\n  // Text-to-speech\n  const speakResponse = (text) => {\n    if (synthRef.current && text) {\n      setIsSpeaking(true);\n      const utterance = new SpeechSynthesisUtterance(text);\n      utterance.rate = 0.9;\n      utterance.pitch = 1.1;\n      utterance.volume = 0.8;\n      \n      // Try to use a more natural voice\n      const voices = synthRef.current.getVoices();\n      const preferredVoice = voices.find(voice => \n        voice.name.includes('Google') || \n        voice.name.includes('Microsoft') ||\n        voice.lang.includes('en-US')\n      );\n      if (preferredVoice) {\n        utterance.voice = preferredVoice;\n      }\n      \n      utterance.onend = () => setIsSpeaking(false);\n      utterance.onerror = () => setIsSpeaking(false);\n      \n      synthRef.current.speak(utterance);\n    }\n  };\n\n  // Start/stop listening\n  const toggleListening = () => {\n    if (isListening) {\n      recognitionRef.current?.stop();\n      setIsListening(false);\n    } else {\n      if (recognitionRef.current) {\n        recognitionRef.current.start();\n        setIsListening(true);\n        setTranscript('');\n      }\n    }\n  };\n\n  // Voice level visualization\n  const VoiceVisualizer = () => {\n    const bars = Array.from({ length: 5 }, (_, i) => (\n      <div\n        key={i}\n        className={`w-2 bg-neon-blue rounded-full transition-all duration-150 ${\n          isListening ? 'animate-pulse' : ''\n        }`}\n        style={{\n          height: `${Math.random() * 40 + 10}px`,\n          animationDelay: `${i * 0.1}s`\n        }}\n      />\n    ));\n\n    return (\n      <div className=\"flex items-end gap-1 h-12 justify-center\">\n        {bars}\n      </div>\n    );\n  };\n\n  // AI Avatar Animation\n  const AIAvatar = () => (\n    <div className=\"relative\">\n      <div className={`text-6xl transition-all duration-300 ${\n        isSpeaking ? 'animate-bounce' : \n        isProcessing ? 'animate-pulse' : \n        isListening ? 'animate-ping' : ''\n      }`}>\n        🤖\n      </div>\n      \n      {/* Status indicator */}\n      <div className=\"absolute -bottom-2 -right-2\">\n        <div className={`w-4 h-4 rounded-full ${\n          isSpeaking ? 'bg-cyber-green animate-pulse' :\n          isProcessing ? 'bg-quantum-gold animate-spin' :\n          isListening ? 'bg-neon-blue animate-ping' :\n          'bg-text-secondary/50'\n        }`} />\n      </div>\n    </div>\n  );\n\n  return (\n    <Card variant=\"glass\" className=\"p-6\">\n      <CardContent>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold text-text-primary mb-2\">\n              AI Voice Assistant\n            </h3>\n            <p className=\"text-text-secondary\">\n              Talk to {cardData.name}'s AI persona\n            </p>\n          </div>\n\n          {/* AI Avatar */}\n          <div className=\"flex justify-center\">\n            <AIAvatar />\n          </div>\n\n          {/* Voice Visualizer */}\n          {isListening && (\n            <div className=\"flex justify-center\">\n              <VoiceVisualizer />\n            </div>\n          )}\n\n          {/* Status Display */}\n          <div className=\"text-center\">\n            {isListening && (\n              <p className=\"text-neon-blue\">🎤 Listening...</p>\n            )}\n            {isProcessing && (\n              <p className=\"text-quantum-gold\">🧠 Processing...</p>\n            )}\n            {isSpeaking && (\n              <p className=\"text-cyber-green\">🔊 Speaking...</p>\n            )}\n            {!isListening && !isProcessing && !isSpeaking && (\n              <p className=\"text-text-secondary\">Ready to chat</p>\n            )}\n          </div>\n\n          {/* Conversation History */}\n          {conversation.length > 0 && (\n            <div className=\"max-h-40 overflow-y-auto space-y-2 bg-dark-space/50 rounded-lg p-4\">\n              {conversation.slice(-4).map((message, index) => (\n                <div\n                  key={index}\n                  className={`text-sm ${\n                    message.type === 'user' \n                      ? 'text-neon-blue text-right' \n                      : 'text-cyber-green text-left'\n                  }`}\n                >\n                  <strong>{message.type === 'user' ? 'You' : cardData.name}:</strong>\n                  <br />\n                  {message.content}\n                </div>\n              ))}\n            </div>\n          )}\n\n          {/* Controls */}\n          <div className=\"flex justify-center gap-4\">\n            <Button\n              onClick={toggleListening}\n              variant={isListening ? 'danger' : 'primary'}\n              size=\"lg\"\n              disabled={isProcessing || isSpeaking}\n            >\n              {isListening ? '🛑 Stop' : '🎤 Talk'}\n            </Button>\n            \n            {aiResponse && (\n              <Button\n                onClick={() => speakResponse(aiResponse)}\n                variant=\"outline\"\n                size=\"lg\"\n                disabled={isSpeaking}\n              >\n                🔊 Repeat\n              </Button>\n            )}\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"grid grid-cols-2 gap-2\">\n            {cardData.availableActions?.slice(0, 4).map((action, index) => (\n              <button\n                key={index}\n                onClick={() => handleVoiceInput(`Tell me about ${action.label.toLowerCase()}`)}\n                className=\"p-2 text-xs bg-surface-dark/50 border border-electric-purple/30 rounded-lg text-text-secondary hover:text-neon-blue hover:border-neon-blue/50 transition-colors\"\n              >\n                {action.icon} {action.label}\n              </button>\n            ))}\n          </div>\n\n          {/* Browser Support Warning */}\n          {typeof window !== 'undefined' && !('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window) && (\n            <div className=\"text-center text-hologram-pink text-sm\">\n              ⚠️ Voice recognition not supported in this browser\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default VoiceInteraction;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,aAAa,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAgCnC;IACF,GAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,UAAU,WAAW;QACnC,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG;QAEjF,0BAA0B;QAC1B,IAAI,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,cAAc;YAClF,OAAO,cAAc,UAAU;QACjC;QAEA,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,eAAe;YAC7D,MAAM,UAAU,eAAe,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,gBAAgB,MAAM,EAAE;YACnF,OAAO,CAAC,yBAAyB,EAAE,QAAQ,2CAA2C,EAAE,cAAc,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9I;QAEA,IAAI,MAAM,QAAQ,CAAC,kBAAkB,MAAM,QAAQ,CAAC,mBAAmB;YACrE,MAAM,cAAc,kBAAkB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,mBAAmB,MAAM,EAAE;YAC7F,OAAO,CAAC,iCAAiC,EAAE,YAAY,uEAAuE,EAAE,cAAc,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/J;QAEA,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,eAAe,MAAM,QAAQ,CAAC,WAAW;YACpF,OAAO,CAAC,oIAAoI,CAAC;QAC/I;QAEA,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,UAAU;YACxD,OAAO,CAAC,yCAAyC,EAAE,SAAS,KAAK,CAAC,8EAA8E,CAAC;QACnJ;QAEA,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,SAAS;YACvD,OAAO,CAAC,UAAU,EAAE,SAAS,OAAO,CAAC,oBAAoB,EAAE,SAAS,QAAQ,CAAC,0CAA0C,EAAE,cAAc,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QACxJ;QAEA,yCAAyC;QACzC,MAAM,YAAY;YAChB,CAAC,qCAAqC,EAAE,cAAc,iBAAiB,CAAC,IAAI,CAAC,MAAM,yCAAyC,CAAC;YAC7H,CAAC,iCAAiC,EAAE,SAAS,QAAQ,CAAC,oBAAoB,EAAE,cAAc,SAAS,CAAC,EAAE,CAAC,sBAAsB,CAAC;YAC9H,CAAC,uEAAuE,EAAE,cAAc,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC9H;QAED,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;IAChE;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,OAAO;QAC9B,eAAe;QACf,gBAAgB;QAEhB,mCAAmC;QACnC,MAAM,cAAc;YAAE,MAAM;YAAQ,SAAS;YAAO,WAAW,KAAK,GAAG;QAAG;QAC1E,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAE9C,+BAA+B;QAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,uBAAuB;QACvB,MAAM,WAAW,mBAAmB;QACpC,cAAc;QAEd,kCAAkC;QAClC,MAAM,YAAY;YAAE,MAAM;YAAM,SAAS;YAAU,WAAW,KAAK,GAAG;QAAG;QACzE,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;aAAU;QAE5C,gBAAgB;QAEhB,qBAAqB;QACrB,cAAc;QAEd,+BAA+B;QAC/B,iBAAiB,cAAc;YAAE,MAAM;YAAqB;YAAO;QAAS;IAC9E;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,OAAO,IAAI,MAAM;YAC5B,cAAc;YACd,MAAM,YAAY,IAAI,yBAAyB;YAC/C,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,kCAAkC;YAClC,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,aACpB,MAAM,IAAI,CAAC,QAAQ,CAAC,gBACpB,MAAM,IAAI,CAAC,QAAQ,CAAC;YAEtB,IAAI,gBAAgB;gBAClB,UAAU,KAAK,GAAG;YACpB;YAEA,UAAU,KAAK,GAAG,IAAM,cAAc;YACtC,UAAU,OAAO,GAAG,IAAM,cAAc;YAExC,SAAS,OAAO,CAAC,KAAK,CAAC;QACzB;IACF;IAEA,uBAAuB;IACvB,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,eAAe,OAAO,EAAE;YACxB,eAAe;QACjB,OAAO;YACL,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,KAAK;gBAC5B,eAAe;gBACf,cAAc;YAChB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB;QACtB,MAAM,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACzC,8OAAC;gBAEC,WAAW,CAAC,0DAA0D,EACpE,cAAc,kBAAkB,IAChC;gBACF,OAAO;oBACL,QAAQ,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,EAAE,CAAC;oBACtC,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;gBAC/B;eAPK;;;;;QAWT,qBACE,8OAAC;YAAI,WAAU;sBACZ;;;;;;IAGP;IAEA,sBAAsB;IACtB,MAAM,WAAW,kBACf,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,CAAC,qCAAqC,EACpD,aAAa,mBACb,eAAe,kBACf,cAAc,iBAAiB,IAC/B;8BAAE;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,CAAC,qBAAqB,EACpC,aAAa,iCACb,eAAe,iCACf,cAAc,8BACd,wBACA;;;;;;;;;;;;;;;;;IAKR,qBACE,8OAAC,+HAAA,CAAA,OAAI;QAAC,SAAQ;QAAQ,WAAU;kBAC9B,cAAA,8OAAC,+HAAA,CAAA,cAAW;sBACV,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,8OAAC;gCAAE,WAAU;;oCAAsB;oCACxB,SAAS,IAAI;oCAAC;;;;;;;;;;;;;kCAK3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;;;;;;;;;oBAIF,6BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;;;;;;;;;kCAKL,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC;gCAAE,WAAU;0CAAiB;;;;;;4BAE/B,8BACC,8OAAC;gCAAE,WAAU;0CAAoB;;;;;;4BAElC,4BACC,8OAAC;gCAAE,WAAU;0CAAmB;;;;;;4BAEjC,CAAC,eAAe,CAAC,gBAAgB,CAAC,4BACjC,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;oBAKtC,aAAa,MAAM,GAAG,mBACrB,8OAAC;wBAAI,WAAU;kCACZ,aAAa,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,sBACpC,8OAAC;gCAEC,WAAW,CAAC,QAAQ,EAClB,QAAQ,IAAI,KAAK,SACb,8BACA,8BACJ;;kDAEF,8OAAC;;4CAAQ,QAAQ,IAAI,KAAK,SAAS,QAAQ,SAAS,IAAI;4CAAC;;;;;;;kDACzD,8OAAC;;;;;oCACA,QAAQ,OAAO;;+BATX;;;;;;;;;;kCAgBb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,UAAM;gCACL,SAAS;gCACT,SAAS,cAAc,WAAW;gCAClC,MAAK;gCACL,UAAU,gBAAgB;0CAEzB,cAAc,YAAY;;;;;;4BAG5B,4BACC,8OAAC,iIAAA,CAAA,UAAM;gCACL,SAAS,IAAM,cAAc;gCAC7B,SAAQ;gCACR,MAAK;gCACL,UAAU;0CACX;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,SAAS,gBAAgB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,sBACnD,8OAAC;gCAEC,SAAS,IAAM,iBAAiB,CAAC,cAAc,EAAE,OAAO,KAAK,CAAC,WAAW,IAAI;gCAC7E,WAAU;;oCAET,OAAO,IAAI;oCAAC;oCAAE,OAAO,KAAK;;+BAJtB;;;;;;;;;;oBAUV,gBAAkB,eAAe,CAAC,CAAC,6BAA6B,MAAM,KAAK,CAAC,CAAC,uBAAuB,MAAM,mBACzG,8OAAC;wBAAI,WAAU;kCAAyC;;;;;;;;;;;;;;;;;;;;;;AAQpE;uCAEe", "debugId": null}}, {"offset": {"line": 1921, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/data/sampleCards.js"], "sourcesContent": ["// Advanced AI-Powered Business Card Data\n\nexport const sampleCards = [\n  {\n    id: 'ai-ceo-001',\n    name: 'Dr. <PERSON>',\n    title: 'CEO & AI Research Director',\n    company: 'QuantumMind Technologies',\n    industry: 'Artificial Intelligence',\n    location: 'San Francisco, CA',\n    \n    // Contact Information\n    email: '<EMAIL>',\n    phone: '+****************',\n    website: 'quantummind.ai',\n    linkedin: 'linkedin.com/in/sarahchen-ai',\n    twitter: '@sarahchen_ai',\n    \n    // Visual Identity\n    avatar: '🧠',\n    profileImage: '/avatars/sarah-chen.jpg',\n    companyLogo: '/logos/quantummind.svg',\n    \n    // AI-Enhanced Features\n    aiPersonality: {\n      voiceIntro: 'Hi! I\\'m <PERSON>, and I\\'m passionate about building AI that enhances human potential.',\n      personalityTraits: ['Innovative', 'Analytical', 'Visionary', 'Collaborative'],\n      communicationStyle: 'Direct and inspiring',\n      interests: ['Machine Learning', 'Quantum Computing', 'Sustainable Tech', 'Mentoring']\n    },\n    \n    // Dynamic Content\n    recentAchievements: [\n      'Led breakthrough in quantum-classical hybrid AI models',\n      'Raised $50M Series B for QuantumMind Technologies',\n      'Keynote speaker at AI Summit 2024',\n      'Published 15 papers in top-tier AI journals'\n    ],\n    \n    currentProjects: [\n      'Quantum Neural Networks for Drug Discovery',\n      'AI Ethics Framework for Enterprise',\n      'Next-gen Natural Language Processing'\n    ],\n    \n    // Interactive Elements\n    availableActions: [\n      { type: 'schedule_meeting', label: 'Schedule a Meeting', icon: '📅' },\n      { type: 'view_portfolio', label: 'View Research Portfolio', icon: '📊' },\n      { type: 'connect_linkedin', label: 'Connect on LinkedIn', icon: '🔗' },\n      { type: 'download_whitepaper', label: 'Download Latest Whitepaper', icon: '📄' },\n      { type: 'join_newsletter', label: 'Join AI Newsletter', icon: '📧' }\n    ],\n    \n    // AR/3D Customization\n    cardTheme: {\n      primary: '#00f5ff',\n      secondary: '#8b5cf6',\n      accent: '#ff006e',\n      background: 'quantum-gradient',\n      effects: ['neural-network', 'particle-flow', 'hologram-glitch'],\n      animation: 'quantum-float'\n    },\n    \n    // Smart Features\n    smartFeatures: {\n      autoTranslate: true,\n      voiceActivated: true,\n      contextAware: true,\n      realTimeUpdates: true,\n      aiRecommendations: true\n    },\n    \n    // Analytics & Insights\n    cardAnalytics: {\n      totalViews: 15420,\n      uniqueConnections: 892,\n      engagementRate: 78,\n      topInteractions: ['schedule_meeting', 'view_portfolio', 'connect_linkedin']\n    },\n    \n    // Verification & Trust\n    verification: {\n      verified: true,\n      verificationLevel: 'Enterprise',\n      badges: ['AI Expert', 'Thought Leader', 'Verified CEO', 'Speaker'],\n      trustScore: 98\n    }\n  },\n  \n  {\n    id: 'creative-director-002',\n    name: 'Marcus Rodriguez',\n    title: 'Creative Director & Digital Artist',\n    company: 'Neon Studios',\n    industry: 'Digital Design',\n    location: 'New York, NY',\n    \n    email: '<EMAIL>',\n    phone: '+****************',\n    website: 'neonstudios.com',\n    instagram: '@marcusrodriguez_art',\n    behance: 'behance.net/marcusrodriguez',\n    \n    avatar: '🎨',\n    profileImage: '/avatars/marcus-rodriguez.jpg',\n    companyLogo: '/logos/neon-studios.svg',\n    \n    aiPersonality: {\n      voiceIntro: 'Hey there! I\\'m Marcus, and I create digital experiences that blur the line between reality and imagination.',\n      personalityTraits: ['Creative', 'Bold', 'Experimental', 'Collaborative'],\n      communicationStyle: 'Energetic and visual',\n      interests: ['Digital Art', 'AR/VR Design', 'Generative AI', 'Interactive Media']\n    },\n    \n    recentAchievements: [\n      'Won Cannes Lions Gold for AR Campaign',\n      'Featured in Adobe\\'s Creative Spotlight',\n      'Collaborated with major brands on metaverse experiences',\n      'Exhibited at Digital Art Museum NYC'\n    ],\n    \n    currentProjects: [\n      'AI-Generated Art Installation',\n      'Immersive Brand Experience for Fashion Week',\n      'NFT Collection with Environmental Theme'\n    ],\n    \n    availableActions: [\n      { type: 'view_portfolio', label: 'View Creative Portfolio', icon: '🎨' },\n      { type: 'commission_work', label: 'Commission Artwork', icon: '💼' },\n      { type: 'collaborate', label: 'Discuss Collaboration', icon: '🤝' },\n      { type: 'follow_instagram', label: 'Follow on Instagram', icon: '📸' },\n      { type: 'book_consultation', label: 'Book Design Consultation', icon: '💡' }\n    ],\n    \n    cardTheme: {\n      primary: '#ff0080',\n      secondary: '#8000ff',\n      accent: '#00ffff',\n      background: 'neon-gradient',\n      effects: ['color-shift', 'paint-splash', 'digital-glitch'],\n      animation: 'creative-pulse'\n    },\n    \n    smartFeatures: {\n      autoTranslate: true,\n      voiceActivated: false,\n      contextAware: true,\n      realTimeUpdates: true,\n      aiRecommendations: true\n    },\n    \n    cardAnalytics: {\n      totalViews: 8750,\n      uniqueConnections: 445,\n      engagementRate: 85,\n      topInteractions: ['view_portfolio', 'follow_instagram', 'commission_work']\n    },\n    \n    verification: {\n      verified: true,\n      verificationLevel: 'Creative Professional',\n      badges: ['Award Winner', 'Featured Artist', 'Verified Creator'],\n      trustScore: 94\n    }\n  },\n  \n  {\n    id: 'blockchain-dev-003',\n    name: 'Alex Kim',\n    title: 'Senior Blockchain Developer',\n    company: 'CryptoForge Labs',\n    industry: 'Blockchain Technology',\n    location: 'Austin, TX',\n    \n    email: '<EMAIL>',\n    phone: '+****************',\n    website: 'cryptoforge.dev',\n    github: 'github.com/alexkim-blockchain',\n    twitter: '@alexkim_crypto',\n    \n    avatar: '⛓️',\n    profileImage: '/avatars/alex-kim.jpg',\n    companyLogo: '/logos/cryptoforge.svg',\n    \n    aiPersonality: {\n      voiceIntro: 'Hello! I\\'m Alex, and I build the decentralized infrastructure that powers the future of finance.',\n      personalityTraits: ['Technical', 'Precise', 'Forward-thinking', 'Security-focused'],\n      communicationStyle: 'Technical but approachable',\n      interests: ['DeFi Protocols', 'Smart Contracts', 'Cryptography', 'Web3 Gaming']\n    },\n    \n    recentAchievements: [\n      'Deployed $100M+ TVL DeFi protocol',\n      'Discovered critical vulnerability in major DEX',\n      'Open-sourced revolutionary consensus algorithm',\n      'Speaker at Ethereum Developer Conference'\n    ],\n    \n    currentProjects: [\n      'Layer 2 Scaling Solution for NFTs',\n      'Cross-chain Bridge Protocol',\n      'Zero-Knowledge Privacy Tools'\n    ],\n    \n    availableActions: [\n      { type: 'view_github', label: 'View GitHub Profile', icon: '💻' },\n      { type: 'technical_discussion', label: 'Technical Discussion', icon: '🔧' },\n      { type: 'code_review', label: 'Request Code Review', icon: '👀' },\n      { type: 'join_project', label: 'Collaborate on Project', icon: '🚀' },\n      { type: 'follow_twitter', label: 'Follow on Twitter', icon: '🐦' }\n    ],\n    \n    cardTheme: {\n      primary: '#00ff41',\n      secondary: '#008f11',\n      accent: '#00ff88',\n      background: 'matrix-gradient',\n      effects: ['code-rain', 'blockchain-nodes', 'crypto-pulse'],\n      animation: 'tech-float'\n    },\n    \n    smartFeatures: {\n      autoTranslate: true,\n      voiceActivated: true,\n      contextAware: true,\n      realTimeUpdates: true,\n      aiRecommendations: true\n    },\n    \n    cardAnalytics: {\n      totalViews: 12300,\n      uniqueConnections: 678,\n      engagementRate: 72,\n      topInteractions: ['view_github', 'technical_discussion', 'follow_twitter']\n    },\n    \n    verification: {\n      verified: true,\n      verificationLevel: 'Technical Expert',\n      badges: ['Blockchain Expert', 'Security Researcher', 'Open Source Contributor'],\n      trustScore: 96\n    }\n  }\n];\n\n// AI-powered card recommendation engine\nexport const getRecommendedCards = (userInterests = [], industry = '') => {\n  return sampleCards.filter(card => {\n    const cardInterests = card.aiPersonality.interests.map(i => i.toLowerCase());\n    const matchingInterests = userInterests.filter(interest => \n      cardInterests.some(cardInterest => \n        cardInterest.includes(interest.toLowerCase()) || \n        interest.toLowerCase().includes(cardInterest)\n      )\n    );\n    \n    const industryMatch = industry && card.industry.toLowerCase().includes(industry.toLowerCase());\n    \n    return matchingInterests.length > 0 || industryMatch;\n  });\n};\n\n// Dynamic card content generator\nexport const generateDynamicContent = (cardId, context = {}) => {\n  const card = sampleCards.find(c => c.id === cardId);\n  if (!card) return null;\n  \n  const { timeOfDay, userLocation, userIndustry } = context;\n  \n  // Generate contextual greeting\n  let greeting = card.aiPersonality.voiceIntro;\n  if (timeOfDay === 'morning') {\n    greeting = `Good morning! ${greeting}`;\n  } else if (timeOfDay === 'evening') {\n    greeting = `Good evening! ${greeting}`;\n  }\n  \n  // Add location-based content\n  if (userLocation && userLocation !== card.location) {\n    greeting += ` I see you're connecting from ${userLocation} - I'd love to learn more about your local tech scene!`;\n  }\n  \n  // Industry-specific recommendations\n  const relevantProjects = card.currentProjects.filter(project => \n    userIndustry && project.toLowerCase().includes(userIndustry.toLowerCase())\n  );\n  \n  return {\n    ...card,\n    dynamicGreeting: greeting,\n    relevantProjects,\n    contextualActions: card.availableActions.slice(0, 3) // Show top 3 most relevant actions\n  };\n};\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;AAElC,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QAEV,sBAAsB;QACtB,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,SAAS;QAET,kBAAkB;QAClB,QAAQ;QACR,cAAc;QACd,aAAa;QAEb,uBAAuB;QACvB,eAAe;YACb,YAAY;YACZ,mBAAmB;gBAAC;gBAAc;gBAAc;gBAAa;aAAgB;YAC7E,oBAAoB;YACpB,WAAW;gBAAC;gBAAoB;gBAAqB;gBAAoB;aAAY;QACvF;QAEA,kBAAkB;QAClB,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QAED,iBAAiB;YACf;YACA;YACA;SACD;QAED,uBAAuB;QACvB,kBAAkB;YAChB;gBAAE,MAAM;gBAAoB,OAAO;gBAAsB,MAAM;YAAK;YACpE;gBAAE,MAAM;gBAAkB,OAAO;gBAA2B,MAAM;YAAK;YACvE;gBAAE,MAAM;gBAAoB,OAAO;gBAAuB,MAAM;YAAK;YACrE;gBAAE,MAAM;gBAAuB,OAAO;gBAA8B,MAAM;YAAK;YAC/E;gBAAE,MAAM;gBAAmB,OAAO;gBAAsB,MAAM;YAAK;SACpE;QAED,sBAAsB;QACtB,WAAW;YACT,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAC;gBAAkB;gBAAiB;aAAkB;YAC/D,WAAW;QACb;QAEA,iBAAiB;QACjB,eAAe;YACb,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;QACrB;QAEA,uBAAuB;QACvB,eAAe;YACb,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;gBAAC;gBAAoB;gBAAkB;aAAmB;QAC7E;QAEA,uBAAuB;QACvB,cAAc;YACZ,UAAU;YACV,mBAAmB;YACnB,QAAQ;gBAAC;gBAAa;gBAAkB;gBAAgB;aAAU;YAClE,YAAY;QACd;IACF;IAEA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QAEV,OAAO;QACP,OAAO;QACP,SAAS;QACT,WAAW;QACX,SAAS;QAET,QAAQ;QACR,cAAc;QACd,aAAa;QAEb,eAAe;YACb,YAAY;YACZ,mBAAmB;gBAAC;gBAAY;gBAAQ;gBAAgB;aAAgB;YACxE,oBAAoB;YACpB,WAAW;gBAAC;gBAAe;gBAAgB;gBAAiB;aAAoB;QAClF;QAEA,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QAED,iBAAiB;YACf;YACA;YACA;SACD;QAED,kBAAkB;YAChB;gBAAE,MAAM;gBAAkB,OAAO;gBAA2B,MAAM;YAAK;YACvE;gBAAE,MAAM;gBAAmB,OAAO;gBAAsB,MAAM;YAAK;YACnE;gBAAE,MAAM;gBAAe,OAAO;gBAAyB,MAAM;YAAK;YAClE;gBAAE,MAAM;gBAAoB,OAAO;gBAAuB,MAAM;YAAK;YACrE;gBAAE,MAAM;gBAAqB,OAAO;gBAA4B,MAAM;YAAK;SAC5E;QAED,WAAW;YACT,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAC;gBAAe;gBAAgB;aAAiB;YAC1D,WAAW;QACb;QAEA,eAAe;YACb,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;QACrB;QAEA,eAAe;YACb,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;gBAAC;gBAAkB;gBAAoB;aAAkB;QAC5E;QAEA,cAAc;YACZ,UAAU;YACV,mBAAmB;YACnB,QAAQ;gBAAC;gBAAgB;gBAAmB;aAAmB;YAC/D,YAAY;QACd;IACF;IAEA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QAEV,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QAET,QAAQ;QACR,cAAc;QACd,aAAa;QAEb,eAAe;YACb,YAAY;YACZ,mBAAmB;gBAAC;gBAAa;gBAAW;gBAAoB;aAAmB;YACnF,oBAAoB;YACpB,WAAW;gBAAC;gBAAkB;gBAAmB;gBAAgB;aAAc;QACjF;QAEA,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QAED,iBAAiB;YACf;YACA;YACA;SACD;QAED,kBAAkB;YAChB;gBAAE,MAAM;gBAAe,OAAO;gBAAuB,MAAM;YAAK;YAChE;gBAAE,MAAM;gBAAwB,OAAO;gBAAwB,MAAM;YAAK;YAC1E;gBAAE,MAAM;gBAAe,OAAO;gBAAuB,MAAM;YAAK;YAChE;gBAAE,MAAM;gBAAgB,OAAO;gBAA0B,MAAM;YAAK;YACpE;gBAAE,MAAM;gBAAkB,OAAO;gBAAqB,MAAM;YAAK;SAClE;QAED,WAAW;YACT,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAC;gBAAa;gBAAoB;aAAe;YAC1D,WAAW;QACb;QAEA,eAAe;YACb,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;QACrB;QAEA,eAAe;YACb,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;gBAAC;gBAAe;gBAAwB;aAAiB;QAC5E;QAEA,cAAc;YACZ,UAAU;YACV,mBAAmB;YACnB,QAAQ;gBAAC;gBAAqB;gBAAuB;aAA0B;YAC/E,YAAY;QACd;IACF;CACD;AAGM,MAAM,sBAAsB,CAAC,gBAAgB,EAAE,EAAE,WAAW,EAAE;IACnE,OAAO,YAAY,MAAM,CAAC,CAAA;QACxB,MAAM,gBAAgB,KAAK,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;QACzE,MAAM,oBAAoB,cAAc,MAAM,CAAC,CAAA,WAC7C,cAAc,IAAI,CAAC,CAAA,eACjB,aAAa,QAAQ,CAAC,SAAS,WAAW,OAC1C,SAAS,WAAW,GAAG,QAAQ,CAAC;QAIpC,MAAM,gBAAgB,YAAY,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;QAE3F,OAAO,kBAAkB,MAAM,GAAG,KAAK;IACzC;AACF;AAGO,MAAM,yBAAyB,CAAC,QAAQ,UAAU,CAAC,CAAC;IACzD,MAAM,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC5C,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;IAElD,+BAA+B;IAC/B,IAAI,WAAW,KAAK,aAAa,CAAC,UAAU;IAC5C,IAAI,cAAc,WAAW;QAC3B,WAAW,CAAC,cAAc,EAAE,UAAU;IACxC,OAAO,IAAI,cAAc,WAAW;QAClC,WAAW,CAAC,cAAc,EAAE,UAAU;IACxC;IAEA,6BAA6B;IAC7B,IAAI,gBAAgB,iBAAiB,KAAK,QAAQ,EAAE;QAClD,YAAY,CAAC,8BAA8B,EAAE,aAAa,sDAAsD,CAAC;IACnH;IAEA,oCAAoC;IACpC,MAAM,mBAAmB,KAAK,eAAe,CAAC,MAAM,CAAC,CAAA,UACnD,gBAAgB,QAAQ,WAAW,GAAG,QAAQ,CAAC,aAAa,WAAW;IAGzE,OAAO;QACL,GAAG,IAAI;QACP,iBAAiB;QACjB;QACA,mBAAmB,KAAK,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,mCAAmC;IAC1F;AACF", "debugId": null}}, {"offset": {"line": 2304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/app/demo/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { generateQRCode } from '@/lib/utils';\nimport Card3D from '@/components/3d/Card3D';\nimport ARSimulation from '@/components/3d/ARSimulation';\nimport VoiceInteraction from '@/components/ai/VoiceInteraction';\nimport { sampleCards, generateDynamicContent } from '@/data/sampleCards';\n\nexport default function DemoPage() {\n  const [activeDemo, setActiveDemo] = useState('ai-showcase');\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [isScanning, setIsScanning] = useState(false);\n  const [scannedCard, setScannedCard] = useState(null);\n  const [selectedCard, setSelectedCard] = useState(sampleCards[0]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [cameraActive, setCameraActive] = useState(false);\n  const [voiceActive, setVoiceActive] = useState(false);\n  const videoRef = useRef(null);\n\n  // Generate QR code on component mount\n  useEffect(() => {\n    const generateQR = async () => {\n      const cardUrl = `https://namecardai.com/card/${selectedCard.id}`;\n      const qrUrl = await generateQRCode(cardUrl);\n      if (qrUrl) {\n        setQrCodeUrl(qrUrl);\n      }\n    };\n    generateQR();\n  }, [selectedCard.id]);\n\n  const demos = [\n    {\n      id: 'ai-showcase',\n      title: 'AI-Powered Cards',\n      description: 'Experience next-level AI business cards with voice interaction',\n      icon: '🤖',\n      featured: true\n    },\n    {\n      id: '3d-hologram',\n      title: '3D Holographic Cards',\n      description: 'Immersive 3D business cards with real-time animations',\n      icon: '🎯',\n      featured: true\n    },\n    {\n      id: 'ar-simulation',\n      title: 'AR Face Recognition',\n      description: 'Advanced AR overlay with face detection and card matching',\n      icon: '👁️',\n      featured: true\n    },\n    {\n      id: 'smart-search',\n      title: 'AI Smart Search',\n      description: 'Intelligent card discovery with context-aware recommendations',\n      icon: '🔍'\n    },\n    {\n      id: 'qr-scan',\n      title: 'QR Code Scan',\n      description: 'Instant card access via QR code with 3D reveal animation',\n      icon: '📱'\n    },\n    {\n      id: 'voice-assistant',\n      title: 'Voice Assistant',\n      description: 'Talk to AI personas and get personalized responses',\n      icon: '🎤'\n    },\n    {\n      id: 'card-customizer',\n      title: 'Live Customizer',\n      description: 'Real-time card editing with instant 3D preview',\n      icon: '🎨'\n    }\n  ];\n\n  // Enhanced demo handlers\n  const handleScanSimulation = () => {\n    setIsScanning(true);\n    setTimeout(() => {\n      setIsScanning(false);\n      setScannedCard(selectedCard);\n    }, 2000);\n  };\n\n  const handleCardSelection = (card) => {\n    setSelectedCard(card);\n    setScannedCard(null);\n  };\n\n  const handleSmartSearch = async (query) => {\n    setIsSearching(true);\n    setSearchQuery(query);\n\n    // Simulate AI-powered search\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    const results = sampleCards.filter(card =>\n      card.name.toLowerCase().includes(query.toLowerCase()) ||\n      card.company.toLowerCase().includes(query.toLowerCase()) ||\n      card.title.toLowerCase().includes(query.toLowerCase()) ||\n      card.industry.toLowerCase().includes(query.toLowerCase()) ||\n      card.aiPersonality.interests.some(interest =>\n        interest.toLowerCase().includes(query.toLowerCase())\n      )\n    );\n\n    setSearchResults(results);\n    setIsSearching(false);\n  };\n\n  const handleVoiceInteraction = (data) => {\n    console.log('Voice interaction:', data);\n    // Handle voice interaction data\n  };\n\n  const handleARCardDetected = (card) => {\n    setScannedCard(card);\n    console.log('AR card detected:', card);\n  };\n\n  const Card3DPreview = ({ card }) => (\n    <div className=\"relative w-full max-w-md mx-auto\">\n      <div className=\"aspect-[1.6/1] bg-gradient-to-br from-neon-blue via-electric-purple to-hologram-pink rounded-2xl shadow-2xl shadow-neon-blue/30 transform hover:scale-105 hover:rotate-3 transition-all duration-500 group\">\n        <div className=\"absolute inset-0 bg-white/10 rounded-2xl backdrop-blur-sm p-6 flex flex-col justify-between\">\n          {/* Header */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"text-4xl\">{card.avatar}</div>\n            <div>\n              <h3 className=\"text-xl font-bold text-white\">{card.name}</h3>\n              <p className=\"text-white/80 text-sm\">{card.title}</p>\n            </div>\n          </div>\n          \n          {/* Company */}\n          <div className=\"text-center\">\n            <p className=\"text-white/90 font-semibold\">{card.company}</p>\n            <p className=\"text-white/70 text-sm italic\">{card.intro}</p>\n          </div>\n          \n          {/* Contact Info */}\n          <div className=\"space-y-1\">\n            <p className=\"text-white/80 text-xs\">{card.email}</p>\n            <p className=\"text-white/80 text-xs\">{card.phone}</p>\n          </div>\n        </div>\n        \n        {/* Floating particles */}\n        <div className=\"absolute -top-2 -right-2 w-3 h-3 bg-neon-blue rounded-full animate-ping\"></div>\n        <div className=\"absolute -bottom-2 -left-2 w-2 h-2 bg-electric-purple rounded-full animate-pulse\"></div>\n        <div className=\"absolute top-1/2 -right-1 w-1.5 h-1.5 bg-hologram-pink rounded-full animate-bounce\"></div>\n      </div>\n    </div>\n  );\n\n  const QRScanDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-2xl font-bold text-text-primary mb-4\">QR Code Scan Demo</h3>\n        <p className=\"text-text-secondary\">Scan the QR code below to see the magic happen</p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\n        {/* QR Code */}\n        <div className=\"text-center\">\n          <div className=\"bg-white p-6 rounded-2xl inline-block mb-4\">\n            {qrCodeUrl ? (\n              <img src={qrCodeUrl} alt=\"Demo QR Code\" className=\"w-48 h-48\" />\n            ) : (\n              <div className=\"w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center\">\n                <div className=\"animate-spin w-8 h-8 border-2 border-neon-blue border-t-transparent rounded-full\"></div>\n              </div>\n            )}\n          </div>\n          \n          <Button \n            onClick={handleScanSimulation}\n            disabled={isScanning}\n            variant=\"primary\"\n            size=\"lg\"\n          >\n            {isScanning ? 'Scanning...' : 'Simulate Scan'}\n          </Button>\n        </div>\n        \n        {/* Result */}\n        <div>\n          {isScanning ? (\n            <div className=\"text-center\">\n              <div className=\"animate-pulse text-6xl mb-4\">📱</div>\n              <p className=\"text-text-secondary\">Scanning QR code...</p>\n            </div>\n          ) : scannedCard ? (\n            <div className=\"space-y-4\">\n              <h4 className=\"text-lg font-semibold text-text-primary\">Card Found!</h4>\n              <Card3DPreview card={scannedCard} />\n              <div className=\"text-center\">\n                <Button variant=\"outline\" size=\"sm\">\n                  Save to Contacts\n                </Button>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center text-text-secondary\">\n              <div className=\"text-6xl mb-4\">👆</div>\n              <p>Scan the QR code to see the result</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  const CameraDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-2xl font-bold text-text-primary mb-4\">Camera Recognition Demo</h3>\n        <p className=\"text-text-secondary\">Point your camera to find digital business cards</p>\n      </div>\n      \n      <div className=\"bg-dark-space/50 rounded-2xl p-6 border border-electric-purple/20\">\n        <div className=\"aspect-video bg-gradient-to-br from-midnight-blue to-deep-purple rounded-lg flex items-center justify-center relative overflow-hidden\">\n          <div className=\"text-center\">\n            <div className=\"text-4xl mb-4\">📷</div>\n            <p className=\"text-text-secondary\">Camera feed simulation</p>\n            <p className=\"text-sm text-text-secondary mt-2\">In real app: Live camera with face detection</p>\n          </div>\n          \n          {/* Scanning overlay */}\n          <div className=\"absolute inset-4 border-2 border-neon-blue/50 rounded-lg\">\n            <div className=\"absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-neon-blue\"></div>\n            <div className=\"absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-neon-blue\"></div>\n            <div className=\"absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-neon-blue\"></div>\n            <div className=\"absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-neon-blue\"></div>\n          </div>\n        </div>\n        \n        <div className=\"mt-4 text-center\">\n          <Button variant=\"primary\">\n            Start Camera Recognition\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const Card3DDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-2xl font-bold text-text-primary mb-4\">3D Card Renderer</h3>\n        <p className=\"text-text-secondary\">Interactive 3D business cards with real-time animations</p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        <div>\n          <Card3DPreview card={sampleCard} />\n        </div>\n        \n        <div className=\"space-y-4\">\n          <h4 className=\"text-lg font-semibold text-text-primary\">Customization Options</h4>\n          \n          <div className=\"space-y-3\">\n            <div>\n              <label className=\"block text-sm text-text-secondary mb-2\">Theme</label>\n              <div className=\"grid grid-cols-3 gap-2\">\n                {['cyber-blue', 'neon-purple', 'matrix-green'].map((theme) => (\n                  <button\n                    key={theme}\n                    className=\"p-2 rounded-lg border border-electric-purple/30 hover:border-neon-blue/50 transition-colors\"\n                  >\n                    <div className={`w-full h-8 rounded bg-gradient-to-r ${\n                      theme === 'cyber-blue' ? 'from-neon-blue to-electric-purple' :\n                      theme === 'neon-purple' ? 'from-electric-purple to-hologram-pink' :\n                      'from-cyber-green to-matrix-green'\n                    }`}></div>\n                  </button>\n                ))}\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm text-text-secondary mb-2\">Effects</label>\n              <div className=\"space-y-2\">\n                {['Matrix Rain', 'Particle Glow', 'Hologram Flicker'].map((effect) => (\n                  <label key={effect} className=\"flex items-center space-x-2\">\n                    <input type=\"checkbox\" className=\"rounded\" />\n                    <span className=\"text-sm text-text-secondary\">{effect}</span>\n                  </label>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  // Advanced Demo Components\n  const AIShowcaseDemo = () => (\n    <div className=\"space-y-8\">\n      <div className=\"text-center\">\n        <h3 className=\"text-3xl font-bold text-text-primary mb-4\">\n          AI-Powered Business Cards\n        </h3>\n        <p className=\"text-text-secondary max-w-2xl mx-auto\">\n          Experience the future of networking with AI-enhanced business cards featuring voice interaction,\n          dynamic content, and intelligent recommendations.\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Card Selection */}\n        <div className=\"space-y-4\">\n          <h4 className=\"text-lg font-semibold text-text-primary\">Select a Card</h4>\n          {sampleCards.map((card, index) => (\n            <button\n              key={card.id}\n              onClick={() => handleCardSelection(card)}\n              className={`w-full p-4 rounded-lg border transition-all duration-300 text-left ${\n                selectedCard.id === card.id\n                  ? 'border-neon-blue bg-neon-blue/10'\n                  : 'border-electric-purple/30 bg-surface-dark/50 hover:border-electric-purple/60'\n              }`}\n            >\n              <div className=\"flex items-center gap-3\">\n                <div className=\"text-2xl\">{card.avatar}</div>\n                <div>\n                  <div className=\"font-semibold text-text-primary\">{card.name}</div>\n                  <div className=\"text-sm text-text-secondary\">{card.title}</div>\n                  <div className=\"text-xs text-electric-purple\">{card.company}</div>\n                </div>\n              </div>\n            </button>\n          ))}\n        </div>\n\n        {/* 3D Card Display */}\n        <div className=\"space-y-4\">\n          <h4 className=\"text-lg font-semibold text-text-primary\">3D Holographic Card</h4>\n          <Card3D\n            cardData={selectedCard}\n            theme={selectedCard.cardTheme?.background || 'cyber'}\n            className=\"h-80\"\n          />\n\n          {/* Card Stats */}\n          <div className=\"grid grid-cols-2 gap-4 text-center\">\n            <div className=\"p-3 bg-surface-dark/50 rounded-lg\">\n              <div className=\"text-lg font-bold text-neon-blue\">\n                {selectedCard.cardAnalytics?.totalViews.toLocaleString()}\n              </div>\n              <div className=\"text-xs text-text-secondary\">Total Views</div>\n            </div>\n            <div className=\"p-3 bg-surface-dark/50 rounded-lg\">\n              <div className=\"text-lg font-bold text-cyber-green\">\n                {selectedCard.cardAnalytics?.engagementRate}%\n              </div>\n              <div className=\"text-xs text-text-secondary\">Engagement</div>\n            </div>\n          </div>\n        </div>\n\n        {/* AI Features */}\n        <div className=\"space-y-4\">\n          <h4 className=\"text-lg font-semibold text-text-primary\">AI Features</h4>\n\n          {/* Voice Interaction */}\n          <VoiceInteraction\n            cardData={selectedCard}\n            isActive={voiceActive}\n            onInteraction={handleVoiceInteraction}\n          />\n        </div>\n      </div>\n    </div>\n  );\n\n  const HologramDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-3xl font-bold text-text-primary mb-4\">\n          3D Holographic Business Cards\n        </h3>\n        <p className=\"text-text-secondary\">\n          Immersive 3D business cards with real-time animations and interactive elements\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        <div>\n          <Card3D\n            cardData={selectedCard}\n            theme={selectedCard.cardTheme?.background || 'cyber'}\n            className=\"h-96\"\n          />\n        </div>\n\n        <div className=\"space-y-6\">\n          <div>\n            <h4 className=\"text-lg font-semibold text-text-primary mb-4\">Customization</h4>\n\n            {/* Theme Selection */}\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm text-text-secondary\">Theme</label>\n              <div className=\"grid grid-cols-3 gap-2\">\n                {['cyber', 'matrix', 'neon'].map((theme) => (\n                  <button\n                    key={theme}\n                    onClick={() => setSelectedCard({...selectedCard, cardTheme: {...selectedCard.cardTheme, background: theme}})}\n                    className={`p-3 rounded-lg border transition-colors ${\n                      selectedCard.cardTheme?.background === theme\n                        ? 'border-neon-blue bg-neon-blue/10'\n                        : 'border-electric-purple/30 hover:border-neon-blue/50'\n                    }`}\n                  >\n                    <div className={`w-full h-8 rounded mb-2 ${\n                      theme === 'cyber' ? 'bg-gradient-to-r from-neon-blue to-electric-purple' :\n                      theme === 'matrix' ? 'bg-gradient-to-r from-cyber-green to-matrix-green' :\n                      'bg-gradient-to-r from-hologram-pink to-quantum-gold'\n                    }`}></div>\n                    <div className=\"text-xs text-text-secondary capitalize\">{theme}</div>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* AI Personality Traits */}\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm text-text-secondary\">AI Personality</label>\n              <div className=\"flex flex-wrap gap-2\">\n                {selectedCard.aiPersonality?.personalityTraits.map((trait, index) => (\n                  <span\n                    key={index}\n                    className=\"px-3 py-1 bg-electric-purple/20 text-electric-purple rounded-full text-xs\"\n                  >\n                    {trait}\n                  </span>\n                ))}\n              </div>\n            </div>\n\n            {/* Recent Achievements */}\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm text-text-secondary\">Recent Achievements</label>\n              <div className=\"space-y-2 max-h-32 overflow-y-auto\">\n                {selectedCard.recentAchievements?.map((achievement, index) => (\n                  <div key={index} className=\"text-xs text-text-secondary p-2 bg-surface-dark/50 rounded\">\n                    • {achievement}\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const ARSimulationDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-3xl font-bold text-text-primary mb-4\">\n          AR Face Recognition & Card Overlay\n        </h3>\n        <p className=\"text-text-secondary\">\n          Advanced augmented reality with real-time face detection and intelligent card matching\n        </p>\n      </div>\n\n      <ARSimulation\n        cardData={selectedCard}\n        isActive={cameraActive}\n        onCardDetected={handleARCardDetected}\n      />\n\n      {scannedCard && (\n        <div className=\"mt-6 p-4 bg-surface-dark/50 rounded-lg border border-cyber-green/30\">\n          <h4 className=\"text-lg font-semibold text-cyber-green mb-2\">Card Detected!</h4>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <p className=\"text-text-secondary text-sm mb-2\">Detected Person:</p>\n              <div className=\"flex items-center gap-3\">\n                <div className=\"text-2xl\">{scannedCard.avatar}</div>\n                <div>\n                  <div className=\"font-semibold text-text-primary\">{scannedCard.name}</div>\n                  <div className=\"text-sm text-text-secondary\">{scannedCard.title}</div>\n                </div>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <Button variant=\"primary\" size=\"sm\" className=\"w-full\">\n                Connect on LinkedIn\n              </Button>\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                Save to Contacts\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const SmartSearchDemo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h3 className=\"text-3xl font-bold text-text-primary mb-4\">\n          AI-Powered Smart Search\n        </h3>\n        <p className=\"text-text-secondary\">\n          Intelligent card discovery with context-aware recommendations and semantic search\n        </p>\n      </div>\n\n      <div className=\"max-w-2xl mx-auto\">\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            placeholder=\"Search by name, company, skills, or interests...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            onKeyPress={(e) => e.key === 'Enter' && handleSmartSearch(searchQuery)}\n            className=\"w-full p-4 bg-surface-dark/50 border border-electric-purple/30 rounded-lg text-text-primary placeholder-text-secondary focus:border-neon-blue focus:outline-none\"\n          />\n          <Button\n            onClick={() => handleSmartSearch(searchQuery)}\n            disabled={isSearching}\n            className=\"absolute right-2 top-2\"\n            size=\"sm\"\n          >\n            {isSearching ? '🔄' : '🔍'}\n          </Button>\n        </div>\n\n        {/* Quick Search Suggestions */}\n        <div className=\"flex flex-wrap gap-2 mt-4\">\n          {['AI', 'Blockchain', 'Design', 'CEO', 'Developer'].map((suggestion) => (\n            <button\n              key={suggestion}\n              onClick={() => handleSmartSearch(suggestion)}\n              className=\"px-3 py-1 bg-electric-purple/20 text-electric-purple rounded-full text-sm hover:bg-electric-purple/30 transition-colors\"\n            >\n              {suggestion}\n            </button>\n          ))}\n        </div>\n\n        {/* Search Results */}\n        {searchResults.length > 0 && (\n          <div className=\"mt-6 space-y-4\">\n            <h4 className=\"text-lg font-semibold text-text-primary\">\n              Search Results ({searchResults.length})\n            </h4>\n            {searchResults.map((card) => (\n              <div\n                key={card.id}\n                className=\"p-4 bg-surface-dark/50 border border-electric-purple/30 rounded-lg hover:border-neon-blue/50 transition-colors cursor-pointer\"\n                onClick={() => handleCardSelection(card)}\n              >\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"text-3xl\">{card.avatar}</div>\n                  <div className=\"flex-1\">\n                    <div className=\"font-semibold text-text-primary\">{card.name}</div>\n                    <div className=\"text-sm text-text-secondary\">{card.title} at {card.company}</div>\n                    <div className=\"text-xs text-electric-purple mt-1\">\n                      {card.aiPersonality.interests.slice(0, 3).join(', ')}\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm text-cyber-green\">\n                      {card.verification.trustScore}% Trust Score\n                    </div>\n                    <div className=\"text-xs text-text-secondary\">\n                      {card.cardAnalytics.totalViews.toLocaleString()} views\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  const renderDemo = () => {\n    switch (activeDemo) {\n      case 'ai-showcase':\n        return <AIShowcaseDemo />;\n      case '3d-hologram':\n        return <HologramDemo />;\n      case 'ar-simulation':\n        return <ARSimulationDemo />;\n      case 'smart-search':\n        return <SmartSearchDemo />;\n      case 'qr-scan':\n        return <QRScanDemo />;\n      case 'voice-assistant':\n        return (\n          <div className=\"max-w-2xl mx-auto\">\n            <VoiceInteraction\n              cardData={selectedCard}\n              isActive={true}\n              onInteraction={handleVoiceInteraction}\n            />\n          </div>\n        );\n      case 'card-customizer':\n        return <HologramDemo />; // Reuse hologram demo for customization\n      default:\n        return <AIShowcaseDemo />;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-dark-space via-deep-purple to-midnight-blue\">\n      {/* Hero Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl md:text-6xl font-bold font-display mb-6\">\n              <span className=\"bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent\">\n                Live Demo\n              </span>\n            </h1>\n            <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n              Experience the future of networking with our interactive demos. See how NameCardAI transforms traditional business cards into immersive AR experiences.\n            </p>\n          </div>\n\n          {/* Featured Demos */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-2xl font-bold text-text-primary mb-4 text-center\">\n              🌟 Featured AI Demos\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n              {demos.filter(demo => demo.featured).map((demo) => (\n                <button\n                  key={demo.id}\n                  onClick={() => setActiveDemo(demo.id)}\n                  className={`p-6 rounded-2xl border-2 transition-all duration-300 text-left ${\n                    activeDemo === demo.id\n                      ? 'border-neon-blue bg-neon-blue/10 scale-105 shadow-2xl shadow-neon-blue/20'\n                      : 'border-electric-purple/30 bg-surface-dark/50 hover:border-neon-blue/50 hover:bg-surface-light/50 hover:scale-102'\n                  }`}\n                >\n                  <div className=\"text-4xl mb-4\">{demo.icon}</div>\n                  <h3 className=\"font-bold text-text-primary text-lg mb-2\">{demo.title}</h3>\n                  <p className=\"text-sm text-text-secondary leading-relaxed\">{demo.description}</p>\n                  {activeDemo === demo.id && (\n                    <div className=\"mt-4 flex items-center gap-2 text-neon-blue text-sm\">\n                      <div className=\"w-2 h-2 bg-neon-blue rounded-full animate-pulse\"></div>\n                      Currently Active\n                    </div>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* All Demos Navigation */}\n          <div className=\"mb-12\">\n            <h3 className=\"text-lg font-semibold text-text-primary mb-4 text-center\">\n              All Demo Features\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3\">\n              {demos.map((demo) => (\n                <button\n                  key={demo.id}\n                  onClick={() => setActiveDemo(demo.id)}\n                  className={`p-3 rounded-lg border transition-all duration-300 ${\n                    activeDemo === demo.id\n                      ? 'border-neon-blue bg-neon-blue/10 scale-105'\n                      : 'border-electric-purple/30 bg-surface-dark/50 hover:border-electric-purple/60 hover:bg-surface-light/50'\n                  } ${demo.featured ? 'ring-2 ring-quantum-gold/30' : ''}`}\n                >\n                  <div className=\"text-2xl mb-1\">{demo.icon}</div>\n                  <h4 className=\"font-semibold text-text-primary text-xs mb-1\">{demo.title}</h4>\n                  <p className=\"text-xs text-text-secondary line-clamp-2\">{demo.description}</p>\n                  {demo.featured && (\n                    <div className=\"text-xs text-quantum-gold mt-1\">⭐ Featured</div>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Demo Content */}\n          <Card variant=\"glass\" className=\"p-8\">\n            <CardContent>\n              {renderDemo()}\n            </CardContent>\n          </Card>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,MAAM,UAAU,CAAC,4BAA4B,EAAE,aAAa,EAAE,EAAE;YAChE,MAAM,QAAQ,MAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;YACnC,IAAI,OAAO;gBACT,aAAa;YACf;QACF;QACA;IACF,GAAG;QAAC,aAAa,EAAE;KAAC;IAEpB,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,cAAc;QACd,WAAW;YACT,cAAc;YACd,eAAe;QACjB,GAAG;IACL;IAEA,MAAM,sBAAsB,CAAC;QAC3B,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,eAAe;QACf,eAAe;QAEf,6BAA6B;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,UAAU,0HAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,OACjC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OAClD,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACrD,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACnD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACtD,KAAK,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,WAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;QAIrD,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,yBAAyB,CAAC;QAC9B,QAAQ,GAAG,CAAC,sBAAsB;IAClC,gCAAgC;IAClC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;QACf,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,iBAC7B,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY,KAAK,MAAM;;;;;;kDACtC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgC,KAAK,IAAI;;;;;;0DACvD,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;0CAKpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA+B,KAAK,OAAO;;;;;;kDACxD,8OAAC;wCAAE,WAAU;kDAAgC,KAAK,KAAK;;;;;;;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAyB,KAAK,KAAK;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;kCAKpD,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAKrB,MAAM,aAAa,kBACjB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAGrC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,0BACC,8OAAC;wCAAI,KAAK;wCAAW,KAAI;wCAAe,WAAU;;;;;6DAElD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;8CAKrB,8OAAC,iIAAA,CAAA,UAAM;oCACL,SAAS;oCACT,UAAU;oCACV,SAAQ;oCACR,MAAK;8CAEJ,aAAa,gBAAgB;;;;;;;;;;;;sCAKlC,8OAAC;sCACE,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;uCAEnC,4BACF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAc,MAAM;;;;;;kDACrB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;qDAMxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQf,MAAM,aAAa,kBACjB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAGrC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAInB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,UAAM;gCAAC,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;;;;;;IAQlC,MAAM,aAAa,kBACjB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAGrC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCACC,cAAA,8OAAC;gCAAc,MAAM;;;;;;;;;;;sCAGvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAExD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyC;;;;;;8DAC1D,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAc;wDAAe;qDAAe,CAAC,GAAG,CAAC,CAAC,sBAClD,8OAAC;4DAEC,WAAU;sEAEV,cAAA,8OAAC;gEAAI,WAAW,CAAC,oCAAoC,EACnD,UAAU,eAAe,sCACzB,UAAU,gBAAgB,0CAC1B,oCACA;;;;;;2DAPG;;;;;;;;;;;;;;;;sDAab,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyC;;;;;;8DAC1D,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAe;wDAAiB;qDAAmB,CAAC,GAAG,CAAC,CAAC,uBACzD,8OAAC;4DAAmB,WAAU;;8EAC5B,8OAAC;oEAAM,MAAK;oEAAW,WAAU;;;;;;8EACjC,8OAAC;oEAAK,WAAU;8EAA+B;;;;;;;2DAFrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAa5B,2BAA2B;IAC3B,MAAM,iBAAiB,kBACrB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAMvD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;gCACvD,0HAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;wCAEC,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,mEAAmE,EAC7E,aAAa,EAAE,KAAK,KAAK,EAAE,GACvB,qCACA,gFACJ;kDAEF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAY,KAAK,MAAM;;;;;;8DACtC,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAmC,KAAK,IAAI;;;;;;sEAC3D,8OAAC;4DAAI,WAAU;sEAA+B,KAAK,KAAK;;;;;;sEACxD,8OAAC;4DAAI,WAAU;sEAAgC,KAAK,OAAO;;;;;;;;;;;;;;;;;;uCAb1D,KAAK,EAAE;;;;;;;;;;;sCAqBlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,8OAAC,iIAAA,CAAA,UAAM;oCACL,UAAU;oCACV,OAAO,aAAa,SAAS,EAAE,cAAc;oCAC7C,WAAU;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,aAAa,aAAa,EAAE,WAAW;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;8DAA8B;;;;;;;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,aAAa,aAAa,EAAE;wDAAe;;;;;;;8DAE9C,8OAAC;oDAAI,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;sCAMnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,8OAAC,2IAAA,CAAA,UAAgB;oCACf,UAAU;oCACV,UAAU;oCACV,eAAe;;;;;;;;;;;;;;;;;;;;;;;;IAOzB,MAAM,eAAe,kBACnB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAKrC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCACC,cAAA,8OAAC,iIAAA,CAAA,UAAM;gCACL,UAAU;gCACV,OAAO,aAAa,SAAS,EAAE,cAAc;gCAC7C,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+C;;;;;;kDAG7D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAS;oDAAU;iDAAO,CAAC,GAAG,CAAC,CAAC,sBAChC,8OAAC;wDAEC,SAAS,IAAM,gBAAgB;gEAAC,GAAG,YAAY;gEAAE,WAAW;oEAAC,GAAG,aAAa,SAAS;oEAAE,YAAY;gEAAK;4DAAC;wDAC1G,WAAW,CAAC,wCAAwC,EAClD,aAAa,SAAS,EAAE,eAAe,QACnC,qCACA,uDACJ;;0EAEF,8OAAC;gEAAI,WAAW,CAAC,wBAAwB,EACvC,UAAU,UAAU,uDACpB,UAAU,WAAW,sDACrB,uDACA;;;;;;0EACF,8OAAC;gEAAI,WAAU;0EAA0C;;;;;;;uDAbpD;;;;;;;;;;;;;;;;kDAoBb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DACZ,aAAa,aAAa,EAAE,kBAAkB,IAAI,CAAC,OAAO,sBACzD,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;kDAUb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DACZ,aAAa,kBAAkB,EAAE,IAAI,CAAC,aAAa,sBAClD,8OAAC;wDAAgB,WAAU;;4DAA6D;4DACnF;;uDADK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAY1B,MAAM,mBAAmB,kBACvB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAKrC,8OAAC,uIAAA,CAAA,UAAY;oBACX,UAAU;oBACV,UAAU;oBACV,gBAAgB;;;;;;gBAGjB,6BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;sDAChD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAY,YAAY,MAAM;;;;;;8DAC7C,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAmC,YAAY,IAAI;;;;;;sEAClE,8OAAC;4DAAI,WAAU;sEAA+B,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAIrE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAS;;;;;;sDAGvD,8OAAC,iIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUnE,MAAM,kBAAkB,kBACtB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAKrC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,kBAAkB;oCAC1D,WAAU;;;;;;8CAEZ,8OAAC,iIAAA,CAAA,UAAM;oCACL,SAAS,IAAM,kBAAkB;oCACjC,UAAU;oCACV,WAAU;oCACV,MAAK;8CAEJ,cAAc,OAAO;;;;;;;;;;;;sCAK1B,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAM;gCAAc;gCAAU;gCAAO;6BAAY,CAAC,GAAG,CAAC,CAAC,2BACvD,8OAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAU;8CAET;mCAJI;;;;;;;;;;wBAUV,cAAc,MAAM,GAAG,mBACtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA0C;wCACrC,cAAc,MAAM;wCAAC;;;;;;;gCAEvC,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDAEnC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAY,KAAK,MAAM;;;;;;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAmC,KAAK,IAAI;;;;;;sEAC3D,8OAAC;4DAAI,WAAU;;gEAA+B,KAAK,KAAK;gEAAC;gEAAK,KAAK,OAAO;;;;;;;sEAC1E,8OAAC;4DAAI,WAAU;sEACZ,KAAK,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;;;;;;8DAGnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,KAAK,YAAY,CAAC,UAAU;gEAAC;;;;;;;sEAEhC,8OAAC;4DAAI,WAAU;;gEACZ,KAAK,aAAa,CAAC,UAAU,CAAC,cAAc;gEAAG;;;;;;;;;;;;;;;;;;;uCAlBjD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;IA8B1B,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,2IAAA,CAAA,UAAgB;wBACf,UAAU;wBACV,UAAU;wBACV,eAAe;;;;;;;;;;;YAIvB,KAAK;gBACH,qBAAO,8OAAC;;;;0BAAiB,wCAAwC;YACnE;gBACE,qBAAO,8OAAC;;;;;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAmF;;;;;;;;;;;0CAIrG,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAM/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAGtE,8OAAC;gCAAI,WAAU;0CACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAC,qBACxC,8OAAC;wCAEC,SAAS,IAAM,cAAc,KAAK,EAAE;wCACpC,WAAW,CAAC,+DAA+D,EACzE,eAAe,KAAK,EAAE,GAClB,8EACA,oHACJ;;0DAEF,8OAAC;gDAAI,WAAU;0DAAiB,KAAK,IAAI;;;;;;0DACzC,8OAAC;gDAAG,WAAU;0DAA4C,KAAK,KAAK;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAA+C,KAAK,WAAW;;;;;;4CAC3E,eAAe,KAAK,EAAE,kBACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;oDAAwD;;;;;;;;uCAbtE,KAAK,EAAE;;;;;;;;;;;;;;;;kCAuBpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wCAEC,SAAS,IAAM,cAAc,KAAK,EAAE;wCACpC,WAAW,CAAC,kDAAkD,EAC5D,eAAe,KAAK,EAAE,GAClB,+CACA,yGACL,CAAC,EAAE,KAAK,QAAQ,GAAG,gCAAgC,IAAI;;0DAExD,8OAAC;gDAAI,WAAU;0DAAiB,KAAK,IAAI;;;;;;0DACzC,8OAAC;gDAAG,WAAU;0DAAgD,KAAK,KAAK;;;;;;0DACxE,8OAAC;gDAAE,WAAU;0DAA4C,KAAK,WAAW;;;;;;4CACxE,KAAK,QAAQ,kBACZ,8OAAC;gDAAI,WAAU;0DAAiC;;;;;;;uCAZ7C,KAAK,EAAE;;;;;;;;;;;;;;;;kCAoBpB,8OAAC,+HAAA,CAAA,OAAI;wBAAC,SAAQ;wBAAQ,WAAU;kCAC9B,cAAA,8OAAC,+HAAA,CAAA,cAAW;sCACT;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}