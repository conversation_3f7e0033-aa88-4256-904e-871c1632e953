{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/effects/TypingText.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst TypingText = ({\n  texts = ['Your Name. Reinvented.'],\n  speed = 100,\n  deleteSpeed = 50,\n  pauseTime = 2000,\n  loop = true,\n  className = '',\n  cursorClassName = '',\n  showCursor = true,\n  onComplete = null\n}) => {\n  const [currentTextIndex, setCurrentTextIndex] = useState(0);\n  const [currentText, setCurrentText] = useState('');\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [isPaused, setIsPaused] = useState(false);\n\n  useEffect(() => {\n    if (texts.length === 0) return;\n\n    const currentFullText = texts[currentTextIndex];\n    \n    const timeout = setTimeout(() => {\n      if (isPaused) {\n        setIsPaused(false);\n        if (loop || currentTextIndex < texts.length - 1) {\n          setIsDeleting(true);\n        }\n        return;\n      }\n\n      if (isDeleting) {\n        // Deleting characters\n        if (currentText.length > 0) {\n          setCurrentText(currentText.slice(0, -1));\n        } else {\n          // Finished deleting, move to next text\n          setIsDeleting(false);\n          setCurrentTextIndex((prev) => (prev + 1) % texts.length);\n        }\n      } else {\n        // Typing characters\n        if (currentText.length < currentFullText.length) {\n          setCurrentText(currentFullText.slice(0, currentText.length + 1));\n        } else {\n          // Finished typing current text\n          if (texts.length === 1 && !loop) {\n            // Single text, no loop - we're done\n            if (onComplete) onComplete();\n            return;\n          }\n          // Pause before deleting (if looping) or moving to next\n          setIsPaused(true);\n        }\n      }\n    }, isPaused ? pauseTime : isDeleting ? deleteSpeed : speed);\n\n    return () => clearTimeout(timeout);\n  }, [\n    currentText, \n    currentTextIndex, \n    isDeleting, \n    isPaused, \n    texts, \n    speed, \n    deleteSpeed, \n    pauseTime, \n    loop, \n    onComplete\n  ]);\n\n  const Cursor = () => (\n    <span \n      className={cn(\n        'inline-block w-0.5 bg-neon-blue animate-pulse ml-1',\n        cursorClassName\n      )}\n      style={{ \n        animation: 'blink 1s infinite',\n        height: '1em'\n      }}\n    />\n  );\n\n  return (\n    <span className={cn('inline-block', className)}>\n      {currentText}\n      {showCursor && <Cursor />}\n      \n      <style jsx>{`\n        @keyframes blink {\n          0%, 50% { opacity: 1; }\n          51%, 100% { opacity: 0; }\n        }\n      `}</style>\n    </span>\n  );\n};\n\n// Preset configurations for common use cases\nexport const TypingTextPresets = {\n  hero: {\n    speed: 80,\n    deleteSpeed: 40,\n    pauseTime: 3000,\n    className: 'text-4xl md:text-6xl font-bold bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent',\n    cursorClassName: 'bg-neon-blue h-12 md:h-16'\n  },\n  \n  subtitle: {\n    speed: 60,\n    deleteSpeed: 30,\n    pauseTime: 2000,\n    className: 'text-xl md:text-2xl text-text-secondary',\n    cursorClassName: 'bg-electric-purple h-6 md:h-8'\n  },\n  \n  feature: {\n    speed: 50,\n    deleteSpeed: 25,\n    pauseTime: 1500,\n    className: 'text-lg text-text-primary',\n    cursorClassName: 'bg-cyber-green h-5'\n  },\n  \n  code: {\n    speed: 30,\n    deleteSpeed: 15,\n    pauseTime: 1000,\n    className: 'font-mono text-matrix-green',\n    cursorClassName: 'bg-matrix-green h-4'\n  }\n};\n\n// Enhanced typing text with preset support\nexport const EnhancedTypingText = ({ preset, ...props }) => {\n  const presetConfig = preset ? TypingTextPresets[preset] : {};\n  const mergedProps = { ...presetConfig, ...props };\n  \n  return <TypingText {...mergedProps} />;\n};\n\nexport default TypingText;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;;AAKA,MAAM,aAAa,CAAC,EAClB,QAAQ;IAAC;CAAyB,EAClC,QAAQ,GAAG,EACX,cAAc,EAAE,EAChB,YAAY,IAAI,EAChB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,aAAa,IAAI,EACjB,aAAa,IAAI,EAClB;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,KAAK,GAAG;QAExB,MAAM,kBAAkB,KAAK,CAAC,iBAAiB;QAE/C,MAAM,UAAU,WAAW;YACzB,IAAI,UAAU;gBACZ,YAAY;gBACZ,IAAI,QAAQ,mBAAmB,MAAM,MAAM,GAAG,GAAG;oBAC/C,cAAc;gBAChB;gBACA;YACF;YAEA,IAAI,YAAY;gBACd,sBAAsB;gBACtB,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,eAAe,YAAY,KAAK,CAAC,GAAG,CAAC;gBACvC,OAAO;oBACL,uCAAuC;oBACvC,cAAc;oBACd,oBAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;gBACzD;YACF,OAAO;gBACL,oBAAoB;gBACpB,IAAI,YAAY,MAAM,GAAG,gBAAgB,MAAM,EAAE;oBAC/C,eAAe,gBAAgB,KAAK,CAAC,GAAG,YAAY,MAAM,GAAG;gBAC/D,OAAO;oBACL,+BAA+B;oBAC/B,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM;wBAC/B,oCAAoC;wBACpC,IAAI,YAAY;wBAChB;oBACF;oBACA,uDAAuD;oBACvD,YAAY;gBACd;YACF;QACF,GAAG,WAAW,YAAY,aAAa,cAAc;QAErD,OAAO,IAAM,aAAa;IAC5B,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,SAAS,kBACb,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;YAEF,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;;;;;;IAIJ,qBACE,8OAAC;mDAAgB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;;YACjC;YACA,4BAAc,8OAAC;;;;;;;;;;;;;;;;;AAUtB;AAGO,MAAM,oBAAoB;IAC/B,MAAM;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,UAAU;QACR,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,SAAS;QACP,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,MAAM;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;AACF;AAGO,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO;IACrD,MAAM,eAAe,SAAS,iBAAiB,CAAC,OAAO,GAAG,CAAC;IAC3D,MAAM,cAAc;QAAE,GAAG,YAAY;QAAE,GAAG,KAAK;IAAC;IAEhD,qBAAO,8OAAC;QAAY,GAAG,WAAW;;;;;;AACpC;uCAEe", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/effects/MatrixEffect.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\nconst MatrixEffect = ({ \n  className = '',\n  density = 0.8,\n  speed = 50,\n  color = '#00ff41',\n  fontSize = 14,\n  opacity = 0.8\n}) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    \n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = canvas.offsetWidth;\n      canvas.height = canvas.offsetHeight;\n    };\n    \n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Matrix characters (mix of katakana, numbers, and symbols)\n    const chars = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+-=[]{}|;:,.<>?';\n    const charArray = chars.split('');\n\n    // Calculate columns\n    const columns = Math.floor(canvas.width / fontSize);\n    const drops = new Array(columns).fill(1);\n\n    // Animation function\n    const animate = () => {\n      // Semi-transparent black background for trail effect\n      ctx.fillStyle = `rgba(10, 10, 15, ${1 - opacity})`;\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Set text properties\n      ctx.fillStyle = color;\n      ctx.font = `${fontSize}px 'JetBrains Mono', monospace`;\n      ctx.textAlign = 'center';\n\n      // Draw characters\n      for (let i = 0; i < drops.length; i++) {\n        // Random character\n        const char = charArray[Math.floor(Math.random() * charArray.length)];\n        \n        // Calculate position\n        const x = i * fontSize + fontSize / 2;\n        const y = drops[i] * fontSize;\n\n        // Add glow effect\n        ctx.shadowColor = color;\n        ctx.shadowBlur = 10;\n        \n        // Draw character\n        ctx.fillText(char, x, y);\n        \n        // Reset shadow\n        ctx.shadowBlur = 0;\n\n        // Reset drop to top randomly or when it reaches bottom\n        if (y > canvas.height && Math.random() > density) {\n          drops[i] = 0;\n        }\n\n        // Move drop down\n        drops[i]++;\n      }\n\n      // Continue animation\n      animationRef.current = setTimeout(animate, speed);\n    };\n\n    // Start animation\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        clearTimeout(animationRef.current);\n      }\n    };\n  }, [density, speed, color, fontSize, opacity]);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className={`absolute inset-0 w-full h-full pointer-events-none ${className}`}\n      style={{ zIndex: -1 }}\n    />\n  );\n};\n\nexport default MatrixEffect;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,eAAe,CAAC,EACpB,YAAY,EAAE,EACd,UAAU,GAAG,EACb,QAAQ,EAAE,EACV,QAAQ,SAAS,EACjB,WAAW,EAAE,EACb,UAAU,GAAG,EACd;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,kBAAkB;QAClB,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,WAAW;YACjC,OAAO,MAAM,GAAG,OAAO,YAAY;QACrC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,4DAA4D;QAC5D,MAAM,QAAQ;QACd,MAAM,YAAY,MAAM,KAAK,CAAC;QAE9B,oBAAoB;QACpB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG;QAC1C,MAAM,QAAQ,IAAI,MAAM,SAAS,IAAI,CAAC;QAEtC,qBAAqB;QACrB,MAAM,UAAU;YACd,qDAAqD;YACrD,IAAI,SAAS,GAAG,CAAC,iBAAiB,EAAE,IAAI,QAAQ,CAAC,CAAC;YAClD,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE9C,sBAAsB;YACtB,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI,GAAG,GAAG,SAAS,8BAA8B,CAAC;YACtD,IAAI,SAAS,GAAG;YAEhB,kBAAkB;YAClB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,mBAAmB;gBACnB,MAAM,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;gBAEpE,qBAAqB;gBACrB,MAAM,IAAI,IAAI,WAAW,WAAW;gBACpC,MAAM,IAAI,KAAK,CAAC,EAAE,GAAG;gBAErB,kBAAkB;gBAClB,IAAI,WAAW,GAAG;gBAClB,IAAI,UAAU,GAAG;gBAEjB,iBAAiB;gBACjB,IAAI,QAAQ,CAAC,MAAM,GAAG;gBAEtB,eAAe;gBACf,IAAI,UAAU,GAAG;gBAEjB,uDAAuD;gBACvD,IAAI,IAAI,OAAO,MAAM,IAAI,KAAK,MAAM,KAAK,SAAS;oBAChD,KAAK,CAAC,EAAE,GAAG;gBACb;gBAEA,iBAAiB;gBACjB,KAAK,CAAC,EAAE;YACV;YAEA,qBAAqB;YACrB,aAAa,OAAO,GAAG,WAAW,SAAS;QAC7C;QAEA,kBAAkB;QAClB;QAEA,UAAU;QACV,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,aAAa,OAAO;YACnC;QACF;IACF,GAAG;QAAC;QAAS;QAAO;QAAO;QAAU;KAAQ;IAE7C,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,mDAAmD,EAAE,WAAW;QAC5E,OAAO;YAAE,QAAQ,CAAC;QAAE;;;;;;AAG1B;uCAEe", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport Button from '@/components/ui/Button';\nimport { EnhancedTypingText } from '@/components/effects/TypingText';\nimport MatrixEffect from '@/components/effects/MatrixEffect';\n\nconst HeroSection = () => {\n  const heroRef = useRef(null);\n  const floatingElementsRef = useRef([]);\n\n  // Floating elements animation\n  useEffect(() => {\n    const elements = floatingElementsRef.current;\n    \n    elements.forEach((element, index) => {\n      if (element) {\n        const delay = index * 0.5;\n        const duration = 3 + Math.random() * 2;\n        \n        element.style.animationDelay = `${delay}s`;\n        element.style.animationDuration = `${duration}s`;\n      }\n    });\n  }, []);\n\n  const heroTexts = [\n    \"Your Name. Reinvented.\",\n    \"Not Just a Card—An Experience.\",\n    \"Connect in 3D. Remember Forever.\",\n    \"The Future of Networking Is Here.\"\n  ];\n\n  const FloatingCard = ({ index, className = '' }) => (\n    <div\n      ref={el => floatingElementsRef.current[index] = el}\n      className={`absolute opacity-20 animate-float ${className}`}\n      style={{\n        animationName: 'float',\n        animationTimingFunction: 'ease-in-out',\n        animationIterationCount: 'infinite',\n        animationDirection: 'alternate'\n      }}\n    >\n      <div className=\"w-16 h-10 bg-gradient-to-r from-neon-blue to-electric-purple rounded-lg shadow-lg shadow-neon-blue/25 transform rotate-12\" />\n    </div>\n  );\n\n  const StatsCard = ({ number, label, delay = 0 }) => (\n    <div \n      className=\"text-center group\"\n      style={{ animationDelay: `${delay}s` }}\n    >\n      <div className=\"text-2xl md:text-3xl font-bold font-display text-neon-blue mb-1 group-hover:scale-110 transition-transform duration-300\">\n        {number}\n      </div>\n      <div className=\"text-sm text-text-secondary\">{label}</div>\n    </div>\n  );\n\n  return (\n    <section \n      ref={heroRef}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-space via-deep-purple to-midnight-blue\"\n    >\n      {/* Matrix Background Effect */}\n      <MatrixEffect \n        className=\"absolute inset-0\" \n        density={0.6}\n        speed={80}\n        opacity={0.3}\n      />\n      \n      {/* Floating 3D Elements */}\n      <FloatingCard index={0} className=\"top-20 left-10\" />\n      <FloatingCard index={1} className=\"top-40 right-20\" />\n      <FloatingCard index={2} className=\"bottom-40 left-20\" />\n      <FloatingCard index={3} className=\"bottom-20 right-10\" />\n      <FloatingCard index={4} className=\"top-60 left-1/3\" />\n      <FloatingCard index={5} className=\"bottom-60 right-1/3\" />\n\n      {/* Main Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"space-y-8\">\n          {/* Main Headline */}\n          <div className=\"space-y-4\">\n            <h1 className=\"text-5xl md:text-7xl lg:text-8xl font-bold font-display leading-tight\">\n              <EnhancedTypingText\n                preset=\"hero\"\n                texts={heroTexts}\n                speed={100}\n                deleteSpeed={50}\n                pauseTime={3000}\n                loop={true}\n              />\n            </h1>\n            \n            <p className=\"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed\">\n              AR-enhanced digital business cards that let you share stunning, interactive profiles via \n              <span className=\"text-neon-blue font-semibold\"> QR, NFC, facial recognition, or camera scan</span>\n              —no app required.\n            </p>\n          </div>\n\n          {/* Mini Demo Preview */}\n          <div className=\"relative max-w-md mx-auto\">\n            <div className=\"bg-surface-dark/80 backdrop-blur-md rounded-2xl p-6 border border-electric-purple/30 hover:border-neon-blue/50 transition-all duration-300 group\">\n              <div className=\"relative\">\n                {/* Simulated 3D Card */}\n                <div className=\"w-full h-32 bg-gradient-to-br from-neon-blue via-electric-purple to-hologram-pink rounded-xl shadow-2xl shadow-neon-blue/30 transform group-hover:scale-105 group-hover:rotate-3 transition-all duration-500\">\n                  <div className=\"absolute inset-0 bg-white/10 rounded-xl backdrop-blur-sm\">\n                    <div className=\"p-4 h-full flex flex-col justify-between\">\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-8 h-8 bg-white/20 rounded-full\"></div>\n                        <div>\n                          <div className=\"w-16 h-2 bg-white/30 rounded mb-1\"></div>\n                          <div className=\"w-12 h-1.5 bg-white/20 rounded\"></div>\n                        </div>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <div className=\"w-20 h-1.5 bg-white/30 rounded\"></div>\n                        <div className=\"w-16 h-1.5 bg-white/20 rounded\"></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Floating particles */}\n                <div className=\"absolute -top-2 -right-2 w-2 h-2 bg-neon-blue rounded-full animate-ping\"></div>\n                <div className=\"absolute -bottom-2 -left-2 w-1.5 h-1.5 bg-electric-purple rounded-full animate-pulse\"></div>\n              </div>\n              \n              <p className=\"text-sm text-text-secondary mt-4 group-hover:text-neon-blue transition-colors duration-300\">\n                ✨ Live 3D Card Preview\n              </p>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Link href=\"/demo\">\n              <Button \n                variant=\"primary\" \n                size=\"lg\"\n                className=\"text-lg font-semibold px-8 py-4 shadow-2xl shadow-neon-blue/30 hover:shadow-electric-purple/40\"\n              >\n                Try Live Demo\n              </Button>\n            </Link>\n            \n            <Link href=\"/pitch\">\n              <Button \n                variant=\"outline\" \n                size=\"lg\"\n                className=\"text-lg font-semibold px-8 py-4\"\n              >\n                Watch Pitch\n              </Button>\n            </Link>\n          </div>\n\n          {/* Stats Row */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto pt-12 border-t border-electric-purple/20\">\n            <StatsCard number=\"88%\" label=\"Cards Thrown Away\" delay={0.2} />\n            <StatsCard number=\"7B+\" label=\"Cards Printed Yearly\" delay={0.4} />\n            <StatsCard number=\"70%\" label=\"Better Retention\" delay={0.6} />\n            <StatsCard number=\"0\" label=\"App Downloads\" delay={0.8} />\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-neon-blue rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-neon-blue rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n\n      {/* CSS for floating animation */}\n      <style jsx>{`\n        @keyframes float {\n          0% { transform: translateY(0px) rotate(12deg); }\n          100% { transform: translateY(-20px) rotate(15deg); }\n        }\n        \n        .animate-float {\n          animation: float 3s ease-in-out infinite alternate;\n        }\n      `}</style>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQA,MAAM,cAAc;IAClB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAErC,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,oBAAoB,OAAO;QAE5C,SAAS,OAAO,CAAC,CAAC,SAAS;YACzB,IAAI,SAAS;gBACX,MAAM,QAAQ,QAAQ;gBACtB,MAAM,WAAW,IAAI,KAAK,MAAM,KAAK;gBAErC,QAAQ,KAAK,CAAC,cAAc,GAAG,GAAG,MAAM,CAAC,CAAC;gBAC1C,QAAQ,KAAK,CAAC,iBAAiB,GAAG,GAAG,SAAS,CAAC,CAAC;YAClD;QACF;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE,iBAC7C,8OAAC;YACC,KAAK,CAAA,KAAM,oBAAoB,OAAO,CAAC,MAAM,GAAG;YAChD,WAAW,CAAC,kCAAkC,EAAE,WAAW;YAC3D,OAAO;gBACL,eAAe;gBACf,yBAAyB;gBACzB,yBAAyB;gBACzB,oBAAoB;YACtB;sBAEA,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAInB,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,iBAC7C,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,gBAAgB,GAAG,MAAM,CAAC,CAAC;YAAC;;8BAErC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,8OAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;IAIlD,qBACE,8OAAC;QACC,KAAK;kDACK;;0BAGV,8OAAC,4IAAA,CAAA,UAAY;gBACX,WAAU;gBACV,SAAS;gBACT,OAAO;gBACP,SAAS;;;;;;0BAIX,8OAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,8OAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,8OAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,8OAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,8OAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,8OAAC;gBAAa,OAAO;0DAAa;;;;;;0BAGlC,8OAAC;0DAAc;0BACb,cAAA,8OAAC;8DAAc;;sCAEb,8OAAC;sEAAc;;8CACb,8OAAC;8EAAa;8CACZ,cAAA,8OAAC,0IAAA,CAAA,qBAAkB;wCACjB,QAAO;wCACP,OAAO;wCACP,OAAO;wCACP,aAAa;wCACb,WAAW;wCACX,MAAM;;;;;;;;;;;8CAIV,8OAAC;8EAAY;;wCAA4E;sDAEvF,8OAAC;sFAAe;sDAA+B;;;;;;wCAAmD;;;;;;;;;;;;;sCAMtG,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;;0DAEb,8OAAC;0FAAc;0DACb,cAAA,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAc;;0EACb,8OAAC;0GAAc;;kFACb,8OAAC;kHAAc;;;;;;kFACf,8OAAC;;;0FACC,8OAAC;0HAAc;;;;;;0FACf,8OAAC;0HAAc;;;;;;;;;;;;;;;;;;0EAGnB,8OAAC;0GAAc;;kFACb,8OAAC;kHAAc;;;;;;kFACf,8OAAC;kHAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOvB,8OAAC;0FAAc;;;;;;0DACf,8OAAC;0FAAc;;;;;;;;;;;;kDAGjB,8OAAC;kFAAY;kDAA6F;;;;;;;;;;;;;;;;;sCAO9G,8OAAC;sEAAc;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,iIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAKH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,iIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,8OAAC;sEAAc;;8CACb,8OAAC;oCAAU,QAAO;oCAAM,OAAM;oCAAoB,OAAO;;;;;;;8CACzD,8OAAC;oCAAU,QAAO;oCAAM,OAAM;oCAAuB,OAAO;;;;;;;8CAC5D,8OAAC;oCAAU,QAAO;oCAAM,OAAM;oCAAmB,OAAO;;;;;;;8CACxD,8OAAC;oCAAU,QAAO;oCAAI,OAAM;oCAAgB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;0DAAc;0BACb,cAAA,8OAAC;8DAAc;8BACb,cAAA,8OAAC;kEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBzB;uCAEe", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/ui/Card.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = forwardRef(({ \n  className, \n  variant = 'default',\n  hover = true,\n  glow = false,\n  children, \n  ...props \n}, ref) => {\n  const baseStyles = `\n    relative rounded-xl border backdrop-blur-sm\n    transition-all duration-300 ease-out\n    overflow-hidden group\n  `;\n\n  const variants = {\n    default: `\n      bg-surface-dark/80 border-electric-purple/20\n      hover:border-neon-blue/40 hover:bg-surface-light/80\n    `,\n    glass: `\n      bg-white/5 border-white/10\n      hover:bg-white/10 hover:border-white/20\n      backdrop-blur-md\n    `,\n    neon: `\n      bg-dark-space/90 border-neon-blue/50\n      hover:border-neon-blue hover:bg-dark-space\n      shadow-lg shadow-neon-blue/10\n    `,\n    purple: `\n      bg-deep-purple/80 border-electric-purple/30\n      hover:border-electric-purple hover:bg-deep-purple\n      shadow-lg shadow-electric-purple/10\n    `,\n    gradient: `\n      bg-gradient-to-br from-surface-dark/80 to-deep-purple/80\n      border-gradient-to-r from-neon-blue/30 to-electric-purple/30\n      hover:from-surface-light/80 hover:to-midnight-blue/80\n    `\n  };\n\n  const hoverEffects = hover ? `\n    hover:scale-[1.02] hover:-translate-y-1\n    hover:shadow-2xl hover:shadow-neon-blue/20\n  ` : '';\n\n  const glowEffect = glow ? `\n    before:absolute before:inset-0 before:rounded-xl\n    before:bg-gradient-to-r before:from-neon-blue/20 before:to-electric-purple/20\n    before:opacity-0 before:transition-opacity before:duration-300\n    hover:before:opacity-100\n  ` : '';\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        baseStyles,\n        variants[variant],\n        hoverEffects,\n        glowEffect,\n        className\n      )}\n      {...props}\n    >\n      {/* Animated border gradient */}\n      <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-neon-blue via-electric-purple to-hologram-pink opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n      \n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n      \n      {/* Corner accent */}\n      <div className=\"absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-neon-blue/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n    </div>\n  );\n});\n\nCard.displayName = 'Card';\n\n// Card Header Component\nconst CardHeader = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('p-6 pb-4', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardHeader.displayName = 'CardHeader';\n\n// Card Title Component\nconst CardTitle = forwardRef(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-xl font-semibold text-text-primary mb-2',\n      'bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </h3>\n));\n\nCardTitle.displayName = 'CardTitle';\n\n// Card Description Component\nconst CardDescription = forwardRef(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-text-secondary leading-relaxed', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\n\nCardDescription.displayName = 'CardDescription';\n\n// Card Content Component\nconst CardContent = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('px-6 pb-6', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardContent.displayName = 'CardContent';\n\n// Card Footer Component\nconst CardFooter = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'px-6 py-4 border-t border-electric-purple/20',\n      'bg-gradient-to-r from-transparent via-electric-purple/5 to-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardFooter.displayName = 'CardFooter';\n\n// Export all components\nexport {\n  Card,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n  CardContent,\n  CardFooter\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACvB,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAC;;;;EAIpB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;IAGV,CAAC;QACD,OAAO,CAAC;;;;IAIR,CAAC;QACD,MAAM,CAAC;;;;IAIP,CAAC;QACD,QAAQ,CAAC;;;;IAIT,CAAC;QACD,UAAU,CAAC;;;;IAIX,CAAC;IACH;IAEA,MAAM,eAAe,QAAQ,CAAC;;;EAG9B,CAAC,GAAG;IAEJ,MAAM,aAAa,OAAO,CAAC;;;;;EAK3B,CAAC,GAAG;IAEJ,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,cACA,YACA;QAED,GAAG,KAAK;;0BAGT,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;AAEA,KAAK,WAAW,GAAG;AAEnB,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;AAIL,WAAW,WAAW,GAAG;AAEzB,uBAAuB;AACvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC/D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gDACA,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIL,UAAU,WAAW,GAAG;AAExB,6BAA6B;AAC7B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACrE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;QACpD,GAAG,KAAK;kBAER;;;;;;AAIL,gBAAgB,WAAW,GAAG;AAE9B,yBAAyB;AACzB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACjE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;kBAER;;;;;;AAIL,YAAY,WAAW,GAAG;AAE1B,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gDACA,0EACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIL,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/ProblemSection.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect } from 'react';\nimport { Card, CardContent } from '@/components/ui/Card';\n\nconst ProblemSection = () => {\n  const sectionRef = useRef(null);\n  const cardsRef = useRef([]);\n\n  // 3D Tilt Effect\n  useEffect(() => {\n    const handleMouseMove = (e, card) => {\n      if (!card) return;\n      \n      const rect = card.getBoundingClientRect();\n      const x = e.clientX - rect.left;\n      const y = e.clientY - rect.top;\n      \n      const centerX = rect.width / 2;\n      const centerY = rect.height / 2;\n      \n      const rotateX = (y - centerY) / 10;\n      const rotateY = (centerX - x) / 10;\n      \n      card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;\n    };\n\n    const handleMouseLeave = (card) => {\n      if (!card) return;\n      card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';\n    };\n\n    cardsRef.current.forEach((card) => {\n      if (card) {\n        const mouseMoveHandler = (e) => handleMouseMove(e, card);\n        const mouseLeaveHandler = () => handleMouseLeave(card);\n        \n        card.addEventListener('mousemove', mouseMoveHandler);\n        card.addEventListener('mouseleave', mouseLeaveHandler);\n        \n        return () => {\n          card.removeEventListener('mousemove', mouseMoveHandler);\n          card.removeEventListener('mouseleave', mouseLeaveHandler);\n        };\n      }\n    });\n  }, []);\n\n  const problems = [\n    {\n      icon: '🗑️',\n      title: 'Paper Cards Get Lost',\n      description: '88% of business cards are thrown away within a week. Your investment literally goes in the trash.',\n      stat: '7B+ cards wasted yearly'\n    },\n    {\n      icon: '📱',\n      title: 'Tech Dependency',\n      description: 'QR codes need cameras, NFC needs compatible devices. What happens when tech fails?',\n      stat: '30% compatibility issues'\n    },\n    {\n      icon: '😴',\n      title: 'Boring & Forgettable',\n      description: 'Static cards blend into the noise. No engagement, no story, no lasting impression.',\n      stat: '95% forgotten instantly'\n    },\n    {\n      icon: '🔄',\n      title: 'Manual Effort Required',\n      description: 'Typing contact info, updating details, managing connections. Too much friction.',\n      stat: '5 minutes per contact'\n    }\n  ];\n\n  return (\n    <section \n      ref={sectionRef}\n      className=\"py-20 bg-gradient-to-b from-midnight-blue to-deep-purple relative overflow-hidden\"\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(0,245,255,0.1),transparent_50%)]\"></div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-hologram-pink to-quantum-gold bg-clip-text text-transparent\">\n              The Problem with Traditional Networking\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n            In a digital world, we're still stuck with analog solutions that waste time, money, and opportunities.\n          </p>\n        </div>\n\n        {/* Problem Cards Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {problems.map((problem, index) => (\n            <Card\n              key={index}\n              ref={el => cardsRef.current[index] = el}\n              variant=\"glass\"\n              className=\"h-full transition-all duration-300 cursor-pointer transform-gpu\"\n              style={{ transformStyle: 'preserve-3d' }}\n            >\n              <CardContent className=\"p-6 h-full flex flex-col\">\n                {/* Icon */}\n                <div className=\"text-4xl mb-4 text-center\">{problem.icon}</div>\n                \n                {/* Title */}\n                <h3 className=\"text-xl font-semibold text-text-primary mb-3 text-center\">\n                  {problem.title}\n                </h3>\n                \n                {/* Description */}\n                <p className=\"text-text-secondary text-center mb-4 flex-grow\">\n                  {problem.description}\n                </p>\n                \n                {/* Stat */}\n                <div className=\"text-center\">\n                  <span className=\"inline-block px-3 py-1 bg-hologram-pink/20 text-hologram-pink rounded-full text-sm font-medium\">\n                    {problem.stat}\n                  </span>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-lg text-text-secondary mb-6\">\n            It's time for a solution that works in the real world.\n          </p>\n          <div className=\"w-24 h-1 bg-gradient-to-r from-hologram-pink to-quantum-gold mx-auto rounded-full\"></div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ProblemSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE1B,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC,GAAG;YAC1B,IAAI,CAAC,MAAM;YAEX,MAAM,OAAO,KAAK,qBAAqB;YACvC,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;YAC/B,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG;YAE9B,MAAM,UAAU,KAAK,KAAK,GAAG;YAC7B,MAAM,UAAU,KAAK,MAAM,GAAG;YAE9B,MAAM,UAAU,CAAC,IAAI,OAAO,IAAI;YAChC,MAAM,UAAU,CAAC,UAAU,CAAC,IAAI;YAEhC,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,4BAA4B,EAAE,QAAQ,aAAa,EAAE,QAAQ,8BAA8B,CAAC;QACtH;QAEA,MAAM,mBAAmB,CAAC;YACxB,IAAI,CAAC,MAAM;YACX,KAAK,KAAK,CAAC,SAAS,GAAG;QACzB;QAEA,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC;YACxB,IAAI,MAAM;gBACR,MAAM,mBAAmB,CAAC,IAAM,gBAAgB,GAAG;gBACnD,MAAM,oBAAoB,IAAM,iBAAiB;gBAEjD,KAAK,gBAAgB,CAAC,aAAa;gBACnC,KAAK,gBAAgB,CAAC,cAAc;gBAEpC,OAAO;oBACL,KAAK,mBAAmB,CAAC,aAAa;oBACtC,KAAK,mBAAmB,CAAC,cAAc;gBACzC;YACF;QACF;IACF,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAoF;;;;;;;;;;;0CAItG,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAM/D,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,+HAAA,CAAA,OAAI;gCAEH,KAAK,CAAA,KAAM,SAAS,OAAO,CAAC,MAAM,GAAG;gCACrC,SAAQ;gCACR,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAc;0CAEvC,cAAA,8OAAC,+HAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,8OAAC;4CAAI,WAAU;sDAA6B,QAAQ,IAAI;;;;;;sDAGxD,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAIhB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAItB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,QAAQ,IAAI;;;;;;;;;;;;;;;;;+BAvBd;;;;;;;;;;kCAgCX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAGhD,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/SolutionSection.js"], "sourcesContent": ["'use client';\n\nimport { EnhancedTypingText } from '@/components/effects/TypingText';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst SolutionSection = () => {\n  const features = [\n    \"AR-powered digital business cards\",\n    \"Multiple sharing methods: QR, NFC, camera scan\",\n    \"Works without app downloads\",\n    \"Real-time 3D animations\",\n    \"Name/number recognition technology\"\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-deep-purple to-dark-space relative\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-cyber-green to-neon-blue bg-clip-text text-transparent\">\n              Our Solution\n            </span>\n          </h2>\n          \n          <div className=\"text-2xl md:text-3xl text-text-primary mb-8\">\n            <EnhancedTypingText\n              preset=\"subtitle\"\n              texts={features}\n              speed={60}\n              deleteSpeed={30}\n              pauseTime={2000}\n              loop={true}\n            />\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          <div>\n            <h3 className=\"text-3xl font-bold text-text-primary mb-6\">\n              The Future of Professional Identity\n            </h3>\n            <p className=\"text-lg text-text-secondary mb-8\">\n              NameCardAI combines cutting-edge AR technology with practical networking needs. \n              Share your professional identity through immersive 3D experiences that people actually remember.\n            </p>\n            \n            <div className=\"space-y-4 mb-8\">\n              {[\n                \"✨ AR-enhanced visual storytelling\",\n                \"🔄 Multiple sharing methods\",\n                \"📱 No app downloads required\",\n                \"🎯 Instant recognition technology\"\n              ].map((feature, index) => (\n                <div key={index} className=\"flex items-center space-x-3\">\n                  <span className=\"text-cyber-green\">{feature}</span>\n                </div>\n              ))}\n            </div>\n\n            <Button variant=\"primary\" size=\"lg\">\n              See How It Works\n            </Button>\n          </div>\n\n          <div className=\"relative\">\n            <Card variant=\"neon\" className=\"p-8\">\n              <CardContent>\n                <div className=\"aspect-video bg-gradient-to-br from-neon-blue/20 to-electric-purple/20 rounded-lg flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl mb-4\">🚀</div>\n                    <p className=\"text-text-secondary\">Interactive Demo Coming Soon</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default SolutionSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAA+E;;;;;;;;;;;sCAKjG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0IAAA,CAAA,qBAAkB;gCACjB,QAAO;gCACP,OAAO;gCACP,OAAO;gCACP,aAAa;gCACb,WAAW;gCACX,MAAM;;;;;;;;;;;;;;;;;8BAKZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAKhD,8OAAC;oCAAI,WAAU;8CACZ;wCACC;wCACA;wCACA;wCACA;qCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;4CAAgB,WAAU;sDACzB,cAAA,8OAAC;gDAAK,WAAU;0DAAoB;;;;;;2CAD5B;;;;;;;;;;8CAMd,8OAAC,iIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;sCAKtC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,+HAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAO,WAAU;0CAC7B,cAAA,8OAAC,+HAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAE,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvD;uCAEe", "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/ProcessSection.js"], "sourcesContent": ["'use client';\n\nconst ProcessSection = () => {\n  const steps = [\n    {\n      number: \"01\",\n      title: \"Create Your Card\",\n      description: \"Design your AR-enhanced digital business card with our intuitive editor\",\n      icon: \"🎨\"\n    },\n    {\n      number: \"02\", \n      title: \"Share Instantly\",\n      description: \"Share via QR, NFC, camera scan, or just your name - no app required\",\n      icon: \"📤\"\n    },\n    {\n      number: \"03\",\n      title: \"Make Impact\",\n      description: \"Recipients see your 3D animated card with immersive AR experience\",\n      icon: \"✨\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-dark-space to-midnight-blue\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-quantum-gold to-hologram-pink bg-clip-text text-transparent\">\n              How It Works\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary\">\n            Three simple steps to revolutionize your networking\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {steps.map((step, index) => (\n            <div key={index} className=\"text-center group\">\n              <div className=\"relative mb-8\">\n                <div className=\"w-20 h-20 bg-gradient-to-r from-neon-blue to-electric-purple rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <span className=\"text-2xl\">{step.icon}</span>\n                </div>\n                <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-quantum-gold rounded-full flex items-center justify-center text-dark-space font-bold text-sm\">\n                  {step.number}\n                </div>\n              </div>\n              \n              <h3 className=\"text-xl font-semibold text-text-primary mb-4\">\n                {step.title}\n              </h3>\n              \n              <p className=\"text-text-secondary\">\n                {step.description}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ProcessSection;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,iBAAiB;IACrB,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAoF;;;;;;;;;;;sCAItG,8OAAC;4BAAE,WAAU;sCAA8B;;;;;;;;;;;;8BAK7C,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAY,KAAK,IAAI;;;;;;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;;;;;;;8CAIhB,8OAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAGb,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;2BAfX;;;;;;;;;;;;;;;;;;;;;AAuBtB;uCAEe", "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/FeaturesSection.js"], "sourcesContent": ["'use client';\n\nconst FeaturesSection = () => {\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-midnight-blue to-deep-purple\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent\">\n              MVP Features\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary\">\n            Carousel with parallax effects coming soon...\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC;4BAAK,WAAU;sCAAmF;;;;;;;;;;;kCAIrG,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 1542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/ComparisonSection.js"], "sourcesContent": ["'use client';\n\nconst ComparisonSection = () => {\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-deep-purple to-dark-space\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-hologram-pink to-quantum-gold bg-clip-text text-transparent\">\n              Competitor Comparison\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary\">\n            Interactive comparison table coming soon...\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ComparisonSection;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,oBAAoB;IACxB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC;4BAAK,WAAU;sCAAoF;;;;;;;;;;;kCAItG,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/TestimonialsSection.js"], "sourcesContent": ["'use client';\n\nconst TestimonialsSection = () => {\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-dark-space to-midnight-blue\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-cyber-green to-neon-blue bg-clip-text text-transparent\">\n              Testimonials\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary\">\n            Floating cards with testimonials coming soon...\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TestimonialsSection;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,sBAAsB;IAC1B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC;4BAAK,WAAU;sCAA+E;;;;;;;;;;;kCAIjG,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 1664, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/ValueSection.js"], "sourcesContent": ["'use client';\n\nconst ValueSection = () => {\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-midnight-blue to-deep-purple\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-electric-purple to-hologram-pink bg-clip-text text-transparent\">\n              Value Proposition\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary\">\n            Audio-responsive visuals coming soon...\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ValueSection;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,eAAe;IACnB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC;4BAAK,WAAU;sCAAuF;;;;;;;;;;;kCAIzG,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 1725, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/PricingSection.js"], "sourcesContent": ["'use client';\n\nconst PricingSection = () => {\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-deep-purple to-dark-space\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-quantum-gold to-cyber-green bg-clip-text text-transparent\">\n              Pricing Plans\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary\">\n            Hover transformations and pricing cards coming soon...\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default PricingSection;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,iBAAiB;IACrB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC;4BAAK,WAAU;sCAAkF;;;;;;;;;;;kCAIpG,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 1786, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/TrustSection.js"], "sourcesContent": ["'use client';\n\nconst TrustSection = () => {\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-dark-space to-midnight-blue\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent\">\n              Trust Elements\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary\">\n            Smoke effects and trust indicators coming soon...\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TrustSection;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,eAAe;IACnB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC;4BAAK,WAAU;sCAAmF;;;;;;;;;;;kCAIrG,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 1847, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/EarlyAdopterSection.js"], "sourcesContent": ["'use client';\n\nconst EarlyAdopterSection = () => {\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-midnight-blue to-deep-purple\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-hologram-pink to-quantum-gold bg-clip-text text-transparent\">\n              Early Adopter CTA\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary\">\n            Fireflies background and early adopter loop coming soon...\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default EarlyAdopterSection;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,sBAAsB;IAC1B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC;4BAAK,WAAU;sCAAoF;;;;;;;;;;;kCAItG,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 1908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/FooterSection.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nconst FooterSection = () => {\n  return (\n    <footer className=\"py-16 bg-gradient-to-b from-deep-purple to-dark-space border-t border-electric-purple/20\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-neon-blue to-electric-purple rounded-lg\"></div>\n              <span className=\"text-xl font-bold font-display text-text-primary\">NameCardAI</span>\n            </div>\n            <p className=\"text-text-secondary mb-4\">\n              Revolutionizing professional networking with AR-enhanced digital business cards.\n            </p>\n            <p className=\"text-sm text-text-secondary\">\n              © 2024 NameCardAI. All rights reserved.\n            </p>\n          </div>\n\n          {/* Links */}\n          <div>\n            <h3 className=\"text-text-primary font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/demo\" className=\"text-text-secondary hover:text-neon-blue transition-colors\">Demo</Link></li>\n              <li><Link href=\"/pricing\" className=\"text-text-secondary hover:text-neon-blue transition-colors\">Pricing</Link></li>\n              <li><Link href=\"/features\" className=\"text-text-secondary hover:text-neon-blue transition-colors\">Features</Link></li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"text-text-primary font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/about\" className=\"text-text-secondary hover:text-neon-blue transition-colors\">About</Link></li>\n              <li><Link href=\"/contact\" className=\"text-text-secondary hover:text-neon-blue transition-colors\">Contact</Link></li>\n              <li><Link href=\"/privacy\" className=\"text-text-secondary hover:text-neon-blue transition-colors\">Privacy</Link></li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default FooterSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAmD;;;;;;;;;;;;0CAErE,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;;;;;;;kCAM7C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;sDAA6D;;;;;;;;;;;kDAC9F,8OAAC;kDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA6D;;;;;;;;;;;kDACjG,8OAAC;kDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;sDAA6D;;;;;;;;;;;;;;;;;;;;;;;kCAItG,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA6D;;;;;;;;;;;kDAC/F,8OAAC;kDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA6D;;;;;;;;;;;kDACjG,8OAAC;kDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/G;uCAEe", "debugId": null}}]}