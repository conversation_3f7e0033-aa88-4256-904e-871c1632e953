// Advanced AI-Powered Business Card Data

export const sampleCards = [
  {
    id: 'ai-ceo-001',
    name: 'Dr. <PERSON>',
    title: 'CEO & AI Research Director',
    company: 'QuantumMind Technologies',
    industry: 'Artificial Intelligence',
    location: 'San Francisco, CA',
    
    // Contact Information
    email: '<EMAIL>',
    phone: '+****************',
    website: 'quantummind.ai',
    linkedin: 'linkedin.com/in/sarahchen-ai',
    twitter: '@sarahchen_ai',
    
    // Visual Identity
    avatar: '🧠',
    profileImage: '/avatars/sarah-chen.jpg',
    companyLogo: '/logos/quantummind.svg',
    
    // AI-Enhanced Features
    aiPersonality: {
      voiceIntro: 'Hi! I\'m <PERSON>, and I\'m passionate about building AI that enhances human potential.',
      personalityTraits: ['Innovative', 'Analytical', 'Visionary', 'Collaborative'],
      communicationStyle: 'Direct and inspiring',
      interests: ['Machine Learning', 'Quantum Computing', 'Sustainable Tech', 'Mentoring']
    },
    
    // Dynamic Content
    recentAchievements: [
      'Led breakthrough in quantum-classical hybrid AI models',
      'Raised $50M Series B for QuantumMind Technologies',
      'Keynote speaker at AI Summit 2024',
      'Published 15 papers in top-tier AI journals'
    ],
    
    currentProjects: [
      'Quantum Neural Networks for Drug Discovery',
      'AI Ethics Framework for Enterprise',
      'Next-gen Natural Language Processing'
    ],
    
    // Interactive Elements
    availableActions: [
      { type: 'schedule_meeting', label: 'Schedule a Meeting', icon: '📅' },
      { type: 'view_portfolio', label: 'View Research Portfolio', icon: '📊' },
      { type: 'connect_linkedin', label: 'Connect on LinkedIn', icon: '🔗' },
      { type: 'download_whitepaper', label: 'Download Latest Whitepaper', icon: '📄' },
      { type: 'join_newsletter', label: 'Join AI Newsletter', icon: '📧' }
    ],
    
    // AR/3D Customization
    cardTheme: {
      primary: '#00f5ff',
      secondary: '#8b5cf6',
      accent: '#ff006e',
      background: 'quantum-gradient',
      effects: ['neural-network', 'particle-flow', 'hologram-glitch'],
      animation: 'quantum-float'
    },
    
    // Smart Features
    smartFeatures: {
      autoTranslate: true,
      voiceActivated: true,
      contextAware: true,
      realTimeUpdates: true,
      aiRecommendations: true
    },
    
    // Analytics & Insights
    cardAnalytics: {
      totalViews: 15420,
      uniqueConnections: 892,
      engagementRate: 78,
      topInteractions: ['schedule_meeting', 'view_portfolio', 'connect_linkedin']
    },
    
    // Verification & Trust
    verification: {
      verified: true,
      verificationLevel: 'Enterprise',
      badges: ['AI Expert', 'Thought Leader', 'Verified CEO', 'Speaker'],
      trustScore: 98
    }
  },
  
  {
    id: 'creative-director-002',
    name: 'Marcus Rodriguez',
    title: 'Creative Director & Digital Artist',
    company: 'Neon Studios',
    industry: 'Digital Design',
    location: 'New York, NY',
    
    email: '<EMAIL>',
    phone: '+****************',
    website: 'neonstudios.com',
    instagram: '@marcusrodriguez_art',
    behance: 'behance.net/marcusrodriguez',
    
    avatar: '🎨',
    profileImage: '/avatars/marcus-rodriguez.jpg',
    companyLogo: '/logos/neon-studios.svg',
    
    aiPersonality: {
      voiceIntro: 'Hey there! I\'m Marcus, and I create digital experiences that blur the line between reality and imagination.',
      personalityTraits: ['Creative', 'Bold', 'Experimental', 'Collaborative'],
      communicationStyle: 'Energetic and visual',
      interests: ['Digital Art', 'AR/VR Design', 'Generative AI', 'Interactive Media']
    },
    
    recentAchievements: [
      'Won Cannes Lions Gold for AR Campaign',
      'Featured in Adobe\'s Creative Spotlight',
      'Collaborated with major brands on metaverse experiences',
      'Exhibited at Digital Art Museum NYC'
    ],
    
    currentProjects: [
      'AI-Generated Art Installation',
      'Immersive Brand Experience for Fashion Week',
      'NFT Collection with Environmental Theme'
    ],
    
    availableActions: [
      { type: 'view_portfolio', label: 'View Creative Portfolio', icon: '🎨' },
      { type: 'commission_work', label: 'Commission Artwork', icon: '💼' },
      { type: 'collaborate', label: 'Discuss Collaboration', icon: '🤝' },
      { type: 'follow_instagram', label: 'Follow on Instagram', icon: '📸' },
      { type: 'book_consultation', label: 'Book Design Consultation', icon: '💡' }
    ],
    
    cardTheme: {
      primary: '#ff0080',
      secondary: '#8000ff',
      accent: '#00ffff',
      background: 'neon-gradient',
      effects: ['color-shift', 'paint-splash', 'digital-glitch'],
      animation: 'creative-pulse'
    },
    
    smartFeatures: {
      autoTranslate: true,
      voiceActivated: false,
      contextAware: true,
      realTimeUpdates: true,
      aiRecommendations: true
    },
    
    cardAnalytics: {
      totalViews: 8750,
      uniqueConnections: 445,
      engagementRate: 85,
      topInteractions: ['view_portfolio', 'follow_instagram', 'commission_work']
    },
    
    verification: {
      verified: true,
      verificationLevel: 'Creative Professional',
      badges: ['Award Winner', 'Featured Artist', 'Verified Creator'],
      trustScore: 94
    }
  },
  
  {
    id: 'blockchain-dev-003',
    name: 'Alex Kim',
    title: 'Senior Blockchain Developer',
    company: 'CryptoForge Labs',
    industry: 'Blockchain Technology',
    location: 'Austin, TX',
    
    email: '<EMAIL>',
    phone: '+****************',
    website: 'cryptoforge.dev',
    github: 'github.com/alexkim-blockchain',
    twitter: '@alexkim_crypto',
    
    avatar: '⛓️',
    profileImage: '/avatars/alex-kim.jpg',
    companyLogo: '/logos/cryptoforge.svg',
    
    aiPersonality: {
      voiceIntro: 'Hello! I\'m Alex, and I build the decentralized infrastructure that powers the future of finance.',
      personalityTraits: ['Technical', 'Precise', 'Forward-thinking', 'Security-focused'],
      communicationStyle: 'Technical but approachable',
      interests: ['DeFi Protocols', 'Smart Contracts', 'Cryptography', 'Web3 Gaming']
    },
    
    recentAchievements: [
      'Deployed $100M+ TVL DeFi protocol',
      'Discovered critical vulnerability in major DEX',
      'Open-sourced revolutionary consensus algorithm',
      'Speaker at Ethereum Developer Conference'
    ],
    
    currentProjects: [
      'Layer 2 Scaling Solution for NFTs',
      'Cross-chain Bridge Protocol',
      'Zero-Knowledge Privacy Tools'
    ],
    
    availableActions: [
      { type: 'view_github', label: 'View GitHub Profile', icon: '💻' },
      { type: 'technical_discussion', label: 'Technical Discussion', icon: '🔧' },
      { type: 'code_review', label: 'Request Code Review', icon: '👀' },
      { type: 'join_project', label: 'Collaborate on Project', icon: '🚀' },
      { type: 'follow_twitter', label: 'Follow on Twitter', icon: '🐦' }
    ],
    
    cardTheme: {
      primary: '#00ff41',
      secondary: '#008f11',
      accent: '#00ff88',
      background: 'matrix-gradient',
      effects: ['code-rain', 'blockchain-nodes', 'crypto-pulse'],
      animation: 'tech-float'
    },
    
    smartFeatures: {
      autoTranslate: true,
      voiceActivated: true,
      contextAware: true,
      realTimeUpdates: true,
      aiRecommendations: true
    },
    
    cardAnalytics: {
      totalViews: 12300,
      uniqueConnections: 678,
      engagementRate: 72,
      topInteractions: ['view_github', 'technical_discussion', 'follow_twitter']
    },
    
    verification: {
      verified: true,
      verificationLevel: 'Technical Expert',
      badges: ['Blockchain Expert', 'Security Researcher', 'Open Source Contributor'],
      trustScore: 96
    }
  },

  {
    id: 'marketing-guru-004',
    name: 'Isabella Martinez',
    title: 'Chief Marketing Officer',
    company: 'GrowthHack Labs',
    industry: 'Digital Marketing',
    location: 'Miami, FL',

    email: '<EMAIL>',
    phone: '+****************',
    website: 'growthhack.com',
    linkedin: 'linkedin.com/in/isabella-martinez',
    instagram: '@isabella_growth',

    avatar: '📈',
    profileImage: '/avatars/isabella-martinez.jpg',
    companyLogo: '/logos/growthhack.svg',

    aiPersonality: {
      voiceIntro: 'Hi! I\'m Isabella, and I turn data into growth stories that captivate audiences and drive results.',
      personalityTraits: ['Strategic', 'Creative', 'Data-driven', 'Energetic'],
      communicationStyle: 'Enthusiastic and results-focused',
      interests: ['Growth Hacking', 'Content Strategy', 'Analytics', 'Brand Building']
    },

    recentAchievements: [
      'Scaled startup from 0 to 1M users in 18 months',
      'Won Marketing Campaign of the Year 2024',
      'Generated $50M in revenue through viral campaigns',
      'Built marketing team from 2 to 25 professionals'
    ],

    currentProjects: [
      'AI-Powered Content Generation Platform',
      'Omnichannel Customer Journey Optimization',
      'Influencer Marketing Automation Tool'
    ],

    availableActions: [
      { type: 'growth_consultation', label: 'Growth Strategy Session', icon: '🚀' },
      { type: 'case_study', label: 'View Case Studies', icon: '📊' },
      { type: 'marketing_audit', label: 'Free Marketing Audit', icon: '🔍' },
      { type: 'connect_linkedin', label: 'Connect on LinkedIn', icon: '🔗' },
      { type: 'follow_instagram', label: 'Follow on Instagram', icon: '📸' }
    ],

    cardTheme: {
      primary: '#ff0080',
      secondary: '#ff6b35',
      accent: '#ffd700',
      background: 'sunset-gradient',
      effects: ['growth-chart', 'data-flow', 'success-sparkles'],
      animation: 'growth-pulse'
    },

    smartFeatures: {
      autoTranslate: true,
      voiceActivated: true,
      contextAware: true,
      realTimeUpdates: true,
      aiRecommendations: true
    },

    cardAnalytics: {
      totalViews: 18750,
      uniqueConnections: 1240,
      engagementRate: 89,
      topInteractions: ['growth_consultation', 'case_study', 'marketing_audit']
    },

    verification: {
      verified: true,
      verificationLevel: 'Marketing Expert',
      badges: ['Growth Hacker', 'Campaign Master', 'Analytics Pro'],
      trustScore: 97
    }
  },

  {
    id: 'startup-founder-005',
    name: 'David Park',
    title: 'Founder & CEO',
    company: 'EcoTech Innovations',
    industry: 'Clean Technology',
    location: 'Seattle, WA',

    email: '<EMAIL>',
    phone: '+****************',
    website: 'ecotech.io',
    linkedin: 'linkedin.com/in/davidpark-ecotech',
    twitter: '@davidpark_eco',

    avatar: '🌱',
    profileImage: '/avatars/david-park.jpg',
    companyLogo: '/logos/ecotech.svg',

    aiPersonality: {
      voiceIntro: 'Hello! I\'m David, and I\'m building technology that makes our planet more sustainable, one innovation at a time.',
      personalityTraits: ['Visionary', 'Sustainable', 'Innovative', 'Purpose-driven'],
      communicationStyle: 'Passionate and mission-focused',
      interests: ['Clean Energy', 'Sustainability', 'Climate Tech', 'Social Impact']
    },

    recentAchievements: [
      'Raised $25M Series A for clean energy solutions',
      'Patent holder for revolutionary solar technology',
      'Named in Forbes 30 Under 30 for Energy',
      'Reduced carbon footprint by 1M tons through innovations'
    ],

    currentProjects: [
      'Next-Gen Solar Panel Technology',
      'Carbon Capture AI System',
      'Sustainable Manufacturing Platform'
    ],

    availableActions: [
      { type: 'pitch_meeting', label: 'Schedule Pitch Meeting', icon: '🎯' },
      { type: 'partnership', label: 'Explore Partnership', icon: '🤝' },
      { type: 'investment_deck', label: 'View Investment Deck', icon: '💼' },
      { type: 'sustainability_report', label: 'Impact Report', icon: '🌍' },
      { type: 'follow_journey', label: 'Follow Our Journey', icon: '📱' }
    ],

    cardTheme: {
      primary: '#00ff88',
      secondary: '#00cc6a',
      accent: '#66ff99',
      background: 'eco-gradient',
      effects: ['leaf-particles', 'energy-flow', 'growth-animation'],
      animation: 'eco-pulse'
    },

    smartFeatures: {
      autoTranslate: true,
      voiceActivated: true,
      contextAware: true,
      realTimeUpdates: true,
      aiRecommendations: true
    },

    cardAnalytics: {
      totalViews: 9850,
      uniqueConnections: 567,
      engagementRate: 92,
      topInteractions: ['pitch_meeting', 'partnership', 'sustainability_report']
    },

    verification: {
      verified: true,
      verificationLevel: 'Startup Founder',
      badges: ['Climate Leader', 'Innovation Award', 'Forbes 30 Under 30'],
      trustScore: 95
    }
  }
];

// AI-powered card recommendation engine
export const getRecommendedCards = (userInterests = [], industry = '') => {
  return sampleCards.filter(card => {
    const cardInterests = card.aiPersonality.interests.map(i => i.toLowerCase());
    const matchingInterests = userInterests.filter(interest => 
      cardInterests.some(cardInterest => 
        cardInterest.includes(interest.toLowerCase()) || 
        interest.toLowerCase().includes(cardInterest)
      )
    );
    
    const industryMatch = industry && card.industry.toLowerCase().includes(industry.toLowerCase());
    
    return matchingInterests.length > 0 || industryMatch;
  });
};

// Dynamic card content generator
export const generateDynamicContent = (cardId, context = {}) => {
  const card = sampleCards.find(c => c.id === cardId);
  if (!card) return null;
  
  const { timeOfDay, userLocation, userIndustry } = context;
  
  // Generate contextual greeting
  let greeting = card.aiPersonality.voiceIntro;
  if (timeOfDay === 'morning') {
    greeting = `Good morning! ${greeting}`;
  } else if (timeOfDay === 'evening') {
    greeting = `Good evening! ${greeting}`;
  }
  
  // Add location-based content
  if (userLocation && userLocation !== card.location) {
    greeting += ` I see you're connecting from ${userLocation} - I'd love to learn more about your local tech scene!`;
  }
  
  // Industry-specific recommendations
  const relevantProjects = card.currentProjects.filter(project => 
    userIndustry && project.toLowerCase().includes(userIndustry.toLowerCase())
  );
  
  return {
    ...card,
    dynamicGreeting: greeting,
    relevantProjects,
    contextualActions: card.availableActions.slice(0, 3) // Show top 3 most relevant actions
  };
};
