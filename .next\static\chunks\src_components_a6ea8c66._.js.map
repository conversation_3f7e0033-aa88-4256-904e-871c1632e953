{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/effects/TypingText.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst TypingText = ({\n  texts = ['Your Name. Reinvented.'],\n  speed = 100,\n  deleteSpeed = 50,\n  pauseTime = 2000,\n  loop = true,\n  className = '',\n  cursorClassName = '',\n  showCursor = true,\n  onComplete = null\n}) => {\n  const [currentTextIndex, setCurrentTextIndex] = useState(0);\n  const [currentText, setCurrentText] = useState('');\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [isPaused, setIsPaused] = useState(false);\n\n  useEffect(() => {\n    if (texts.length === 0) return;\n\n    const currentFullText = texts[currentTextIndex];\n    \n    const timeout = setTimeout(() => {\n      if (isPaused) {\n        setIsPaused(false);\n        if (loop || currentTextIndex < texts.length - 1) {\n          setIsDeleting(true);\n        }\n        return;\n      }\n\n      if (isDeleting) {\n        // Deleting characters\n        if (currentText.length > 0) {\n          setCurrentText(currentText.slice(0, -1));\n        } else {\n          // Finished deleting, move to next text\n          setIsDeleting(false);\n          setCurrentTextIndex((prev) => (prev + 1) % texts.length);\n        }\n      } else {\n        // Typing characters\n        if (currentText.length < currentFullText.length) {\n          setCurrentText(currentFullText.slice(0, currentText.length + 1));\n        } else {\n          // Finished typing current text\n          if (texts.length === 1 && !loop) {\n            // Single text, no loop - we're done\n            if (onComplete) onComplete();\n            return;\n          }\n          // Pause before deleting (if looping) or moving to next\n          setIsPaused(true);\n        }\n      }\n    }, isPaused ? pauseTime : isDeleting ? deleteSpeed : speed);\n\n    return () => clearTimeout(timeout);\n  }, [\n    currentText, \n    currentTextIndex, \n    isDeleting, \n    isPaused, \n    texts, \n    speed, \n    deleteSpeed, \n    pauseTime, \n    loop, \n    onComplete\n  ]);\n\n  const Cursor = () => (\n    <span \n      className={cn(\n        'inline-block w-0.5 bg-neon-blue animate-pulse ml-1',\n        cursorClassName\n      )}\n      style={{ \n        animation: 'blink 1s infinite',\n        height: '1em'\n      }}\n    />\n  );\n\n  return (\n    <span className={cn('inline-block', className)}>\n      {currentText}\n      {showCursor && <Cursor />}\n      \n      <style jsx>{`\n        @keyframes blink {\n          0%, 50% { opacity: 1; }\n          51%, 100% { opacity: 0; }\n        }\n      `}</style>\n    </span>\n  );\n};\n\n// Preset configurations for common use cases\nexport const TypingTextPresets = {\n  hero: {\n    speed: 80,\n    deleteSpeed: 40,\n    pauseTime: 3000,\n    className: 'text-4xl md:text-6xl font-bold bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent',\n    cursorClassName: 'bg-neon-blue h-12 md:h-16'\n  },\n  \n  subtitle: {\n    speed: 60,\n    deleteSpeed: 30,\n    pauseTime: 2000,\n    className: 'text-xl md:text-2xl text-text-secondary',\n    cursorClassName: 'bg-electric-purple h-6 md:h-8'\n  },\n  \n  feature: {\n    speed: 50,\n    deleteSpeed: 25,\n    pauseTime: 1500,\n    className: 'text-lg text-text-primary',\n    cursorClassName: 'bg-cyber-green h-5'\n  },\n  \n  code: {\n    speed: 30,\n    deleteSpeed: 15,\n    pauseTime: 1000,\n    className: 'font-mono text-matrix-green',\n    cursorClassName: 'bg-matrix-green h-4'\n  }\n};\n\n// Enhanced typing text with preset support\nexport const EnhancedTypingText = ({ preset, ...props }) => {\n  const presetConfig = preset ? TypingTextPresets[preset] : {};\n  const mergedProps = { ...presetConfig, ...props };\n  \n  return <TypingText {...mergedProps} />;\n};\n\nexport default TypingText;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;AAHA;;;;AAKA,MAAM,aAAa,CAAC,EAClB,QAAQ;IAAC;CAAyB,EAClC,QAAQ,GAAG,EACX,cAAc,EAAE,EAChB,YAAY,IAAI,EAChB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,aAAa,IAAI,EACjB,aAAa,IAAI,EAClB;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,MAAM,MAAM,KAAK,GAAG;YAExB,MAAM,kBAAkB,KAAK,CAAC,iBAAiB;YAE/C,MAAM,UAAU;gDAAW;oBACzB,IAAI,UAAU;wBACZ,YAAY;wBACZ,IAAI,QAAQ,mBAAmB,MAAM,MAAM,GAAG,GAAG;4BAC/C,cAAc;wBAChB;wBACA;oBACF;oBAEA,IAAI,YAAY;wBACd,sBAAsB;wBACtB,IAAI,YAAY,MAAM,GAAG,GAAG;4BAC1B,eAAe,YAAY,KAAK,CAAC,GAAG,CAAC;wBACvC,OAAO;4BACL,uCAAuC;4BACvC,cAAc;4BACd;gEAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;wBACzD;oBACF,OAAO;wBACL,oBAAoB;wBACpB,IAAI,YAAY,MAAM,GAAG,gBAAgB,MAAM,EAAE;4BAC/C,eAAe,gBAAgB,KAAK,CAAC,GAAG,YAAY,MAAM,GAAG;wBAC/D,OAAO;4BACL,+BAA+B;4BAC/B,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM;gCAC/B,oCAAoC;gCACpC,IAAI,YAAY;gCAChB;4BACF;4BACA,uDAAuD;4BACvD,YAAY;wBACd;oBACF;gBACF;+CAAG,WAAW,YAAY,aAAa,cAAc;YAErD;wCAAO,IAAM,aAAa;;QAC5B;+BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,SAAS,kBACb,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;YAEF,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;;;;;;IAIJ,qBACE,6LAAC;mDAAgB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;;YACjC;YACA,4BAAc,6LAAC;;;;;;;;;;;;;;;;;AAUtB;GAhGM;KAAA;AAmGC,MAAM,oBAAoB;IAC/B,MAAM;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,UAAU;QACR,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,SAAS;QACP,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,MAAM;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;AACF;AAGO,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO;IACrD,MAAM,eAAe,SAAS,iBAAiB,CAAC,OAAO,GAAG,CAAC;IAC3D,MAAM,cAAc;QAAE,GAAG,YAAY;QAAE,GAAG,KAAK;IAAC;IAEhD,qBAAO,6LAAC;QAAY,GAAG,WAAW;;;;;;AACpC;MALa;uCAOE", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/effects/MatrixEffect.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\nconst MatrixEffect = ({ \n  className = '',\n  density = 0.8,\n  speed = 50,\n  color = '#00ff41',\n  fontSize = 14,\n  opacity = 0.8\n}) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    \n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = canvas.offsetWidth;\n      canvas.height = canvas.offsetHeight;\n    };\n    \n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Matrix characters (mix of katakana, numbers, and symbols)\n    const chars = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+-=[]{}|;:,.<>?';\n    const charArray = chars.split('');\n\n    // Calculate columns\n    const columns = Math.floor(canvas.width / fontSize);\n    const drops = new Array(columns).fill(1);\n\n    // Animation function\n    const animate = () => {\n      // Semi-transparent black background for trail effect\n      ctx.fillStyle = `rgba(10, 10, 15, ${1 - opacity})`;\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Set text properties\n      ctx.fillStyle = color;\n      ctx.font = `${fontSize}px 'JetBrains Mono', monospace`;\n      ctx.textAlign = 'center';\n\n      // Draw characters\n      for (let i = 0; i < drops.length; i++) {\n        // Random character\n        const char = charArray[Math.floor(Math.random() * charArray.length)];\n        \n        // Calculate position\n        const x = i * fontSize + fontSize / 2;\n        const y = drops[i] * fontSize;\n\n        // Add glow effect\n        ctx.shadowColor = color;\n        ctx.shadowBlur = 10;\n        \n        // Draw character\n        ctx.fillText(char, x, y);\n        \n        // Reset shadow\n        ctx.shadowBlur = 0;\n\n        // Reset drop to top randomly or when it reaches bottom\n        if (y > canvas.height && Math.random() > density) {\n          drops[i] = 0;\n        }\n\n        // Move drop down\n        drops[i]++;\n      }\n\n      // Continue animation\n      animationRef.current = setTimeout(animate, speed);\n    };\n\n    // Start animation\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        clearTimeout(animationRef.current);\n      }\n    };\n  }, [density, speed, color, fontSize, opacity]);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className={`absolute inset-0 w-full h-full pointer-events-none ${className}`}\n      style={{ zIndex: -1 }}\n    />\n  );\n};\n\nexport default MatrixEffect;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,eAAe,CAAC,EACpB,YAAY,EAAE,EACd,UAAU,GAAG,EACb,QAAQ,EAAE,EACV,QAAQ,SAAS,EACjB,WAAW,EAAE,EACb,UAAU,GAAG,EACd;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAE9B,kBAAkB;YAClB,MAAM;uDAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,WAAW;oBACjC,OAAO,MAAM,GAAG,OAAO,YAAY;gBACrC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC,4DAA4D;YAC5D,MAAM,QAAQ;YACd,MAAM,YAAY,MAAM,KAAK,CAAC;YAE9B,oBAAoB;YACpB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG;YAC1C,MAAM,QAAQ,IAAI,MAAM,SAAS,IAAI,CAAC;YAEtC,qBAAqB;YACrB,MAAM;kDAAU;oBACd,qDAAqD;oBACrD,IAAI,SAAS,GAAG,CAAC,iBAAiB,EAAE,IAAI,QAAQ,CAAC,CAAC;oBAClD,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE9C,sBAAsB;oBACtB,IAAI,SAAS,GAAG;oBAChB,IAAI,IAAI,GAAG,GAAG,SAAS,8BAA8B,CAAC;oBACtD,IAAI,SAAS,GAAG;oBAEhB,kBAAkB;oBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;wBACrC,mBAAmB;wBACnB,MAAM,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;wBAEpE,qBAAqB;wBACrB,MAAM,IAAI,IAAI,WAAW,WAAW;wBACpC,MAAM,IAAI,KAAK,CAAC,EAAE,GAAG;wBAErB,kBAAkB;wBAClB,IAAI,WAAW,GAAG;wBAClB,IAAI,UAAU,GAAG;wBAEjB,iBAAiB;wBACjB,IAAI,QAAQ,CAAC,MAAM,GAAG;wBAEtB,eAAe;wBACf,IAAI,UAAU,GAAG;wBAEjB,uDAAuD;wBACvD,IAAI,IAAI,OAAO,MAAM,IAAI,KAAK,MAAM,KAAK,SAAS;4BAChD,KAAK,CAAC,EAAE,GAAG;wBACb;wBAEA,iBAAiB;wBACjB,KAAK,CAAC,EAAE;oBACV;oBAEA,qBAAqB;oBACrB,aAAa,OAAO,GAAG,WAAW,SAAS;gBAC7C;;YAEA,kBAAkB;YAClB;YAEA,UAAU;YACV;0CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,aAAa,OAAO,EAAE;wBACxB,aAAa,aAAa,OAAO;oBACnC;gBACF;;QACF;iCAAG;QAAC;QAAS;QAAO;QAAO;QAAU;KAAQ;IAE7C,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,mDAAmD,EAAE,WAAW;QAC5E,OAAO;YAAE,QAAQ,CAAC;QAAE;;;;;;AAG1B;GAhGM;KAAA;uCAkGS", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport Button from '@/components/ui/Button';\nimport { EnhancedTypingText } from '@/components/effects/TypingText';\nimport MatrixEffect from '@/components/effects/MatrixEffect';\n\nconst HeroSection = () => {\n  const heroRef = useRef(null);\n  const floatingElementsRef = useRef([]);\n\n  // Floating elements animation\n  useEffect(() => {\n    const elements = floatingElementsRef.current;\n    \n    elements.forEach((element, index) => {\n      if (element) {\n        const delay = index * 0.5;\n        const duration = 3 + Math.random() * 2;\n        \n        element.style.animationDelay = `${delay}s`;\n        element.style.animationDuration = `${duration}s`;\n      }\n    });\n  }, []);\n\n  const heroTexts = [\n    \"Your Name. Reinvented.\",\n    \"Not Just a Card—An Experience.\",\n    \"Connect in 3D. Remember Forever.\",\n    \"The Future of Networking Is Here.\"\n  ];\n\n  const FloatingCard = ({ index, className = '' }) => (\n    <div\n      ref={el => floatingElementsRef.current[index] = el}\n      className={`absolute opacity-20 animate-float ${className}`}\n      style={{\n        animationName: 'float',\n        animationTimingFunction: 'ease-in-out',\n        animationIterationCount: 'infinite',\n        animationDirection: 'alternate'\n      }}\n    >\n      <div className=\"w-16 h-10 bg-gradient-to-r from-neon-blue to-electric-purple rounded-lg shadow-lg shadow-neon-blue/25 transform rotate-12\" />\n    </div>\n  );\n\n  const StatsCard = ({ number, label, delay = 0 }) => (\n    <div \n      className=\"text-center group\"\n      style={{ animationDelay: `${delay}s` }}\n    >\n      <div className=\"text-2xl md:text-3xl font-bold font-display text-neon-blue mb-1 group-hover:scale-110 transition-transform duration-300\">\n        {number}\n      </div>\n      <div className=\"text-sm text-text-secondary\">{label}</div>\n    </div>\n  );\n\n  return (\n    <section \n      ref={heroRef}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-space via-deep-purple to-midnight-blue\"\n    >\n      {/* Matrix Background Effect */}\n      <MatrixEffect \n        className=\"absolute inset-0\" \n        density={0.6}\n        speed={80}\n        opacity={0.3}\n      />\n      \n      {/* Floating 3D Elements */}\n      <FloatingCard index={0} className=\"top-20 left-10\" />\n      <FloatingCard index={1} className=\"top-40 right-20\" />\n      <FloatingCard index={2} className=\"bottom-40 left-20\" />\n      <FloatingCard index={3} className=\"bottom-20 right-10\" />\n      <FloatingCard index={4} className=\"top-60 left-1/3\" />\n      <FloatingCard index={5} className=\"bottom-60 right-1/3\" />\n\n      {/* Main Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"space-y-8\">\n          {/* Main Headline */}\n          <div className=\"space-y-4\">\n            <h1 className=\"text-5xl md:text-7xl lg:text-8xl font-bold font-display leading-tight\">\n              <EnhancedTypingText\n                preset=\"hero\"\n                texts={heroTexts}\n                speed={100}\n                deleteSpeed={50}\n                pauseTime={3000}\n                loop={true}\n              />\n            </h1>\n            \n            <p className=\"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed\">\n              AR-enhanced digital business cards that let you share stunning, interactive profiles via \n              <span className=\"text-neon-blue font-semibold\"> QR, NFC, facial recognition, or camera scan</span>\n              —no app required.\n            </p>\n          </div>\n\n          {/* Mini Demo Preview */}\n          <div className=\"relative max-w-md mx-auto\">\n            <div className=\"bg-surface-dark/80 backdrop-blur-md rounded-2xl p-6 border border-electric-purple/30 hover:border-neon-blue/50 transition-all duration-300 group\">\n              <div className=\"relative\">\n                {/* Simulated 3D Card */}\n                <div className=\"w-full h-32 bg-gradient-to-br from-neon-blue via-electric-purple to-hologram-pink rounded-xl shadow-2xl shadow-neon-blue/30 transform group-hover:scale-105 group-hover:rotate-3 transition-all duration-500\">\n                  <div className=\"absolute inset-0 bg-white/10 rounded-xl backdrop-blur-sm\">\n                    <div className=\"p-4 h-full flex flex-col justify-between\">\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-8 h-8 bg-white/20 rounded-full\"></div>\n                        <div>\n                          <div className=\"w-16 h-2 bg-white/30 rounded mb-1\"></div>\n                          <div className=\"w-12 h-1.5 bg-white/20 rounded\"></div>\n                        </div>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <div className=\"w-20 h-1.5 bg-white/30 rounded\"></div>\n                        <div className=\"w-16 h-1.5 bg-white/20 rounded\"></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Floating particles */}\n                <div className=\"absolute -top-2 -right-2 w-2 h-2 bg-neon-blue rounded-full animate-ping\"></div>\n                <div className=\"absolute -bottom-2 -left-2 w-1.5 h-1.5 bg-electric-purple rounded-full animate-pulse\"></div>\n              </div>\n              \n              <p className=\"text-sm text-text-secondary mt-4 group-hover:text-neon-blue transition-colors duration-300\">\n                ✨ Live 3D Card Preview\n              </p>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Link href=\"/demo\">\n              <Button \n                variant=\"primary\" \n                size=\"lg\"\n                className=\"text-lg font-semibold px-8 py-4 shadow-2xl shadow-neon-blue/30 hover:shadow-electric-purple/40\"\n              >\n                Try Live Demo\n              </Button>\n            </Link>\n            \n            <Link href=\"/pitch\">\n              <Button \n                variant=\"outline\" \n                size=\"lg\"\n                className=\"text-lg font-semibold px-8 py-4\"\n              >\n                Watch Pitch\n              </Button>\n            </Link>\n          </div>\n\n          {/* Stats Row */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto pt-12 border-t border-electric-purple/20\">\n            <StatsCard number=\"88%\" label=\"Cards Thrown Away\" delay={0.2} />\n            <StatsCard number=\"7B+\" label=\"Cards Printed Yearly\" delay={0.4} />\n            <StatsCard number=\"70%\" label=\"Better Retention\" delay={0.6} />\n            <StatsCard number=\"0\" label=\"App Downloads\" delay={0.8} />\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-neon-blue rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-neon-blue rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n\n      {/* CSS for floating animation */}\n      <style jsx>{`\n        @keyframes float {\n          0% { transform: translateY(0px) rotate(12deg); }\n          100% { transform: translateY(-20px) rotate(15deg); }\n        }\n        \n        .animate-float {\n          animation: float 3s ease-in-out infinite alternate;\n        }\n      `}</style>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAQA,MAAM,cAAc;;IAClB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAErC,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW,oBAAoB,OAAO;YAE5C,SAAS,OAAO;yCAAC,CAAC,SAAS;oBACzB,IAAI,SAAS;wBACX,MAAM,QAAQ,QAAQ;wBACtB,MAAM,WAAW,IAAI,KAAK,MAAM,KAAK;wBAErC,QAAQ,KAAK,CAAC,cAAc,GAAG,GAAG,MAAM,CAAC,CAAC;wBAC1C,QAAQ,KAAK,CAAC,iBAAiB,GAAG,GAAG,SAAS,CAAC,CAAC;oBAClD;gBACF;;QACF;gCAAG,EAAE;IAEL,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE,iBAC7C,6LAAC;YACC,KAAK,CAAA,KAAM,oBAAoB,OAAO,CAAC,MAAM,GAAG;YAChD,WAAW,CAAC,kCAAkC,EAAE,WAAW;YAC3D,OAAO;gBACL,eAAe;gBACf,yBAAyB;gBACzB,yBAAyB;gBACzB,oBAAoB;YACtB;sBAEA,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAInB,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,iBAC7C,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE,gBAAgB,GAAG,MAAM,CAAC,CAAC;YAAC;;8BAErC,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,6LAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;IAIlD,qBACE,6LAAC;QACC,KAAK;kDACK;;0BAGV,6LAAC,+IAAA,CAAA,UAAY;gBACX,WAAU;gBACV,SAAS;gBACT,OAAO;gBACP,SAAS;;;;;;0BAIX,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAClC,6LAAC;gBAAa,OAAO;0DAAa;;;;;;0BAGlC,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAc;;sCAEb,6LAAC;sEAAc;;8CACb,6LAAC;8EAAa;8CACZ,cAAA,6LAAC,6IAAA,CAAA,qBAAkB;wCACjB,QAAO;wCACP,OAAO;wCACP,OAAO;wCACP,aAAa;wCACb,WAAW;wCACX,MAAM;;;;;;;;;;;8CAIV,6LAAC;8EAAY;;wCAA4E;sDAEvF,6LAAC;sFAAe;sDAA+B;;;;;;wCAAmD;;;;;;;;;;;;;sCAMtG,6LAAC;sEAAc;sCACb,cAAA,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;;0DAEb,6LAAC;0FAAc;0DACb,cAAA,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAc;;0EACb,6LAAC;0GAAc;;kFACb,6LAAC;kHAAc;;;;;;kFACf,6LAAC;;;0FACC,6LAAC;0HAAc;;;;;;0FACf,6LAAC;0HAAc;;;;;;;;;;;;;;;;;;0EAGnB,6LAAC;0GAAc;;kFACb,6LAAC;kHAAc;;;;;;kFACf,6LAAC;kHAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOvB,6LAAC;0FAAc;;;;;;0DACf,6LAAC;0FAAc;;;;;;;;;;;;kDAGjB,6LAAC;kFAAY;kDAA6F;;;;;;;;;;;;;;;;;sCAO9G,6LAAC;sEAAc;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,oIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAKH,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,oIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,6LAAC;sEAAc;;8CACb,6LAAC;oCAAU,QAAO;oCAAM,OAAM;oCAAoB,OAAO;;;;;;;8CACzD,6LAAC;oCAAU,QAAO;oCAAM,OAAM;oCAAuB,OAAO;;;;;;;8CAC5D,6LAAC;oCAAU,QAAO;oCAAM,OAAM;oCAAmB,OAAO;;;;;;;8CACxD,6LAAC;oCAAU,QAAO;oCAAI,OAAM;oCAAgB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAc;8BACb,cAAA,6LAAC;kEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBzB;GAxLM;KAAA;uCA0LS", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/ui/Card.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = forwardRef(({ \n  className, \n  variant = 'default',\n  hover = true,\n  glow = false,\n  children, \n  ...props \n}, ref) => {\n  const baseStyles = `\n    relative rounded-xl border backdrop-blur-sm\n    transition-all duration-300 ease-out\n    overflow-hidden group\n  `;\n\n  const variants = {\n    default: `\n      bg-surface-dark/80 border-electric-purple/20\n      hover:border-neon-blue/40 hover:bg-surface-light/80\n    `,\n    glass: `\n      bg-white/5 border-white/10\n      hover:bg-white/10 hover:border-white/20\n      backdrop-blur-md\n    `,\n    neon: `\n      bg-dark-space/90 border-neon-blue/50\n      hover:border-neon-blue hover:bg-dark-space\n      shadow-lg shadow-neon-blue/10\n    `,\n    purple: `\n      bg-deep-purple/80 border-electric-purple/30\n      hover:border-electric-purple hover:bg-deep-purple\n      shadow-lg shadow-electric-purple/10\n    `,\n    gradient: `\n      bg-gradient-to-br from-surface-dark/80 to-deep-purple/80\n      border-gradient-to-r from-neon-blue/30 to-electric-purple/30\n      hover:from-surface-light/80 hover:to-midnight-blue/80\n    `\n  };\n\n  const hoverEffects = hover ? `\n    hover:scale-[1.02] hover:-translate-y-1\n    hover:shadow-2xl hover:shadow-neon-blue/20\n  ` : '';\n\n  const glowEffect = glow ? `\n    before:absolute before:inset-0 before:rounded-xl\n    before:bg-gradient-to-r before:from-neon-blue/20 before:to-electric-purple/20\n    before:opacity-0 before:transition-opacity before:duration-300\n    hover:before:opacity-100\n  ` : '';\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        baseStyles,\n        variants[variant],\n        hoverEffects,\n        glowEffect,\n        className\n      )}\n      {...props}\n    >\n      {/* Animated border gradient */}\n      <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-neon-blue via-electric-purple to-hologram-pink opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n      \n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n      \n      {/* Corner accent */}\n      <div className=\"absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-neon-blue/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n    </div>\n  );\n});\n\nCard.displayName = 'Card';\n\n// Card Header Component\nconst CardHeader = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('p-6 pb-4', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardHeader.displayName = 'CardHeader';\n\n// Card Title Component\nconst CardTitle = forwardRef(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-xl font-semibold text-text-primary mb-2',\n      'bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </h3>\n));\n\nCardTitle.displayName = 'CardTitle';\n\n// Card Description Component\nconst CardDescription = forwardRef(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-text-secondary leading-relaxed', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\n\nCardDescription.displayName = 'CardDescription';\n\n// Card Content Component\nconst CardContent = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('px-6 pb-6', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardContent.displayName = 'CardContent';\n\n// Card Footer Component\nconst CardFooter = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'px-6 py-4 border-t border-electric-purple/20',\n      'bg-gradient-to-r from-transparent via-electric-purple/5 to-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardFooter.displayName = 'CardFooter';\n\n// Export all components\nexport {\n  Card,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n  CardContent,\n  CardFooter\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACvB,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAC;;;;EAIpB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;IAGV,CAAC;QACD,OAAO,CAAC;;;;IAIR,CAAC;QACD,MAAM,CAAC;;;;IAIP,CAAC;QACD,QAAQ,CAAC;;;;IAIT,CAAC;QACD,UAAU,CAAC;;;;IAIX,CAAC;IACH;IAEA,MAAM,eAAe,QAAQ,CAAC;;;EAG9B,CAAC,GAAG;IAEJ,MAAM,aAAa,OAAO,CAAC;;;;;EAK3B,CAAC,GAAG;IAEJ,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,cACA,YACA;QAED,GAAG,KAAK;;0BAGT,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;;AAEA,KAAK,WAAW,GAAG;AAEnB,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG;AAEzB,uBAAuB;AACvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIL,UAAU,WAAW,GAAG;AAExB,6BAA6B;AAC7B,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACrE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;QACpD,GAAG,KAAK;kBAER;;;;;;;AAIL,gBAAgB,WAAW,GAAG;AAE9B,yBAAyB;AACzB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACjE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;kBAER;;;;;;;AAIL,YAAY,WAAW,GAAG;AAE1B,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,0EACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/ProblemSection.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect } from 'react';\nimport { Card, CardContent } from '@/components/ui/Card';\n\nconst ProblemSection = () => {\n  const sectionRef = useRef(null);\n  const cardsRef = useRef([]);\n\n  // 3D Tilt Effect\n  useEffect(() => {\n    const handleMouseMove = (e, card) => {\n      if (!card) return;\n      \n      const rect = card.getBoundingClientRect();\n      const x = e.clientX - rect.left;\n      const y = e.clientY - rect.top;\n      \n      const centerX = rect.width / 2;\n      const centerY = rect.height / 2;\n      \n      const rotateX = (y - centerY) / 10;\n      const rotateY = (centerX - x) / 10;\n      \n      card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;\n    };\n\n    const handleMouseLeave = (card) => {\n      if (!card) return;\n      card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';\n    };\n\n    cardsRef.current.forEach((card) => {\n      if (card) {\n        const mouseMoveHandler = (e) => handleMouseMove(e, card);\n        const mouseLeaveHandler = () => handleMouseLeave(card);\n        \n        card.addEventListener('mousemove', mouseMoveHandler);\n        card.addEventListener('mouseleave', mouseLeaveHandler);\n        \n        return () => {\n          card.removeEventListener('mousemove', mouseMoveHandler);\n          card.removeEventListener('mouseleave', mouseLeaveHandler);\n        };\n      }\n    });\n  }, []);\n\n  const problems = [\n    {\n      icon: '🗑️',\n      title: 'Paper Cards Get Lost',\n      description: '88% of business cards are thrown away within a week. Your investment literally goes in the trash.',\n      stat: '7B+ cards wasted yearly'\n    },\n    {\n      icon: '📱',\n      title: 'Tech Dependency',\n      description: 'QR codes need cameras, NFC needs compatible devices. What happens when tech fails?',\n      stat: '30% compatibility issues'\n    },\n    {\n      icon: '😴',\n      title: 'Boring & Forgettable',\n      description: 'Static cards blend into the noise. No engagement, no story, no lasting impression.',\n      stat: '95% forgotten instantly'\n    },\n    {\n      icon: '🔄',\n      title: 'Manual Effort Required',\n      description: 'Typing contact info, updating details, managing connections. Too much friction.',\n      stat: '5 minutes per contact'\n    }\n  ];\n\n  return (\n    <section \n      ref={sectionRef}\n      className=\"py-20 bg-gradient-to-b from-midnight-blue to-deep-purple relative overflow-hidden\"\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(0,245,255,0.1),transparent_50%)]\"></div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-hologram-pink to-quantum-gold bg-clip-text text-transparent\">\n              The Problem with Traditional Networking\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n            In a digital world, we're still stuck with analog solutions that waste time, money, and opportunities.\n          </p>\n        </div>\n\n        {/* Problem Cards Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {problems.map((problem, index) => (\n            <Card\n              key={index}\n              ref={el => cardsRef.current[index] = el}\n              variant=\"glass\"\n              className=\"h-full transition-all duration-300 cursor-pointer transform-gpu\"\n              style={{ transformStyle: 'preserve-3d' }}\n            >\n              <CardContent className=\"p-6 h-full flex flex-col\">\n                {/* Icon */}\n                <div className=\"text-4xl mb-4 text-center\">{problem.icon}</div>\n                \n                {/* Title */}\n                <h3 className=\"text-xl font-semibold text-text-primary mb-3 text-center\">\n                  {problem.title}\n                </h3>\n                \n                {/* Description */}\n                <p className=\"text-text-secondary text-center mb-4 flex-grow\">\n                  {problem.description}\n                </p>\n                \n                {/* Stat */}\n                <div className=\"text-center\">\n                  <span className=\"inline-block px-3 py-1 bg-hologram-pink/20 text-hologram-pink rounded-full text-sm font-medium\">\n                    {problem.stat}\n                  </span>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-lg text-text-secondary mb-6\">\n            It's time for a solution that works in the real world.\n          </p>\n          <div className=\"w-24 h-1 bg-gradient-to-r from-hologram-pink to-quantum-gold mx-auto rounded-full\"></div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ProblemSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,iBAAiB;;IACrB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE1B,iBAAiB;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;4DAAkB,CAAC,GAAG;oBAC1B,IAAI,CAAC,MAAM;oBAEX,MAAM,OAAO,KAAK,qBAAqB;oBACvC,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;oBAC/B,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG;oBAE9B,MAAM,UAAU,KAAK,KAAK,GAAG;oBAC7B,MAAM,UAAU,KAAK,MAAM,GAAG;oBAE9B,MAAM,UAAU,CAAC,IAAI,OAAO,IAAI;oBAChC,MAAM,UAAU,CAAC,UAAU,CAAC,IAAI;oBAEhC,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,4BAA4B,EAAE,QAAQ,aAAa,EAAE,QAAQ,8BAA8B,CAAC;gBACtH;;YAEA,MAAM;6DAAmB,CAAC;oBACxB,IAAI,CAAC,MAAM;oBACX,KAAK,KAAK,CAAC,SAAS,GAAG;gBACzB;;YAEA,SAAS,OAAO,CAAC,OAAO;4CAAC,CAAC;oBACxB,IAAI,MAAM;wBACR,MAAM;yEAAmB,CAAC,IAAM,gBAAgB,GAAG;;wBACnD,MAAM;0EAAoB,IAAM,iBAAiB;;wBAEjD,KAAK,gBAAgB,CAAC,aAAa;wBACnC,KAAK,gBAAgB,CAAC,cAAc;wBAEpC;wDAAO;gCACL,KAAK,mBAAmB,CAAC,aAAa;gCACtC,KAAK,mBAAmB,CAAC,cAAc;4BACzC;;oBACF;gBACF;;QACF;mCAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAK,WAAU;8CAAoF;;;;;;;;;;;0CAItG,6LAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAM/D,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,kIAAA,CAAA,OAAI;gCAEH,KAAK,CAAA,KAAM,SAAS,OAAO,CAAC,MAAM,GAAG;gCACrC,SAAQ;gCACR,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAc;0CAEvC,cAAA,6LAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,6LAAC;4CAAI,WAAU;sDAA6B,QAAQ,IAAI;;;;;;sDAGxD,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAIhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAItB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,QAAQ,IAAI;;;;;;;;;;;;;;;;;+BAvBd;;;;;;;;;;kCAgCX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAGhD,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzB;GA1IM;KAAA;uCA4IS", "debugId": null}}, {"offset": {"line": 1219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/SolutionSection.js"], "sourcesContent": ["'use client';\n\nimport { EnhancedTypingText } from '@/components/effects/TypingText';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst SolutionSection = () => {\n  const features = [\n    \"AR-powered digital business cards\",\n    \"Multiple sharing methods: QR, NFC, camera scan\",\n    \"Works without app downloads\",\n    \"Real-time 3D animations\",\n    \"Name/number recognition technology\"\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-deep-purple to-dark-space relative\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-cyber-green to-neon-blue bg-clip-text text-transparent\">\n              Our Solution\n            </span>\n          </h2>\n          \n          <div className=\"text-2xl md:text-3xl text-text-primary mb-8\">\n            <EnhancedTypingText\n              preset=\"subtitle\"\n              texts={features}\n              speed={60}\n              deleteSpeed={30}\n              pauseTime={2000}\n              loop={true}\n            />\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          <div>\n            <h3 className=\"text-3xl font-bold text-text-primary mb-6\">\n              The Future of Professional Identity\n            </h3>\n            <p className=\"text-lg text-text-secondary mb-8\">\n              NameCardAI combines cutting-edge AR technology with practical networking needs. \n              Share your professional identity through immersive 3D experiences that people actually remember.\n            </p>\n            \n            <div className=\"space-y-4 mb-8\">\n              {[\n                \"✨ AR-enhanced visual storytelling\",\n                \"🔄 Multiple sharing methods\",\n                \"📱 No app downloads required\",\n                \"🎯 Instant recognition technology\"\n              ].map((feature, index) => (\n                <div key={index} className=\"flex items-center space-x-3\">\n                  <span className=\"text-cyber-green\">{feature}</span>\n                </div>\n              ))}\n            </div>\n\n            <Button variant=\"primary\" size=\"lg\">\n              See How It Works\n            </Button>\n          </div>\n\n          <div className=\"relative\">\n            <Card variant=\"neon\" className=\"p-8\">\n              <CardContent>\n                <div className=\"aspect-video bg-gradient-to-br from-neon-blue/20 to-electric-purple/20 rounded-lg flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl mb-4\">🚀</div>\n                    <p className=\"text-text-secondary\">Interactive Demo Coming Soon</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default SolutionSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAA+E;;;;;;;;;;;sCAKjG,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6IAAA,CAAA,qBAAkB;gCACjB,QAAO;gCACP,OAAO;gCACP,OAAO;gCACP,aAAa;gCACb,WAAW;gCACX,MAAM;;;;;;;;;;;;;;;;;8BAKZ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAKhD,6LAAC;oCAAI,WAAU;8CACZ;wCACC;wCACA;wCACA;wCACA;qCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;4CAAgB,WAAU;sDACzB,cAAA,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;2CAD5B;;;;;;;;;;8CAMd,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;sCAKtC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,kIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAO,WAAU;0CAC7B,cAAA,6LAAC,kIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAE,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvD;KA3EM;uCA6ES", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/ProcessSection.js"], "sourcesContent": ["'use client';\n\nconst ProcessSection = () => {\n  const steps = [\n    {\n      number: \"01\",\n      title: \"Create Your Card\",\n      description: \"Design your AR-enhanced digital business card with our intuitive editor\",\n      icon: \"🎨\"\n    },\n    {\n      number: \"02\", \n      title: \"Share Instantly\",\n      description: \"Share via QR, NFC, camera scan, or just your name - no app required\",\n      icon: \"📤\"\n    },\n    {\n      number: \"03\",\n      title: \"Make Impact\",\n      description: \"Recipients see your 3D animated card with immersive AR experience\",\n      icon: \"✨\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-dark-space to-midnight-blue\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-quantum-gold to-hologram-pink bg-clip-text text-transparent\">\n              How It Works\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary\">\n            Three simple steps to revolutionize your networking\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {steps.map((step, index) => (\n            <div key={index} className=\"text-center group\">\n              <div className=\"relative mb-8\">\n                <div className=\"w-20 h-20 bg-gradient-to-r from-neon-blue to-electric-purple rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <span className=\"text-2xl\">{step.icon}</span>\n                </div>\n                <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-quantum-gold rounded-full flex items-center justify-center text-dark-space font-bold text-sm\">\n                  {step.number}\n                </div>\n              </div>\n              \n              <h3 className=\"text-xl font-semibold text-text-primary mb-4\">\n                {step.title}\n              </h3>\n              \n              <p className=\"text-text-secondary\">\n                {step.description}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ProcessSection;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,iBAAiB;IACrB,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAAoF;;;;;;;;;;;sCAItG,6LAAC;4BAAE,WAAU;sCAA8B;;;;;;;;;;;;8BAK7C,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAY,KAAK,IAAI;;;;;;;;;;;sDAEvC,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;;;;;;;8CAIhB,6LAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAGb,6LAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;2BAfX;;;;;;;;;;;;;;;;;;;;;AAuBtB;KA7DM;uCA+DS", "debugId": null}}, {"offset": {"line": 1587, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/sections/FeaturesSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst FeaturesSection = () => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n  const intervalRef = useRef(null);\n\n  const features = [\n    {\n      category: \"AR Technology\",\n      title: \"3D Card Rendering\",\n      description: \"Real-time 3D business cards with immersive animations and interactive elements\",\n      icon: \"🎯\",\n      demo: \"Live 3D Preview\",\n      benefits: [\"WebGL powered\", \"60fps animations\", \"Cross-platform\"]\n    },\n    {\n      category: \"Sharing Methods\",\n      title: \"Multi-Modal Access\",\n      description: \"Share via QR codes, NFC tags, camera scan, or just remembering a name\",\n      icon: \"📱\",\n      demo: \"Scan Simulation\",\n      benefits: [\"No app required\", \"Universal compatibility\", \"Instant access\"]\n    },\n    {\n      category: \"Recognition\",\n      title: \"Smart Detection\",\n      description: \"AI-powered face and name recognition for seamless card discovery\",\n      icon: \"🔍\",\n      demo: \"Recognition Demo\",\n      benefits: [\"Face matching\", \"Name search\", \"Auto-suggestions\"]\n    },\n    {\n      category: \"Customization\",\n      title: \"Visual Effects\",\n      description: \"Choose from preset animations or create custom AR experiences\",\n      icon: \"✨\",\n      demo: \"Effect Gallery\",\n      benefits: [\"Preset themes\", \"Custom animations\", \"Brand integration\"]\n    },\n    {\n      category: \"Analytics\",\n      title: \"Engagement Tracking\",\n      description: \"Track views, interactions, and networking effectiveness\",\n      icon: \"📊\",\n      demo: \"Analytics Dashboard\",\n      benefits: [\"View metrics\", \"Interaction data\", \"ROI tracking\"]\n    }\n  ];\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (isAutoPlaying) {\n      intervalRef.current = setInterval(() => {\n        setCurrentSlide((prev) => (prev + 1) % features.length);\n      }, 4000);\n    }\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [isAutoPlaying, features.length]);\n\n  const handleSlideChange = (index) => {\n    setCurrentSlide(index);\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume auto-play after 10s\n  };\n\n  const FeatureCard = ({ feature, index, isActive }) => (\n    <div\n      className={`transition-all duration-500 ${\n        isActive\n          ? 'scale-100 opacity-100 z-10'\n          : 'scale-95 opacity-60 z-0'\n      }`}\n      style={{\n        transform: `translateX(${(index - currentSlide) * 100}%)`,\n      }}\n    >\n      <Card\n        variant=\"glass\"\n        className=\"h-full min-h-[400px] relative overflow-hidden\"\n        hover={isActive}\n        glow={isActive}\n      >\n        <CardHeader className=\"text-center\">\n          <div className=\"text-6xl mb-4\">{feature.icon}</div>\n          <div className=\"text-sm text-neon-blue font-semibold mb-2\">\n            {feature.category}\n          </div>\n          <CardTitle className=\"text-2xl mb-4\">\n            {feature.title}\n          </CardTitle>\n        </CardHeader>\n\n        <CardContent className=\"space-y-6\">\n          <p className=\"text-text-secondary text-center leading-relaxed\">\n            {feature.description}\n          </p>\n\n          {/* Demo Preview */}\n          <div className=\"bg-dark-space/50 rounded-lg p-4 text-center\">\n            <div className=\"text-sm text-electric-purple mb-2\">\n              {feature.demo}\n            </div>\n            <div className=\"w-full h-24 bg-gradient-to-r from-neon-blue/20 to-electric-purple/20 rounded-lg flex items-center justify-center\">\n              <div className=\"animate-pulse text-text-secondary\">\n                Interactive Demo\n              </div>\n            </div>\n          </div>\n\n          {/* Benefits */}\n          <div className=\"space-y-2\">\n            {feature.benefits.map((benefit, idx) => (\n              <div key={idx} className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-cyber-green rounded-full\"></div>\n                <span className=\"text-sm text-text-secondary\">{benefit}</span>\n              </div>\n            ))}\n          </div>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"w-full\"\n          >\n            Try {feature.title}\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  );\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-midnight-blue to-deep-purple relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_70%,rgba(139,92,246,0.1),transparent_50%)]\"></div>\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(0,245,255,0.1),transparent_50%)]\"></div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent\">\n              MVP Features\n            </span>\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n            Experience the future of networking with our cutting-edge AR technology and intelligent features\n          </p>\n        </div>\n\n        {/* Carousel Container */}\n        <div className=\"relative\">\n          {/* Feature Cards */}\n          <div className=\"relative h-[500px] overflow-hidden rounded-2xl\">\n            <div className=\"flex transition-transform duration-500 ease-in-out h-full\">\n              {features.map((feature, index) => (\n                <div key={index} className=\"w-full flex-shrink-0 px-4\">\n                  <FeatureCard\n                    feature={feature}\n                    index={index}\n                    isActive={index === currentSlide}\n                  />\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Navigation Dots */}\n          <div className=\"flex justify-center space-x-3 mt-8\">\n            {features.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => handleSlideChange(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                  index === currentSlide\n                    ? 'bg-neon-blue scale-125'\n                    : 'bg-text-secondary/30 hover:bg-text-secondary/60'\n                }`}\n              />\n            ))}\n          </div>\n\n          {/* Navigation Arrows */}\n          <button\n            onClick={() => handleSlideChange((currentSlide - 1 + features.length) % features.length)}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-surface-dark/80 backdrop-blur-sm border border-electric-purple/30 rounded-full flex items-center justify-center text-text-primary hover:bg-surface-light/80 hover:border-neon-blue/50 transition-all duration-300\"\n          >\n            ←\n          </button>\n\n          <button\n            onClick={() => handleSlideChange((currentSlide + 1) % features.length)}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-surface-dark/80 backdrop-blur-sm border border-electric-purple/30 rounded-full flex items-center justify-center text-text-primary hover:bg-surface-light/80 hover:border-neon-blue/50 transition-all duration-300\"\n          >\n            →\n          </button>\n        </div>\n\n        {/* Feature Grid Preview */}\n        <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4 mt-16\">\n          {features.map((feature, index) => (\n            <button\n              key={index}\n              onClick={() => handleSlideChange(index)}\n              className={`p-4 rounded-lg border transition-all duration-300 ${\n                index === currentSlide\n                  ? 'border-neon-blue bg-neon-blue/10'\n                  : 'border-electric-purple/30 bg-surface-dark/50 hover:border-electric-purple/60'\n              }`}\n            >\n              <div className=\"text-2xl mb-2\">{feature.icon}</div>\n              <div className=\"text-sm font-medium text-text-primary\">\n                {feature.title}\n              </div>\n            </button>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,kBAAkB;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,MAAM,WAAW;QACf;YACE,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,UAAU;gBAAC;gBAAiB;gBAAoB;aAAiB;QACnE;QACA;YACE,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,UAAU;gBAAC;gBAAmB;gBAA2B;aAAiB;QAC5E;QACA;YACE,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,UAAU;gBAAC;gBAAiB;gBAAe;aAAmB;QAChE;QACA;YACE,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,UAAU;gBAAC;gBAAiB;gBAAqB;aAAoB;QACvE;QACA;YACE,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,UAAU;gBAAC;gBAAgB;gBAAoB;aAAe;QAChE;KACD;IAED,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,eAAe;gBACjB,YAAY,OAAO,GAAG;iDAAY;wBAChC;yDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,SAAS,MAAM;;oBACxD;gDAAG;YACL;YAEA;6CAAO;oBACL,IAAI,YAAY,OAAO,EAAE;wBACvB,cAAc,YAAY,OAAO;oBACnC;gBACF;;QACF;oCAAG;QAAC;QAAe,SAAS,MAAM;KAAC;IAEnC,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO,QAAQ,6BAA6B;IAChF;IAEA,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,iBAC/C,6LAAC;YACC,WAAW,CAAC,4BAA4B,EACtC,WACI,+BACA,2BACJ;YACF,OAAO;gBACL,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,YAAY,IAAI,IAAI,EAAE,CAAC;YAC3D;sBAEA,cAAA,6LAAC,kIAAA,CAAA,OAAI;gBACH,SAAQ;gBACR,WAAU;gBACV,OAAO;gBACP,MAAM;;kCAEN,6LAAC,kIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCAAI,WAAU;0CAAiB,QAAQ,IAAI;;;;;;0CAC5C,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,QAAQ;;;;;;0CAEnB,6LAAC,kIAAA,CAAA,YAAS;gCAAC,WAAU;0CAClB,QAAQ,KAAK;;;;;;;;;;;;kCAIlB,6LAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;0CAItB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,IAAI;;;;;;kDAEf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAOvD,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC9B,6LAAC;wCAAc,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;uCAFvC;;;;;;;;;;0CAOd,6LAAC,oIAAA,CAAA,UAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;oCACX;oCACM,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;IAO5B,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAK,WAAU;8CAAmF;;;;;;;;;;;0CAIrG,6LAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAM/D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4CAAgB,WAAU;sDACzB,cAAA,6LAAC;gDACC,SAAS;gDACT,OAAO;gDACP,UAAU,UAAU;;;;;;2CAJd;;;;;;;;;;;;;;;0CAYhB,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,GAAG,sBAChB,6LAAC;wCAEC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,2BACA,mDACJ;uCANG;;;;;;;;;;0CAYX,6LAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC,eAAe,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM;gCACvF,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC,eAAe,CAAC,IAAI,SAAS,MAAM;gCACrE,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gCAEC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,kDAAkD,EAC5D,UAAU,eACN,qCACA,gFACJ;;kDAEF,6LAAC;wCAAI,WAAU;kDAAiB,QAAQ,IAAI;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,KAAK;;;;;;;+BAVX;;;;;;;;;;;;;;;;;;;;;;AAkBnB;GAlOM;KAAA;uCAoOS", "debugId": null}}]}