{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/components/ui/Card.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = forwardRef(({ \n  className, \n  variant = 'default',\n  hover = true,\n  glow = false,\n  children, \n  ...props \n}, ref) => {\n  const baseStyles = `\n    relative rounded-xl border backdrop-blur-sm\n    transition-all duration-300 ease-out\n    overflow-hidden group\n  `;\n\n  const variants = {\n    default: `\n      bg-surface-dark/80 border-electric-purple/20\n      hover:border-neon-blue/40 hover:bg-surface-light/80\n    `,\n    glass: `\n      bg-white/5 border-white/10\n      hover:bg-white/10 hover:border-white/20\n      backdrop-blur-md\n    `,\n    neon: `\n      bg-dark-space/90 border-neon-blue/50\n      hover:border-neon-blue hover:bg-dark-space\n      shadow-lg shadow-neon-blue/10\n    `,\n    purple: `\n      bg-deep-purple/80 border-electric-purple/30\n      hover:border-electric-purple hover:bg-deep-purple\n      shadow-lg shadow-electric-purple/10\n    `,\n    gradient: `\n      bg-gradient-to-br from-surface-dark/80 to-deep-purple/80\n      border-gradient-to-r from-neon-blue/30 to-electric-purple/30\n      hover:from-surface-light/80 hover:to-midnight-blue/80\n    `\n  };\n\n  const hoverEffects = hover ? `\n    hover:scale-[1.02] hover:-translate-y-1\n    hover:shadow-2xl hover:shadow-neon-blue/20\n  ` : '';\n\n  const glowEffect = glow ? `\n    before:absolute before:inset-0 before:rounded-xl\n    before:bg-gradient-to-r before:from-neon-blue/20 before:to-electric-purple/20\n    before:opacity-0 before:transition-opacity before:duration-300\n    hover:before:opacity-100\n  ` : '';\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        baseStyles,\n        variants[variant],\n        hoverEffects,\n        glowEffect,\n        className\n      )}\n      {...props}\n    >\n      {/* Animated border gradient */}\n      <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-neon-blue via-electric-purple to-hologram-pink opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n      \n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n      \n      {/* Corner accent */}\n      <div className=\"absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-neon-blue/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n    </div>\n  );\n});\n\nCard.displayName = 'Card';\n\n// Card Header Component\nconst CardHeader = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('p-6 pb-4', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardHeader.displayName = 'CardHeader';\n\n// Card Title Component\nconst CardTitle = forwardRef(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-xl font-semibold text-text-primary mb-2',\n      'bg-gradient-to-r from-neon-blue to-electric-purple bg-clip-text text-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </h3>\n));\n\nCardTitle.displayName = 'CardTitle';\n\n// Card Description Component\nconst CardDescription = forwardRef(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-text-secondary leading-relaxed', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\n\nCardDescription.displayName = 'CardDescription';\n\n// Card Content Component\nconst CardContent = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('px-6 pb-6', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardContent.displayName = 'CardContent';\n\n// Card Footer Component\nconst CardFooter = forwardRef(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'px-6 py-4 border-t border-electric-purple/20',\n      'bg-gradient-to-r from-transparent via-electric-purple/5 to-transparent',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardFooter.displayName = 'CardFooter';\n\n// Export all components\nexport {\n  Card,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n  CardContent,\n  CardFooter\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACvB,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAC;;;;EAIpB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;IAGV,CAAC;QACD,OAAO,CAAC;;;;IAIR,CAAC;QACD,MAAM,CAAC;;;;IAIP,CAAC;QACD,QAAQ,CAAC;;;;IAIT,CAAC;QACD,UAAU,CAAC;;;;IAIX,CAAC;IACH;IAEA,MAAM,eAAe,QAAQ,CAAC;;;EAG9B,CAAC,GAAG;IAEJ,MAAM,aAAa,OAAO,CAAC;;;;;EAK3B,CAAC,GAAG;IAEJ,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,cACA,YACA;QAED,GAAG,KAAK;;0BAGT,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;;AAEA,KAAK,WAAW,GAAG;AAEnB,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG;AAEzB,uBAAuB;AACvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIL,UAAU,WAAW,GAAG;AAExB,6BAA6B;AAC7B,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACrE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;QACpD,GAAG,KAAK;kBAER;;;;;;;AAIL,gBAAgB,WAAW,GAAG;AAE9B,yBAAyB;AACzB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACjE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;kBAER;;;;;;;AAIL,YAAY,WAAW,GAAG;AAE1B,wBAAwB;AACxB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,0EACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d1-namecardai/src/app/pitch/page.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nexport default function PitchPage() {\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  const slides = [\n    {\n      title: \"The Problem\",\n      subtitle: \"Traditional Business Cards Are Dead\",\n      content: [\n        \"📄 90% of business cards are thrown away within a week\",\n        \"🌍 8 billion cards printed annually = massive environmental waste\",\n        \"📱 No digital integration or smart features\",\n        \"🤝 Limited networking capabilities and follow-up tracking\",\n        \"💼 Static information that quickly becomes outdated\"\n      ],\n      visual: \"📊\"\n    },\n    {\n      title: \"The Solution\",\n      subtitle: \"AI-Powered Digital Business Cards\",\n      content: [\n        \"🤖 AI personalities that represent you 24/7\",\n        \"🎯 Smart networking with context-aware recommendations\",\n        \"📱 Instant sharing via QR codes, NFC, or AR scanning\",\n        \"📈 Real-time analytics and engagement tracking\",\n        \"🌐 Multi-platform integration (LinkedIn, CRM, Calendar)\"\n      ],\n      visual: \"🚀\"\n    },\n    {\n      title: \"Market Opportunity\",\n      subtitle: \"$45B Digital Business Card Market\",\n      content: [\n        \"📈 Market growing at 11.2% CAGR through 2028\",\n        \"👥 500M+ professionals worldwide need better networking\",\n        \"💰 Average professional spends $200/year on networking tools\",\n        \"🏢 Enterprise market: $12B opportunity\",\n        \"🌍 Global remote work trend accelerating adoption\"\n      ],\n      visual: \"💰\"\n    },\n    {\n      title: \"Competitive Advantage\",\n      subtitle: \"First AI-Native Business Card Platform\",\n      content: [\n        \"🧠 Proprietary AI personality engine\",\n        \"🎭 Voice-activated interactions and conversations\",\n        \"👁️ AR/VR integration for immersive networking\",\n        \"⚡ Real-time content updates and smart suggestions\",\n        \"🔒 Enterprise-grade security and privacy\"\n      ],\n      visual: \"⚡\"\n    },\n    {\n      title: \"Business Model\",\n      subtitle: \"Scalable SaaS with Multiple Revenue Streams\",\n      content: [\n        \"💳 Freemium: $0 (basic) → $29/month (pro) → $99/month (enterprise)\",\n        \"🏢 Enterprise licenses: $10,000-$100,000 annually\",\n        \"🤝 Partnership revenue: 20% commission on integrations\",\n        \"📊 Analytics premium: $19/month for advanced insights\",\n        \"🎨 Custom design services: $500-$5,000 per project\"\n      ],\n      visual: \"💎\"\n    },\n    {\n      title: \"Traction\",\n      subtitle: \"Strong Early Adoption & Growth\",\n      content: [\n        \"👥 10,000+ beta users in first 3 months\",\n        \"📈 40% month-over-month user growth\",\n        \"💰 $50,000 MRR with 95% customer satisfaction\",\n        \"🏆 Winner of TechCrunch Disrupt Startup Battlefield\",\n        \"🤝 Partnerships with Microsoft, Salesforce, HubSpot\"\n      ],\n      visual: \"📈\"\n    },\n    {\n      title: \"Financial Projections\",\n      subtitle: \"Path to $100M ARR in 5 Years\",\n      content: [\n        \"Year 1: $1M ARR (10,000 users)\",\n        \"Year 2: $10M ARR (100,000 users)\",\n        \"Year 3: $30M ARR (250,000 users)\",\n        \"Year 4: $60M ARR (400,000 users)\",\n        \"Year 5: $100M ARR (500,000 users)\"\n      ],\n      visual: \"🎯\"\n    },\n    {\n      title: \"Funding Ask\",\n      subtitle: \"$5M Series A to Scale Globally\",\n      content: [\n        \"💻 Product Development (40%): AI engine, AR features\",\n        \"📢 Marketing & Sales (35%): Global expansion\",\n        \"👥 Team Expansion (20%): Engineering, AI specialists\",\n        \"🏢 Operations (5%): Infrastructure, compliance\",\n        \"🎯 Goal: 1M users, $50M ARR in 24 months\"\n      ],\n      visual: \"💰\"\n    }\n  ];\n\n  const nextSlide = () => {\n    setCurrentSlide((prev) => (prev + 1) % slides.length);\n  };\n\n  const prevSlide = () => {\n    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);\n  };\n\n  const goToSlide = (index) => {\n    setCurrentSlide(index);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-dark-space via-deep-purple to-midnight-blue\">\n      {/* Header */}\n      <div className=\"py-8 px-4\">\n        <div className=\"max-w-6xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"text-3xl\">🚀</div>\n            <div>\n              <h1 className=\"text-2xl font-bold text-text-primary\">NameCardAI</h1>\n              <p className=\"text-sm text-text-secondary\">Investor Pitch Deck</p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center gap-4\">\n            <div className=\"text-sm text-text-secondary\">\n              Slide {currentSlide + 1} of {slides.length}\n            </div>\n            <Button variant=\"outline\" size=\"sm\">\n              📧 Contact Us\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Slide */}\n      <div className=\"px-4 pb-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          <Card variant=\"glass\" className=\"min-h-[600px]\">\n            <CardContent className=\"p-12\">\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center h-full\">\n                {/* Content */}\n                <div className=\"space-y-8\">\n                  <div>\n                    <div className=\"text-6xl mb-4\">{slides[currentSlide].visual}</div>\n                    <h2 className=\"text-4xl font-bold text-text-primary mb-4\">\n                      {slides[currentSlide].title}\n                    </h2>\n                    <p className=\"text-xl text-electric-purple font-semibold\">\n                      {slides[currentSlide].subtitle}\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    {slides[currentSlide].content.map((item, index) => (\n                      <div\n                        key={index}\n                        className=\"flex items-start gap-3 p-4 bg-surface-dark/50 rounded-lg border border-electric-purple/20 hover:border-neon-blue/40 transition-colors\"\n                      >\n                        <div className=\"text-lg\">{item.split(' ')[0]}</div>\n                        <p className=\"text-text-primary\">{item.substring(item.indexOf(' ') + 1)}</p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Visual/Chart Area */}\n                <div className=\"flex items-center justify-center\">\n                  <div className=\"w-full h-96 bg-gradient-to-br from-neon-blue/20 to-electric-purple/20 rounded-2xl border border-electric-purple/30 flex items-center justify-center\">\n                    <div className=\"text-center\">\n                      <div className=\"text-8xl mb-4\">{slides[currentSlide].visual}</div>\n                      <p className=\"text-text-secondary\">Interactive Chart/Visual</p>\n                      <p className=\"text-sm text-text-secondary mt-2\">\n                        {slides[currentSlide].title}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"px-4 pb-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"flex items-center justify-between\">\n            {/* Previous Button */}\n            <Button\n              onClick={prevSlide}\n              variant=\"outline\"\n              className=\"flex items-center gap-2\"\n            >\n              ← Previous\n            </Button>\n\n            {/* Slide Indicators */}\n            <div className=\"flex gap-2\">\n              {slides.map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => goToSlide(index)}\n                  className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                    index === currentSlide\n                      ? 'bg-neon-blue scale-125'\n                      : 'bg-text-secondary/30 hover:bg-text-secondary/60'\n                  }`}\n                />\n              ))}\n            </div>\n\n            {/* Next Button */}\n            <Button\n              onClick={nextSlide}\n              variant=\"primary\"\n              className=\"flex items-center gap-2\"\n            >\n              Next →\n            </Button>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"mt-8 flex justify-center gap-4\">\n            <Button variant=\"primary\" size=\"lg\">\n              💰 Schedule Investor Meeting\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              📄 Download Full Deck\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              📊 View Financial Model\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"py-8 px-4 border-t border-electric-purple/20\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <p className=\"text-text-secondary text-sm\">\n            NameCardAI - Revolutionizing Professional Networking with AI\n          </p>\n          <p className=\"text-text-secondary text-xs mt-2\">\n            Confidential and Proprietary - For Investor Use Only\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,SAAS;QACb;YACE,OAAO;YACP,UAAU;YACV,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;KACD;IAED,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;IACtD;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;IACtE;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAW;;;;;;8CAC1B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;sCAI/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAA8B;wCACpC,eAAe;wCAAE;wCAAK,OAAO,MAAM;;;;;;;8CAE5C,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAQ1C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,kIAAA,CAAA,OAAI;wBAAC,SAAQ;wBAAQ,WAAU;kCAC9B,cAAA,6LAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAiB,MAAM,CAAC,aAAa,CAAC,MAAM;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,MAAM,CAAC,aAAa,CAAC,KAAK;;;;;;kEAE7B,6LAAC;wDAAE,WAAU;kEACV,MAAM,CAAC,aAAa,CAAC,QAAQ;;;;;;;;;;;;0DAIlC,6LAAC;gDAAI,WAAU;0DACZ,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvC,6LAAC;wDAEC,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;0EAAW,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;0EAC5C,6LAAC;gEAAE,WAAU;0EAAqB,KAAK,SAAS,CAAC,KAAK,OAAO,CAAC,OAAO;;;;;;;uDAJhE;;;;;;;;;;;;;;;;kDAWb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiB,MAAM,CAAC,aAAa,CAAC,MAAM;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,6LAAC;wDAAE,WAAU;kEACV,MAAM,CAAC,aAAa,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAY7C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAS;oCACT,SAAQ;oCACR,WAAU;8CACX;;;;;;8CAKD,6LAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;4CAEC,SAAS,IAAM,UAAU;4CACzB,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,2BACA,mDACJ;2CANG;;;;;;;;;;8CAYX,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAS;oCACT,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAQ1C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA8B;;;;;;sCAG3C,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;;AAO1D;GA7PwB;KAAA", "debugId": null}}]}